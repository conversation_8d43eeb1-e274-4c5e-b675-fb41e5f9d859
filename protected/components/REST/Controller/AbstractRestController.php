<?php

declare(strict_types=1);

namespace Components\REST\Controller;

\Yang::loadComponentNamespaces('Core');
\Yang::loadComponentNamespaces('Grid2Core');
\Yang::loadComponentNamespaces('HTTPCore');
\Yang::loadComponentNamespaces('JWT');


use Components\HTTPCore\Builder\RequestBuilder;
use Components\HTTPCore\Handler\SendResponseHandler;
use Components\JWT\Validator\JWTValidator;
use Components\REST\Handler\RestExceptionHandler;
use Psr\Http\Message\ServerRequestInterface;
use Fig\Http\Message;

abstract class AbstractRestController extends \Controller
{
    public const BEARER = "Bearer";
    protected ServerRequestInterface $request;

    /**
     * @throws \Exception
     */
    public function build(): void
    {
        $this->request = (new RequestBuilder())->build();
        try {
            (new JWTValidator())->validate($this->getBearerToken());
        } catch (\Exception $exception) {
            (new RestExceptionHandler())->handle($exception);
            $this->sendUnauthorizedResponse();
        } catch (\Throwable $exception) {
            (new RestExceptionHandler())->handle($exception);
            echo 'throw';
            $this->sendUnauthorizedResponse();
        }
    }

    private function getBearerToken(): string
    {
        $headers = $this->request->getServerParams()["HTTP_AUTHORIZATION"] ?? "";
        if (preg_match('/' . self::BEARER . '\s(\S+)/', $headers, $matches)) {
            return $matches[1] ?? "";
        }
        return "";
    }

    protected function sendUnauthorizedResponse(): void
    {
        http_response_code(Message\StatusCodeInterface::STATUS_UNAUTHORIZED);
        //(new SendResponseHandler())->handle([]);
        // echo json_encode(["message" => Dict::getValue("login_for_authentication")]);
        \Yii::app()->end();
    }

    protected function sendResponse(array $response): void
    {
        (new SendResponseHandler())->handle([]);
        \Yii::app()->end();
    }
}
<?php

declare(strict_types=1);

namespace Components\Cost\Provider;

final class EmployeeCostByEcAndValidIntervalProvider
{
    public function __invoke(string $validFrom, string $validTo, array $employeeContractIds)
    {
        $endDate = \App::getSetting('defaultEnd');
        
        $result =\Yii::app()->db->createCommand()
            ->select('cal.date, ec.employee_contract_id, ec.cost_id, ec.cost_center_id')
            ->from('calendar cal')
            ->join(
                'employee_cost ec', 
                "cal.date BETWEEN ec.valid_from AND IFNULL(ec.valid_to, :appEndDate) 
                AND ec.status = :published
                AND ec.employee_contract_id IN ('" . join("','", $employeeContractIds) . "')"
                , [':appEndDate' => $endDate, ':published' => \Status::PUBLISHED])
            ->where(
                'cal.date BETWEEN :validFrom AND :validTo',
                [':validFrom' => $validFrom, ':validTo' => $validTo])
            ->queryAll()
        ;

        if (!\is_array($result)) {
            $result = [];
        }
        
        $resultArray = [];
        foreach ($result as $value) {
            $resultArray[$value['date']][$value['employee_contract_id']] = [
                'cost_id' => $value['cost_id'],
                'cost_center_id' => $value['cost_center_id']
            ];
        }
        
        return $resultArray;
    }
}

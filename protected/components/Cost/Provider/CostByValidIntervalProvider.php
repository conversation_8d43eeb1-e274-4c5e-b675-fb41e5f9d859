<?php

declare(strict_types=1);

namespace Components\Cost\Provider;

final class CostByValidIntervalProvider
{
    public function __invoke(string $validFrom, string $validTo)
    {
        $endDate = \App::getSetting('defaultEnd');

        $criterias = new \CDbCriteria();
        $criterias->select = 't.cost_id, t.cost_name';
        $criterias->alias = 't';
        $criterias->condition = 
                        't.status = :published
						AND t.valid_from <= :validTo
						AND IFNULL(t.valid_to, :appEndDate) >= :validFrom';
        $criterias->params = [
            ':validFrom' => $validFrom,
            ':validTo' => $validTo,
            ':appEndDate' => $endDate,
            ':published' => \Status::PUBLISHED
        ];

        $result = \Cost::model()->findAll($criterias);
        if (!\is_array($result)) {
            $result = [];
        }

        $resultArray = [];
        foreach ($result as $object) {
            $resultArray[$object->cost_id] = $object->cost_name;
        }

        return $resultArray;
    }
}

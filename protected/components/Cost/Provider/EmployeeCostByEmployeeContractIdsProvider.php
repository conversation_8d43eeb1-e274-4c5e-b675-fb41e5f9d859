<?php

declare(strict_types=1);

namespace Components\Cost\Provider;

final class EmployeeCostByEmployeeContractIdsProvider
{
    public function __invoke(array $employeeContractIds)
    {
        $criteria = new \CDbCriteria();
        $criteria->condition = '`status` = ' . \Status::PUBLISHED . "
            AND `employee_contract_id` IN ('" . implode("', '", $employeeContractIds) . "')
            ";
        $employeeCost = \EmployeeCost::model()->findAll($criteria);
        if (!\is_array($employeeCost)) {
            $employeeCost = [];
        }

        return $employeeCost;
    }
}

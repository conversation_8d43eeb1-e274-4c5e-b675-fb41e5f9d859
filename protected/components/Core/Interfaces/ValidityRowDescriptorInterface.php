<?php

declare(strict_types=1);

namespace Components\Core\Interfaces;

interface ValidityRowDescriptorInterface
{
    public const DEFAULT_VALID_FROM = '1915-01-01';
    public const DEFAULT_VALID_TO = '2038-01-01';
    public function getValidFrom(): null|\DateTime;
    public function getValidTo(): null|\Datetime;
    public function setValidFrom(string|null $validFrom);
    public function setValidTo(string|null $validTo);
    public function setValidityRowArray(array $historicityRow);
    public function getValidityRowArrayKeys(): array;
    public function getValidityRow() : array;
}

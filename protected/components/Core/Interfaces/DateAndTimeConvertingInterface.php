<?php

declare(strict_types=1);

namespace Components\Core\Interfaces;

interface DateAndTimeConvertingInterface
{
    public function convertSecToHourMinute(int $sec): string;

    public function convertSecToMinuteSec(int $sec): string;

    public function convertSecToMinute(int $sec): string;

    public function convertStringToDateTime(string $dayTime, string $format = "Y-m-d H:i:s"): \DateTime;

    public function convertSecToDateTimeInterval(int $sec): \DateInterval;

    public function convertDateIntervalToHourMinute(\DateInterval $interval): string;

    public function convertDateIntervalToSec(\DateInterval $interval): int;

    public function convertDayAndTimeToDateTime(string $day, string $time): \DateTime;

    public function convertTimeStringToInt(string $time, string $format = "H:i"): int;

    public function convertTimeStringToDateInterval(string $time): \DateInterval;

    public function convertSecToHourMinuteText(int $sec): string;

    public function convertSecToHour(int $sec): int;

    public function convertHourToSec(int $hour): int;

    public function convertDateTimeToString(\DateTime $dateTime, string $format = "Y-m-d H:i:s"): string;

    public function convertStringToDate(string $dayTime, string $format = "Y-m-d"): \DateTime;

    public function convertDateToString(\DateTime $dateTime): string;
}

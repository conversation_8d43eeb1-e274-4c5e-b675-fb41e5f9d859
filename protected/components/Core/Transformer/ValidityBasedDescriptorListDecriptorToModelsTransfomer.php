<?php

declare(strict_types=1);

namespace Components\Core\Transformer;

use Components\Core\Descriptor\ValidityBasedDescriptorListDescriptor;
use Components\Core\Descriptor\ValidityBasedModelDescriptor;

final class ValidityBasedDescriptorListDecriptorToModelsTransfomer
{
    public function transform(array $listDescriptors): array
    {
        /** @var ValidityBasedModelDescriptor[] $descriptors */
        $descriptors = array_merge(
            ...array_values(
                array_map(static function (ValidityBasedDescriptorListDescriptor $item) {
                    return $item->getAll();
                }, $listDescriptors)
            )
        );
        return array_map(static function (ValidityBasedModelDescriptor $descriptor) {
            return $descriptor->getModel();
        }, $descriptors);
    }
}

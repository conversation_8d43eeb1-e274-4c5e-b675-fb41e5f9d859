<?php

declare(strict_types=1);

namespace Components\Core\Transformer;

use Components\Core\Interfaces\ValidityRowDescriptorSortingToArrayTransformerInterface;
use Components\Core\Enum\ValidityRowDescriptorSortingToArrayEnum;
final class ValidityRowDescriptorSortingToArrayTransformer implements ValidityRowDescriptorSortingToArrayTransformerInterface
{

    private string|null $nullOrEmpty;

    
    public function __construct()
    {
        $this->nullOrEmpty = null;   
    }
    public function transform(array $descriptors): array
    {
        $validityArray = [];
        $columns = $this->setColumns($descriptors);
        $minValidFrom = $this->getMinValidFrom($descriptors);
        if ($minValidFrom === null) {
            return $validityArray;
        }

        $rows = $this->setRowsFromDescriptors($descriptors, $columns, $minValidFrom); 
        $rows = $this->setRowsMultiSort($rows);

        $j = 0;
        $validityArray[$j] = $rows[0];
        for($i = 1; $i < count($rows); $i++)
        {
            $dateDiff = $validityArray[$j][ValidityRowDescriptorSortingToArrayEnum::VALID_FROM]->diff($rows[$i][ValidityRowDescriptorSortingToArrayEnum::VALID_FROM]);
            if ((int)$dateDiff->format('%a') > 0)
            {
                ++$j;
                $validityArray[$j] = $validityArray[$j-1];
                $validityArray[$j][ValidityRowDescriptorSortingToArrayEnum::VALID_FROM] = clone $rows[$i][ValidityRowDescriptorSortingToArrayEnum::VALID_FROM];
                $validityArray[$j][ValidityRowDescriptorSortingToArrayEnum::VALID_TO] = clone $rows[$i][ValidityRowDescriptorSortingToArrayEnum::VALID_TO];
                $validityArray[$j-1][ValidityRowDescriptorSortingToArrayEnum::VALID_TO] = clone $rows[$i][ValidityRowDescriptorSortingToArrayEnum::VALID_FROM];
                $validityArray[$j-1][ValidityRowDescriptorSortingToArrayEnum::VALID_TO]->modify("-1 day");
                if (isset($validityArray[$j][ValidityRowDescriptorSortingToArrayEnum::ROW_ID])) {
                    $validityArray[$j][ValidityRowDescriptorSortingToArrayEnum::ROW_ID] = null;
                }
            }

            foreach ($columns as $column) {
                if ($rows[$i][$column] != $this->nullOrEmpty) {
                    $validityArray[$j][$column] = $rows[$i][$column];
                }
            }
        }
        $validityArray = $this->mergingRows($validityArray);
        return $validityArray;
    }

    public function setNullOrEmpty(string|null $nullOrEmpty)
    {
        $this->nullOrEmpty = $nullOrEmpty;
    }

    private function setColumns(array $descriptors): array
    {
        $columns = [];
        foreach ($descriptors as $descriptor)
        {
            array_push($columns, ...$descriptor->getValidityRowArrayKeys());
        }
        $columns = array_unique($columns);
        return $columns;
    }

    private function setRowsFromDescriptors(array $descriptors, array $columns, \Datetime $minValidFrom) : array
    {
        $rows = [];
        foreach($descriptors as $descriptor)
        {
            $validFrom = $descriptor->has(ValidityRowDescriptorSortingToArrayEnum::VALIDITY_ROW_DESCRIPTOR_VALID_FROM) ? $descriptor->getValidFrom() : $minValidFrom;
            $row = [
                    ValidityRowDescriptorSortingToArrayEnum::VALID_FROM => $validFrom,
                    ValidityRowDescriptorSortingToArrayEnum::VALID_TO => $descriptor->getValidTo()
                ];
            
            $descriptorArray = $descriptor->getValidityRow();
            foreach ($columns as $column) {
                $rowData = [$column => $descriptorArray[$column] ?? $this->nullOrEmpty];
                $row = array_merge($row, $rowData);
            }
            $rows[] = $row;
        }
        return $rows;
    }

    private function setRowsMultiSort(array $rows): array
    {
        $validFromColumn = array_column($rows, ValidityRowDescriptorSortingToArrayEnum::VALID_FROM);
        $validFromTo = array_column($rows, ValidityRowDescriptorSortingToArrayEnum::VALID_TO);
        $rowId = array_column($rows, ValidityRowDescriptorSortingToArrayEnum::ROW_ID);
        if (empty($rowId)) {
            array_multisort($validFromColumn, SORT_ASC, $validFromTo, SORT_ASC, $rows);
        }
        else {
            array_multisort($validFromColumn, SORT_ASC, $validFromTo, SORT_ASC, $rowId, SORT_DESC, $rows);
        }
        return $rows;
    }

    private function getMinValidFrom(array $descriptors): \DateTime|null
    {
        $rowsValidFrom = [];
        foreach ($descriptors as $descriptor)
        {
            if (!is_null($descriptor->getValidFrom())) {
                $rowsValidFrom[] = $descriptor->getValidFrom();
            }
        }
        if (empty($rowsValidFrom)) {
            return null;
        }
        $minValidFrom = min($rowsValidFrom);
        return $minValidFrom;
    }


    private function mergingRows(array $validityArray)
    {
        if (count($validityArray) < 2) {
            return $validityArray;
        }

        $aktualRow = 0;
        $aktualRowHash = $this->arrayValuesToHash($validityArray[0]);
        $validityArrayCount = count($validityArray);
        for($row = 1; $row < $validityArrayCount; $row++) {
            $rowHash = $this->arrayValuesToHash($validityArray[$row]);
            if ($rowHash != $aktualRowHash)
            {
                $aktualRow = $row;
                $aktualRowHash = $rowHash;
                continue;
            }
            $validityArray[$aktualRow][ValidityRowDescriptorSortingToArrayEnum::VALID_TO] = $validityArray[$row][ValidityRowDescriptorSortingToArrayEnum::VALID_TO];
            if (!isset($validityArray[$row][ValidityRowDescriptorSortingToArrayEnum::ROW_ID]))
            {
                unset($validityArray[$row]);
                continue;
            }

            if (is_null($validityArray[$row][ValidityRowDescriptorSortingToArrayEnum::ROW_ID])) {
                unset($validityArray[$row]);
            }
            else {
                $validityArray[$row][ValidityRowDescriptorSortingToArrayEnum::STATUS] = ValidityRowDescriptorSortingToArrayEnum::STATUS_INVALID;
            }
        }

        return $validityArray;
    }

    private function arrayValuesToHash(array $arrayValues)
    {
        $exceptionKeys = [ValidityRowDescriptorSortingToArrayEnum::VALID_FROM, ValidityRowDescriptorSortingToArrayEnum::VALID_TO, ValidityRowDescriptorSortingToArrayEnum::ROW_ID];
        $exceptionEcValidKeys = [ValidityRowDescriptorSortingToArrayEnum::EC_VALID_FROM, ValidityRowDescriptorSortingToArrayEnum::EC_VALID_TO];
        $values = '';
        foreach($arrayValues as $arrayKey => $arrayValue)
        {
            if (!in_array($arrayKey, $exceptionKeys)) {
                $arrayValue = in_array($arrayKey, $exceptionEcValidKeys) && !is_null($arrayValue) ? (string)$arrayValue->format("Y-m-d") : $arrayValue;
                $values .= $arrayValue;
            }
        }
        return md5($values);
    }
}

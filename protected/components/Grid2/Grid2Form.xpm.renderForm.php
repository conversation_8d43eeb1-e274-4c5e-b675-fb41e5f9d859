<?php

'yii2-only`;

	namespace app\components\Grid2;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\components\Grid2\Grid2Form;
	use app\components\Helpers\Miner;
	use Yang;

`/yii2-only';


//	Holtartás	#see 20191130-214406

"Experimental new version of Grid2Form::renderForm()";{

	"Old notes";{

		#see n-7hq1q2bp //	Az eredeti HTML forrás (viszont a prettifier szar volt)
		#see n-7hs71vzg //	Itt van a teljes HTML anyag (itt már korrekt a formázás, nade mik vannak itt tejóisten)
		#see n-7hq1y3pz //	Így nézett ki eredetileg
		#see n-7hqd611r //	Egyelőre kivettük a Minert
		#see n-7j90vc4p //	Kombinált filtering még nem genki (✔ de azóta már igen)

	}

	"Fetch rows for matrix building";{

		$vFrom = $_SESSION["summarySheet_filters"]["valid_from"];
		$vTill = $_SESSION["summarySheet_filters"]["valid_to"];
		$allEmployeesInRange = Miner::get("summarysheet, searchform dropdowns",$vFrom,$vTill);

	}

	"Build filter matrix";{

		$collectValuesOf = function($targetField,$callerField) use ($allEmployeesInRange) {
			$a = $allEmployeesInRange;
			$m = [];
			foreach($a as $r) { $c=$r[$callerField];$t=$r[$targetField];$m[$c][$t]=1;} #see n-7h35rx49
			foreach($m as $i=>$x) {$m[$i] = join("\t",array_keys($x));}
			return $m;
		};

		$allOptions		= [];
		$filterMatrix	= [];
		$influencedBy	= [];
		$influenceMap	= parseAsKeyArray('

			company_id				unit_id, workgroup_id, payroll_id
			payroll_id				unit_id, workgroup_id
			workgroup_id			unit_id

		');

		$influencedBy; #see n-7h2npfn3
		$influenceMap; #see n-7h2n060d
		$filterMatrix; #see n-7hxg73y5

		foreach($influenceMap as $caller=>$targetList) {
			$filterMatrix[$caller] = [];
			foreach($targetList as $targetField) {
				$influencedBy[$targetField][] = $caller;
				$filterMatrix[$caller][$targetField] = $collectValuesOf($targetField,$caller);
			}
		}

	}

	"Get list of input controls to display";{

		switch($mode) {

			case Grid2Controller::G2BC_FORM_MODE_SEARCHBAR:
				$properties = $this->search();
				$inputPrefix = "searchInput";
			break;

			case Grid2Controller::G2BC_FORM_MODE_DIALOG:
				$properties = $this->columns(!!$dialogColumns);
				if ($this->hasMultiGridMode() && !empty($generateFrom)) {
					$properties = valueOrNull($properties[$generateFrom]) ?: [];
				}
				$inputPrefix = "dialogInput_$generateFrom";
				$modelName = $this->LAGridDB->getModelName($generateFrom);
			break;

		}
		$properties; #see n-7hqz4rn7

	}

	"Some local helpers";{

		$optionList = function($sourceArray,$defaultKey=null) {
			$h = "";
			foreach($sourceArray as $key=>$caption) {
				$d = "";if($key===$defaultKey) if(!is_null($defaultKey)) $d = " selected";
				$h.="<option value=\"$key\">$caption</option>\n";
			}
			return $h;
		};

		$nonzeroFilter = function($items,$field) use($allEmployeesInRange) {
			$useThis = useExperimental("summary sheet: search form only shows nonzero filters");
			if(!$useThis) return $items;
			$keys = [];foreach($allEmployeesInRange as $e) $keys[$e[$field."_id"]]=1;
			$rows = [];
			if($items["ALL"]) $rows["ALL"]=$items["ALL"];
			foreach($items as $k=>$v) if($keys[$k]) $rows[$k]=$v;
			return $rows;
		};

	}


	"Generate input/select controls as HTML";{

		ob_start();

		if(!$vFrom) {
			$today = date("Y-m");
			$vFrom = "$today-01";
			$vTill = date("Y-m-d",strtotime("$vFrom + 1 month - 1 day"));
		}

		$dateVariables = [
			"{valid_from}"	=> $vFrom,
			"{valid_to}"	=> $vTill
		];

		foreach ($properties as $column => $attrs) {

			$field = $column;
			$ftype = $attrs["col_type"];
			$label = $attrs["label_text"];
			$query = $attrs["options"]["sql"];
			$query = strtr($query,$dateVariables);
			$prefx = $inputPrefix;
			$ident = sprintf('id="%s_%s" name="%s[%s]"'				,$prefx,$field,$prefx,$field);
			$aidnt = sprintf('id="%s_auto_%s" name="%s_auto[%s]"'	,$prefx,$field,$prefx,$field);
			$clear = Dict::getValue("delete_filter");
			$multi = ($attrs["multiple"]);

			switch($ftype) {
				case "ed":
					?>
					<div class="switchable switched-off">
						<label><?=$label?></label>
						<input type="text" name="<?=$prefx?>[<?=$field?>]" autocomplete="off" class="to-serialize">
					</div>
					<?php
				break;
				case "auto":
					?>
					<label><?=$label?></label>
					<div>
						<div class="g2b form-autocomplete-container">
							<input autocomplete="off" class="autocomplete" type="text" <?=$aidnt?>>
							<input type="hidden" <?=$ident?> autocomplete="off" class="to-serialize">
							<a class="g2b form-autocomplete-delete-icon" href="#">
								<img alt="<?=$clear?>" src="/images/input/input_x.png" title="<?=$clear?>"/>
							</a>
						</div>
					</div>
					<?php
				break;
				case "combo":

					$showAll = ["ALL"=>Dict::getValue("all")];
					$items = dbFetchAll($query,"id","value");
					$items = array_merge($showAll,$items);
					$items = $nonzeroFilter($items,$field);
					$hList = $optionList($items);
					foreach($items as $k=>$v) $allOptions[$field][$k]=$v;
					?>
					<label><?=$label?></label>
					<select
						<?=$multi?"multiple":""?>
						<?=$ident?>
						onchange="this.dataset.current = this.value;"
						data-current="ALL"
						data-dbfield="<?=$field?>"
						class="g2b styled-select yellow-if-active to-serialize"
						><?=$hList?>
					</select>
					<?php
				break;
			}

		}

		$months = [];
		$cursor = strtotime(date("Y-m-15"));
		for($i=0;$i<25;++$i) {
			$maxDay = date("t",$cursor);
			$yrmon = date("Y-m",$cursor);
			$range = "$yrmon-01, $yrmon-$maxDay";
			$mLang = strtolower(date("F",$cursor));
			$mLang = Dict::getValue($mLang);
			$value = date("Y. ",$cursor).$mLang;
			$months[$range] = $value;
			$cursor-= 86400 * 30;
		}

		$optMonths = $optionList($months,date("n"));
		$lngFullMonth = "Teljes hónap";																					#changeThis #languageSpecific

		$htmlFormControls = ob_get_contents(); // igen, tudom, hogy van end_clean is, #see n-7h2a6kwm

		ob_end_clean();

	}

	"Generate main HTML";{

		ob_start();

		"Styles";{
			?>
			<style>

				select.g2b.styled-select {margin-bottom:11px;}
				select.g2b.styled-select.yellow-if-active:not([data-current='ALL']) {background-color:yellow}

				span.select2-selection {border-color:#e5e5e5 !important;border-radius:6px !important;margin-bottom:5px;}
				span.select2-selection__arrow {background:#29b6f6;padding-left:13px;padding-right:13px;border-radius:0px 5px 5px 0px;}
				.select2-container--default.select2-container       .select2-selection--single .select2-selection__arrow b {border-top-color:white;transform:scale(1.18);}
				.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {border-bottom-color:white;}

				label input[type=checkbox] {width:auto;float:left;margin-right:7px;width:16px;height:16px;}
				.switchable {transition:all 200ms ease;transform-origin:center top;}
				.switchable.switched-off {transform:scaleY(0);height:0;}
				.switchable:not(.switched-off) {transform:scaleY(1);}

			</style>
			<?php
		}

		"HTML part";{
			?>
			<label><input type="checkbox" checked onclick="switchFullMonth(this)"><?=$lngFullMonth?></label>
			<div class="clearfix"></div>
			<div class="switchable">
				<select
					class="g2b styled-select"
					name="fullmonth" id="fullmonth"
					onclick="setDatesByFullMonth(this)"
					><?=$optMonths?>
				</select>
			</div>
			<br>
			<div> <?=$htmlFormControls?> </div>
			<div class="g2b form-item-container">
				<div></div>
				<div>
					<input class="searchButton" name="yt0" onclick="
						showLoader(true);
						laGrid_mainGrid.enableGridForceLoad('');
						laGrid_mainGrid.init();
						toggleOffCanvasSearchPanel();
					" style="width:75px;height:30px;" type="button">
					</input>
				</div>
			</div>
			<?php
		}

		"Scripts";{

			$validFrom = $vFrom;
			$validTill = $vTill;
			$followOnConsole = useExperimental("summary sheet: follow filter logic on dev console");
			$langAll = Dict::getValue("all");

			?>
			<script>

				"Get data from PHP";{

					<?php $xferToJavascript = '

						filterMatrix
						influenceMap
						influencedBy
						validFrom
						validTill
						allOptions
						followOnConsole
						langAll

					';?>
					var add = function(p,d) {d=d||{};for(var i in p) d[i]=p[i];return d;};
					window.phpdata = add(<?=json(compact(linesOf($xferToJavascript)))?>,window.phpdata);

				}

				"Helpers";{

					function switchFullMonth(o) {
						var calFields = $("input[name*='valid_']"     ).closest("div");
						var fullMonth = $("select[name*='fullmonth']" ).closest("div");
						var chk = $(o).is(':checked') ?1:0;
						var off = 'switched-off';
						if(chk==1) {setFullMonthByDates();}
						if(chk==1) {calFields.addClass(off);fullMonth.removeClass(off);}
						if(chk==0) {fullMonth.addClass(off);calFields.removeClass(off);}
					}

					function setFullMonthByDates() {
						var yrmon = $("input[name*='valid_from']").val().substr(0,7);
						var values = $("#fullmonth option");
						var active = values.filter("[value^='"+yrmon+"']");
						var selectedValue = active.val();
						if(selectedValue) $("#fullmonth").val(selectedValue);
					}

					function setDatesByFullMonth() {
						var v = $("#fullmonth").val();
						var days = v.split(", ");
						$("[name*='valid_from']").val(days[0]);
						$("[name*='valid_to']"  ).val(days[1]);
					}

					function setSessionValues() {
						var d1 = $("[name*='valid_from']").val();
						var d2 = $("[name*='valid_to']"  ).val();
						$.getJSON("/jsession.php",{
							"write":{
								"summarySheet_filters":{
									"valid_from" : d1,
									"valid_to"   : d2,
								}
							}
						},function(d) {
							console.log("Date fields initialized elvileg");
						});

					}

					function checkSessionKey(key) {
						$.getJSON("/jsession.php",{
							"read":[key]
						},function(d) {
							console.log(JSON.stringify(d,0,4))
						});
					}

					function allOptionsFor(field) {
						return phpdata.allOptions[field];
					}

					function optionsToHtml(optionList) {
						var out='<option value="ALL">'+phpdata.langAll+'</option>';
						for(var key in optionList) {
							var caption = optionList[key];
							out+='<option value="'+key+'">'+caption+'</option>\n';
						}
						return out;
					}

					function limitDropdown(field,theseKeysOnly,optionsSoFar) {

						console.log("limitDropdown",field,theseKeysOnly);

						var name = field.replace(/_id$/,"");
						var full = (theseKeysOnly == "*");
						var opts = optionsSoFar || allOptionsFor(name);
						var keys = {};for(var i in theseKeysOnly) {var k=theseKeysOnly[i];keys[k]=1;}
						var good = {};for(var i in opts) if((keys[i])||full) good[i]=opts[i];
						var $sel = $("select[data-dbfield='"+name+"']");
						var cval = $sel.val();
						var opts = optionsToHtml(good);
						var lost = ($sel.find("option[value='"+cval+"']").size()==0); // már nincs meg az, ami aktív volt

						$sel.html(opts);
						$sel.val(lost?"ALL":cval);

						return good;

					}

					function possibleOptionsFor(thisField) {

						var matrix = phpdata.filterMatrix;
						var fieldsToCheck = phpdata.influencedBy[thisField]; // erre a mezőre kik hatnak
						var collectValues = {};
						var possibleValues = {};
						var optionsRemaining = allOptionsFor(thisField);
						for(var i in fieldsToCheck) {

							var callerField = fieldsToCheck[i];
							var callerIdent = "select[data-dbfield='"+callerField.replace(/_id$/,"")+"']";
							var callerValue = $(callerIdent).val();
							if(callerValue!="ALL") {
								var possibleValues = matrix[callerField][thisField][callerValue] || "";
							var possibleValues = possibleValues.split("\t");
							}else{
								var possibleValues = "*";
							}
							optionsRemaining = limitDropdown(thisField,possibleValues,optionsRemaining);

						}

					}

				}


				"Initialize date fields";{

					$("input[name*='valid_']").datepicker({dateFormat:"yy-mm-dd"});
					$("input[name*='valid_from']").val(phpdata.validFrom);
					$("input[name*='valid_to'  ]").val(phpdata.validTill);
					setFullMonthByDates();
					setSessionValues();

				}

				"Initialize field interaction logic";{
					$("select.to-serialize").select2();
					$("select.to-serialize").on("change",function() {

						var name = $(this).prop("name").replace(/.*\[/,"").replace(/\]$/,"");
						var fieldName = name+"_id";
						var affectedFields = phpdata.influenceMap[fieldName];

						for(var i in affectedFields) {
							var targetField = affectedFields[i];
							possibleOptionsFor(targetField);
						}


						if(phpdata.followOnConsole) {

							console.clear();
							var titleCSS = "background:#026899;color:white";
							var fields = ['payroll','workgroup','unit'];
							var x,spacePad = (x="            ")+x+x+x+x+x+x+x; // 96 spaces :)
							for(var i in fields) {
								var name = fields[i];
								var text = "";
								var title = "   Valid options for "+name+":"+spacePad;
								var title = title.substr(0,80);
								console.log("%c"+title,titleCSS);
								var a = document.querySelectorAll("select[data-dbfield*='"+name+"'] > option");
								for(j=0;j<a.length;++j) {
									var capt = a[j].innerText;
									var code = a[j].value;  code=(spacePad+code).substr(-33);
									text = text+"    "+code+" | "+capt+"\n";
								}
								console.log(text+"\n");
							}

						}

					});
				}

				openOffCanvasSearchPanel();

			</script>
			<?php
		}

		$htmlWholeForm = ob_get_contents();

		ob_end_clean();

	}


	return $htmlWholeForm;

}

<?php

trait Grid2CheckRights
{

    protected function composeGargSQLPieces($options) {

        if(!is_array($options)) throw new Exception('A composeGargSQLPieces paramétere egy tömb kéne legyen.');

        $approverGroup      = $options["approver group"] ?? null;
        $approverPID        = $options["approver process id"] ?? null;
        $userID             = $options["user id"] ?? false;
        $dateField          = $options["date field"] ?? false;
        $addActiveJoins     = $options["add active joins"] ?? false;
        $excludeJoins       = $options["exclude joins"] ?? null;
        $emAlias            = $options["alias for employee"] ?? "employee";
        $ecAlias            = $options["alias for employee_contract"] ?? "employee_contract";
        $vFrom              = $options["valid from"] ?? false;
        $vTo                = $options["valid to"] ?? false;

        if(!property_exists($this,"useApprover")) throw new Exception('Nincs $useApprover property. Jól jönne az.');

        $out = [
            "left joins" => "",
            "conditions" => "1",
        ];

        if(!$this->useApprover) return $out;

        $art = new ApproverRelatedGroup;
        $gargSQL = $art->getApproverReleatedGroupSQL($approverGroup,$approverPID,$userID,$dateField,"AND","CurrentDate",$this->getControllerID());

        $gargWhere  = $gargSQL["where"] ?? null;
        $gargJoin   = ""; // ennek a gargSQL-ből kéne jönnie, de az ott nem működik, úgyhogy mindig üres

        $gargWhere = trim($gargWhere);
        $gargWhere = preg_replace("/^(AND|OR)\s/","",$gargWhere);


        if($addActiveJoins) {

            $possibleActiveGroups = linesOf('

                unit_id
                workgroup_id
                company_org_group1_id
                company_org_group2_id
                company_org_group3_id

            ');

            foreach($possibleActiveGroups as $g) {
                if(oneof($g,$excludeJoins)) continue;
                if(!EmployeeGroupConfig::isActiveGroup($g)) continue;
                if( $dateField)                     $gargJoin .= EmployeeGroup::getLeftJoinSQLWithoutCal($g,$ecAlias,$dateField);
                if(!$dateField && $vFrom && $vTo)   $gargJoin .= EmployeeGroup::getLeftJoinSQLWithoutCal($g,$ecAlias,"","",$emAlias,$vFrom,$vTo);
                if(!$dateField && !$vFrom && !$vTo) $gargJoin .= EmployeeGroup::getLeftJoinSQLWithoutCalAndate($g,$ecAlias);
            }

        }

        $defaultEnd     = App::getSetting("defaultEnd");
        $stPublished    = Status::PUBLISHED;
        $egUnit         = EmployeeGroup::getActiveGroupSQL("unit_id"                ,$emAlias);
        $egWorkgroup    = EmployeeGroup::getActiveGroupSQL("workgroup_id"           ,$ecAlias);
        $egGroup1       = EmployeeGroup::getActiveGroupSQL("company_org_group1_id"  ,$emAlias);
        $egGroup2       = EmployeeGroup::getActiveGroupSQL("company_org_group2_id"  ,$emAlias);
        $egGroup3       = EmployeeGroup::getActiveGroupSQL("company_org_group3_id"  ,$emAlias);

        $gargJoinPieces = [

            "unit" => "
                LEFT JOIN unit ON 1
                    AND unit.unit_id = $egUnit
                    AND unit.status = $stPublished
                    AND $emAlias.valid_from <= IFNULL(unit.valid_to, '$defaultEnd')
                    AND $emAlias.valid_to   >= unit.valid_from
                ",

            "workgroup" => "
                LEFT JOIN workgroup ON 1
                    AND workgroup.workgroup_id = $egWorkgroup
                    AND workgroup.status = $stPublished
                    AND $ecAlias.valid_from <= IFNULL(workgroup.valid_to, '$defaultEnd')
                    AND $ecAlias.valid_to   >= workgroup.valid_from",

            "company_org_group1" => "
                LEFT JOIN company_org_group1 ON 1
                    AND company_org_group1.company_org_group_id = $egGroup1
                    AND company_org_group1.status = $stPublished
                    AND $emAlias.valid_from <= IFNULL(company_org_group1.valid_to, '$defaultEnd')
                    AND $emAlias.valid_to   >= company_org_group1.valid_from
                ",

            "company_org_group2" => "
                LEFT JOIN company_org_group2 ON 1
                    AND company_org_group2.company_org_group_id = $egGroup2
                    AND company_org_group2.status = $stPublished
                    AND $emAlias.valid_from <= IFNULL(company_org_group2.valid_to, '$defaultEnd')
                    AND $emAlias.valid_to   >= company_org_group2.valid_from
                ",

            "company_org_group3" => "
                LEFT JOIN company_org_group3 ON 1
                    AND company_org_group3.company_org_group_id = $egGroup3
                    AND company_org_group3.status = $stPublished
                    AND $emAlias.valid_from <= IFNULL(company_org_group3.valid_to, '$defaultEnd')
                    AND $emAlias.valid_to   >= company_org_group3.valid_from
                ",

        ];

        foreach($gargJoinPieces as $table=>$sqlPiece) {
            if(!oneof($table,$excludeJoins)) continue;
            unset($gargJoinPieces[$table]);
        }

        $gargJoin.= join("\n\n",$gargJoinPieces);

        $out["left joins"] = $gargJoin;
        $out["conditions"] = $gargWhere;

        return $out;

    }
}
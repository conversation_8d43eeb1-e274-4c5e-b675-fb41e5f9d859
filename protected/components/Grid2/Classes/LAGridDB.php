<?php
class LAGridDB extends LAGridBase
{
	private $mode = [];
	private $connection = false;

	private $modelName = [];
	private $primaryKey = false;
	private $primaryKeyString = "";
	private $primaryKeyArray = [];

	private $enableSplitRowID = false;
	private $rowIDDelimiter = "_";
	private $splitRowIDStructure = [];

	private $modelSelectionModel, $modelSelectionCriteria = [];
	private $SQLSelection = null;
	private $SQLSelectionForReport = null;
	private $onBeforeQuerySQL = null;
	private $hasOnBeforeQuerySQL = false;

	/**
	 * Connection management
	 */
	public function setConnection($connection): self {
		$this->connection = $connection;
        return $this;
	}

	public function getConnection() {
		if ($this->connection) {
			return $this->connection;
		}

		return Yii::app()->db; // #see n-7wpd5adl
	}

	/**
	 * MODEL / SQL switch
	 */
	public function enableSQLMode($gridID = 'dhtmlxGrid') {
		$this->mode[$gridID] = Grid2Controller::G2BC_QUERY_MODE_SQL;
	}

	public function enableArrMode($gridID = 'dhtmlxGrid') {
		$this->mode[$gridID] = Grid2Controller::G2BC_QUERY_MODE_ARRAY;
	}

	public function getMode($gridID = 'dhtmlxGrid') {
		if (isset($this->mode[$gridID])) {
			return $this->mode[$gridID];
		} else {
			return Grid2Controller::G2BC_QUERY_MODE_MODEL;
		}
	}

	/**
	 * row_id management
	 */
	public function enableSplitRowID($delimiter = "_") {
		$this->rowIDDelimiter = $delimiter;

		$this->enableSplitRowID = true;
	}

	public function isEnabledSplitRowID() {
		return $this->enableSplitRowID;
	}

	public function getRowIDDelimiter() {
		if ($this->isEnabledSplitRowID()) {
			return $this->rowIDDelimiter;
		}

		return false;
	}

	/**
	 * When enableSplitRowID is true, defines the structure of it
	 */
	public function setSplitRowIDStructure($splitRowIDStructure = []) {
		$this->splitRowIDStructure = $splitRowIDStructure;
	}

	public function getSplitRowIDStructure() {
		return $this->splitRowIDStructure;
	}

	public function hasSplitRowIDStructure() {
		return count($this->splitRowIDStructure) > 0;
	}

	/**
	 * Model management
	 */
	public function setModelName($modelName, $gridID = null) {
		if ($this->hasMultiGridMode() && !empty($gridID)) {
			$this->modelName[$gridID] = $modelName;
		} else {
			$this->modelName["dhtmlxGrid"] = $modelName;
		}
	}

	public function getModelName($gridID = null) {
		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($this->modelName[$gridID]) && !empty($this->modelName[$gridID])) {
				return $this->modelName[$gridID];
			}

			if (isset($this->modelName["dhtmlxGrid"]) && !empty($this->modelName["dhtmlxGrid"])) {
				return $this->modelName["dhtmlxGrid"];
			}
		} else {
			if (isset($this->modelName["dhtmlxGrid"]) && !empty($this->modelName["dhtmlxGrid"])) {
				return $this->modelName["dhtmlxGrid"];
			}
		}

		return null;
	}

	/**
	 * Primary Key management
	 */
	public function setPrimaryKey($primaryKey, $gridID = null) {
		if (!empty($primaryKey)) {
			$this->primaryKey = true;
			if ($this->hasMultiGridMode() && !empty($gridID)) {
				$this->primaryKeyArray[$gridID] = $primaryKey;
			} else {
				$this->primaryKeyString = $primaryKey;
			}
		}
	}

	public function getPrimaryKey($gridID = null) {
		if (!$this->primaryKey && $this->getMode($gridID) === Grid2Controller::G2BC_QUERY_MODE_MODEL) {
			$modelName = $this->getModelName($gridID);
			return $modelName::model()->tableSchema->primaryKey;
		}

		if ($this->hasMultiGridMode() && !empty($gridID) && isset($this->primaryKeyArray[$gridID])) {
			return $this->primaryKeyArray[$gridID];
		} else if ($this->hasMultiGridMode() && !empty($gridID) && !isset($this->primaryKeyArray[$gridID])) {
			return null;
		} else {
			return $this->primaryKeyString;
		}
	}

	/**
	 * Model & SQL selection
	 */

	public function setModelSelection($modelSelectionModel, $modelSelectionCriteria, $gridID = 'dhtmlxGrid') {
		$this->modelSelectionModel[$gridID] = $modelSelectionModel;
		$this->modelSelectionCriteria[$gridID] = $modelSelectionCriteria;
	}

	public function getModelSelectionModel($gridID = 'dhtmlxGrid') {
		if (!isset($this->modelSelectionModel[$gridID])) {
			return false;
		}

		return $this->modelSelectionModel[$gridID];
	}

	public function getModelSelectionCriteria($gridID = 'dhtmlxGrid') {
		if (!isset($this->modelSelectionCriteria[$gridID])) {
			return false;
		}

		return $this->modelSelectionCriteria[$gridID];
	}

	public function setSQLSelection($SQLSelection, $primaryKey = null, $gridID = null) {
		if ($this->hasMultiGridMode() && !empty($gridID)) {
			$this->SQLSelection[$gridID] = $SQLSelection;
			$this->setPrimaryKey($primaryKey, $gridID);
		} else {
			$this->SQLSelection = $SQLSelection;
			$this->setPrimaryKey($primaryKey);
		}
	}

	public function setSQLSelectionForReport($SQLSelection) {
		$this->SQLSelectionForReport = $SQLSelection;
	}

	public function setOnBeforeQuerySQL($SQL) {
		$this->onBeforeQuerySQL = $SQL;
		$this->hasOnBeforeQuerySQL = true;
	}

	public function getSQLSelection($gridID = null) {
		if ($this->hasMultiGridMode() && !empty($gridID)) {
			return $this->SQLSelection[$gridID];
		} else {
			return $this->SQLSelection;
		}
	}

	public function getSQLSelectionForReport() {
		return $this->SQLSelectionForReport;
	}

	public function getOnBeforeQuerySQL() {
		return $this->onBeforeQuerySQL;
	}

	public function hasOnBeforeQuerySQL() {
		return $this->hasOnBeforeQuerySQL;
	}
}
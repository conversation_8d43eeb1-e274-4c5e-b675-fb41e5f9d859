<?php 
class LAGridColumnProperties
{
	private $columns			= [];
	private $headers			= [];
	private $modelName			= null;
	private $attributeLabels	= [];
	private $delimiter			= ";";
	private $properties			= [];

	public function __construct($columns, $headers = [], $modelName = null, $attributeLabels = array(), $delimiter = ";") {
		$this->columns			= $columns;
		$this->headers			= $headers;
		$this->modelName		= $modelName;
		$this->attributeLabels	= $attributeLabels;
		$this->delimiter		= $delimiter;
	}

	private function propertiesInitalized() {
		return count($this->properties) > 0;
	}

	private function initGridProperties() {
		if ($this->propertiesInitalized()) {
			return false;
		}

		$columns			= $this->columns;
		$modelName			= $this->modelName;
		$attributeLabels	= $this->attributeLabels;

		$properties = [];

		foreach ($columns as $column => $attrs) {
			if ( !$this->isColumnVisibleInGrid($attrs) ) {
				continue;
			}

			$properties["columns"][] = $column;

			$attributeLabelsModel = [];
			if (!is_null($modelName) && class_exists($modelName)) {
				$attributeLabelsModel = $modelName::model()->attributeLabels();
			}

			$attributeLabelsController = [];
			if (is_array($attributeLabels) && count($attributeLabels)) {
				$attributeLabelsController = $attributeLabels;
			}

			$header = $column;

			if (isset($attributeLabelsModel[$column])) {
				$header = $attributeLabelsModel[$column];
			}

			if (isset($attributeLabelsController[$column])) {
				$header = $attributeLabelsController[$column];
			}

			$properties["headers"][] = $header;

			if (isset($attrs["width"])) {
				$properties["widths"][] = $attrs["width"];
			} else {
				$properties["widths"][] = 200;
			}

			$properties["defaultWidths"][] = 200;

			if (isset($attrs["col_type"])) {
				if ($attrs["col_type"] === "auto") { // autocomplete-nél gridben combo legyen!
					$attrs["col_type"] = "combo";
				}
				$properties["types"][] = $attrs["col_type"];
			} else {
				$properties["types"][] = "ro";
			}

			if (isset($attrs["col_type"]) && $attrs["col_type"] === "combo") {
				$properties["attachHeaders"][] = "#select_filter";
			} else {
				$properties["attachHeaders"][] = "#text_filter";
			}

			if (isset($attrs["align"])) {
				$properties["aligns"][] = $attrs["align"];
			} else {
				$properties["aligns"][] = "left";
			}

			if (isset($attrs["valign"])) {
				$properties["valigns"][] = $attrs["valign"];
			} else {
				$properties["valigns"][] = "middle";
			}

			if (isset($attrs["sorting"])) {
				$properties["colSortings"][] = $attrs["sorting"];
			} else {
				if (isset($attrs["col_type"]) && $attrs["col_type"] === "combo") {
					$properties["colSortings"][] = "_combo";
				} else {
					$properties["colSortings"][] = "str";
				}
			}

			if (isset($attrs["read_only"]) && $attrs["read_only"]) {
				$properties["read_only"][] = 1;
			} else {
				$properties["read_only"][] = 0;
			}

			// edit in LAGrid
			if (isset($attrs["lagrid_edit"]) && $attrs["lagrid_edit"]) {
				$properties["lagrid_edit"][] = 1;
			} else {
				$properties["lagrid_edit"][] = 0;
			}
		}

		$this->properties = $properties;
	}

	public function getGridColumns() {
		$this->initGridProperties();

		$columnProperties = [];

		foreach ($this->properties as $property => $value) {
			$columnProperties[$property] = implode($this->delimiter, $value);
		}

		return $columnProperties;
	}

	public function getGridLabels() {
		$this->initGridProperties();

		$gridLabels = [];

		if (isset($this->properties["columns"]) && is_array($this->properties["columns"])) {
			foreach ($this->properties["columns"] as $id => $column) {
				$header = isset($this->properties["headers"][$id]) ? $this->properties["headers"][$id] : $column;

				$gridLabels[$column] = $header;
			}
		}

		return $gridLabels;
	}

	public function getGridLabelsSerialized($attrName = "labels") {
		$serializedArr = [];

		foreach ($this->getGridLabels() as $column => $label) {
			$serializedArr[] = "{$attrName}[{$column}]={$label}";
		}

		$serializedString = implode("&", $serializedArr);

		return $serializedString;
	}

	public function getGridDropdowns() {
		$columns = $this->columns;
		$dropdowns = array();

		$i = 0;
        if (is_array($columns)) {
            foreach ($columns as $column => $attrs) {
                if ( !$this->isColumnVisibleInGrid($attrs) ) {
                    continue;
                }
    
                if (isset($attrs["col_type"]) && ($attrs["col_type"] === "combo" || $attrs["col_type"] === "auto")) {
                    $dropdowns[] = $i;
                }
    
                $i++;
            }
        }
		

		return "[".implode(",", $dropdowns)."]";
	}

    public static function isColumnVisibleInGrid($attrs) {
		if (is_array($attrs) && (!isset($attrs['grid']) || (isset($attrs['grid']) && $attrs['grid']))) {
			return true;
		}

		return false;
	}

    public static function isColumnVisibleInWindow($attrs) {
		if (is_array($attrs) && (!isset($attrs['window']) || (isset($attrs['window']) && $attrs['window']))) {
			return true;
		}

		return false;
	}

    public static function isColumnVisibleInAddWindow($attrs) {
		if (is_array($attrs) && (!isset($attrs['add']) || (isset($attrs['add']) && $attrs['add']))) {
			return self::isColumnVisibleInWindow($attrs);
		}

		return false;
	}

    public static function isColumnVisibleInEditWindow($attrs) {
		if (is_array($attrs) && (!isset($attrs['edit']) || (isset($attrs['edit']) && $attrs['edit']))) {
			return self::isColumnVisibleInWindow($attrs);
		}

		return false;
	}

	public static function isColumnVisibleInExport($attrs) {
		if (is_array($attrs) && (!isset($attrs['export']) || (isset($attrs['export']) && $attrs['export']))) {
			return true;
		}

		return false;
	}
}
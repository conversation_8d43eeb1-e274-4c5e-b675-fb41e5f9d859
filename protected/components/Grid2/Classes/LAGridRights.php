<?php

class LAGridRights extends LAGridBase
{
    private $controllerRights = [];
	private $rightTypes = array(
		"view",					// controller visibility
		"reload",				// grid reload
		"select",				// in grid, user can select lines
		"multi_select",			// in grid, user can select multiple lines at the same time
		"add",					// right to add new line
		"modify",				// right to modify lines
		"inline_modify",		// right to inline modify lines
		"delete",				// right to delete lines
		"details",				// right to detaild view
		"force_delete",			// right to real DELETE
		"paging",				// right to paging in grid
		"search",				// right to search
		"init_open_search",		// on page load, open searchbar
		"disable_init_data",	// on page load, disable data loading
		"search_header",		// right to search in grid header
		"export_xls",			// right to export XLS
		"export_xlsx",			// right to export XLSX
		"export_pdf",			// right to export PDF
		"column_move",			// column move right
		"col_sorting",			// enable col sorting
		"reload_sortings",		// on grid reload, reload sortings
		"on_click",				// enables gridOnClick(grid_id, selected_id)
		"export_pdf_jasper",	// right to export PDF with jasper
		"export_pdf_node",		// right to export PDF with NodeJS
		"export_csv",			// right to export CSV
		"save_state",           // Save current state
		"print",				// right to print displayed table data
		"regenerate_table",		// right to regenerate table /nemak hr kpi/
		"downloadCSV",			// right to download table content /nemak hr kpi/
	);

	public function getRightTypes() {
		return $this->rightTypes;
	}

	public function controllerInitRights() {
        $cid = $this->getControllerID();

		foreach ($this->getRightTypes() as $right) {
			if (!isset($this->controllerRights['initRights'][$right])) {
				$cid = $this->getControllerID();
				$this->controllerRights['initRights'][$right] = App::hasRight($cid,$right);
			}
		}
	}

	public function overrideInitRightSwitches($multilineString, $gridID = null)
	{
		foreach(linesOf($multilineString) as $line) {
			list($state,$right) = explode("]",$line,2);
			$state = trim($state,"[ ]");
			$state = !!$state;
			$right = trim($right);
			$right = strtr($right," ","_");
			$this->overrideInitRights($right,$state);
		}
	}

	public function overrideInitRights($right, $value = false, $gridID = null)
	{
		if (in_array($right, $this->getRightTypes())) {
			if ($this->hasMultiGridMode()) {
				if (!empty($gridID)) {
					if( is_array($gridID) ){
						foreach ( $gridID as $key => $grid ){
							$this->controllerRights[$grid][$right] = $value;
						}
					}else{
						$this->controllerRights[$gridID][$right] = $value;
					}
				}
			} else {
				$this->controllerRights['initRights'][$right] = $value;
			}
		}
	 }

	public function hasRight($right, $gridID = null) {
		if (in_array($right, $this->getRightTypes())) {
			if ($this->hasMultiGridMode() && !empty($gridID)) {
				if (isset($this->controllerRights[$gridID][$right])) {
					return $this->controllerRights[$gridID][$right];
				} else if (isset($this->controllerRights['initRights'][$right])) {
					return $this->controllerRights['initRights'][$right];
				}
			} else {
				if (isset($this->controllerRights['initRights'][$right])) {
					return $this->controllerRights['initRights'][$right];
				}
			}
		}

		return false;
	}

	public function getControllerRights($gridID = null) {
		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($this->controllerRights[$gridID])) {
				return Yang::arrayMerge($this->controllerRights['initRights'], $this->controllerRights[$gridID]);
			}
		}
        return $this->controllerRights['initRights'];
	}
}
<?php

use Components\Core\Enum\AppSettingsIdEnum;

ini_set('max_execution_time', 3600);
$memoriaLimit = App::getSetting("controllermemoriamax") . 'M';
ini_set('memory_limit', $memoriaLimit);

trait Grid2Actions
{
	/**
	 * Grid base layout
	 */
	public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array()) {
		$this->G2BInit();

		$default_search = Yang::getUserDefaultSearch();

		$controllerId = $this->getControllerID();
		$user_id = userID();

		if (count($default_search)) {
			foreach ($default_search as $sessioncId => $items) {
				if ($sessioncId === $controllerId) {
					foreach ((array) $items as $column => $value) {
						if (!isset($_SESSION["tiptime"][$user_id][$sessioncId]["searchInput_".$column])) {
							$_SESSION["tiptime"][$user_id][$sessioncId]["searchInput_".$column] = $value;
						}
					}
				}
			}
		}

		if (Yang::isAjaxRequest()) {
			$this->layout = "//layouts/ajax";
		} else {
			$this->layout = $layout;

			$this->pageTitle = $this->getPageTitle();
			$this->breadcrumbs = array($this->getControllerPageTitle());
		}

		if ($this->hasRight("view")) {
			// render
			$this->render($view, $params);
		} else {
			$this->redirect(array(Yang::getParam('permdeniedUrl')));
		}
	}

	/**
	 * Report mode content - ajax
	 */
	public function actionReportGridContent() {
		$this->layout = "//layouts/ajax";
	}

	/**
	 * Set grid init properties
	 */
	public function actionSetInitProperties() {
		$results = ["status" => 2,];

		$callback = requestParam('callback');

		if (!empty($callback)) {
			echo $callback."(".json_encode($results, JSON_UNESCAPED_UNICODE).");";
		} else {
			echo json_encode($results, JSON_UNESCAPED_UNICODE);
		}
	}

	/**
	 * Set grid load init properties
	 */
	public function actionSetLoadInitProperties() {
		$results = ["status" => 2,];

		echo json_encode($results);
	}

	/**
	 * Set grid post-init properties
	 */
	public function actionSetPostInitProperties() {
		$results = ["status" => 2,];

		echo json_encode($results);
	}

	/**
	 * Get actual column properties - Dj Robert Debug Edition
	 */
	public function actionGetColumnProperties_Robert() {
		$this->layout = "//layouts/ajax";

//     $srv = Yang::getParam('srv');
//     $usr = Yang::getUsername();

//     if ($srv === "sinia-ttwa.login.hu" && $usr === "robert") { Yang::log("OK1",'log','system.Grid2Actions.actionGetColumnProperties ('.$srv.'-'.$usr.')'); }

		$gridID = requestParam('gridID');

//     if ($srv === "sinia-ttwa.login.hu" && $usr === "robert") { Yang::log("OK1.1",'log','system.Grid2Actions.actionGetColumnProperties ('.$srv.'-'.$usr.')'); }

		$this->G2BInit();

//     if ($srv === "sinia-ttwa.login.hu" && $usr === "robert") { Yang::log("OK1.2",'log','system.Grid2Actions.actionGetColumnProperties ('.$srv.'-'.$usr.')'); }

		$this->setColumnProperties($gridID);

//     if ($srv === "sinia-ttwa.login.hu" && $usr === "robert") { Yang::log("OK1.3",'log','system.Grid2Actions.actionGetColumnProperties ('.$srv.'-'.$usr.')'); }

		$columnProperties = $this->getColumnProperties();

//     if ($srv === "sinia-ttwa.login.hu" && $usr === "robert") { Yang::log("OK1.4",'log','system.Grid2Actions.actionGetColumnProperties ('.$srv.'-'.$usr.')'); }

		$results = array(
			"data" => $columnProperties,
		);

//     if ($srv === "sinia-ttwa.login.hu" && $usr === "robert") { Yang::log("OK1.5",'log','system.Grid2Actions.actionGetColumnProperties ('.$srv.'-'.$usr.')'); }

		echo json_encode($results);
	}	/**
	 * Get actual column properties
	 */
	public function actionGetColumnProperties() {

		$this->layout = "//layouts/ajax";
		$gridID = requestParam('gridID');
		$this->G2BInit();
		$this->setColumnProperties($gridID);
		$columnProperties = $this->getColumnProperties();
		$results = array(
			"data" => $columnProperties,
		);
		echo json_encode($results);

	}

	public function actionGetGridMultiHeaders() {
		$this->layout = "//layouts/ajax";

		$gridID = requestParam('gridID');

		$headers = $this->headers();

		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($headers[$gridID])) {
				$headers = $headers[$gridID];
			}
		}

		$footers = $this->footers();

		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($footers[$gridID])) {
				$footers = $footers[$gridID];
			}
		}

		echo json_encode([
			"headers" => $headers,
			"footers" => $footers,
		]);
	}

	/**
	 * Grid data
	 */
	public function actionGridData() {

		$this->layout = "//layouts/ajax";
		$searchBar = requestParam('searchInput');
		$gridResults = $this->fetchGridData($searchBar);																#timeLoss
		#todo ../wfm/Grid2SummarySheet/controllers/SummarySheet/Grid2SummarySheetData.php

		$laGridEnabled = $this->hasLAGrid();
		$noRowInDhtmlx = isset($gridResults["dhtmlxGrid"]) ? false : true;
		$gridGenFormat = ( ($laGridEnabled)&&($noRowInDhtmlx) ) ? "html":"xml";

		$results = [];

		switch($gridGenFormat) {
			case "html"	: $results["htmlData"	] = $this->generateGridDataHTML($gridResults); break;  #wtmh!!
			case "xml"	: $results["data"		] = $this->generateGridDataXML ($gridResults); break;
		}
		//	Holtartás: http://innote-dk.local/n-71hww2l3

		$keysToCopy = '

			pageCount
			currentPage
			totalRowCount
			pageStart
			pageEnd
			summaryData
			log
			alert
			extraData

		';
		$addValues = [];
		foreach(linesOf($keysToCopy) as $key) if(isset($gridResults[$key])) $addValues[$key] = $gridResults[$key];
		$results = array_merge($results,$addValues);

		$this->generateNodePdf();
		echo json_encode($results);



	}

	/**
	*  NodeJs PDF generálás
	*/
	protected function generateNodePdf() {
		$gridID = requestParam('gridID');
		if($this->hasRight("export_pdf_node", $gridID) && $this->hasLAGrid()){ //ha engedélyezve van az export és az LAGrid is!
			if(isset($_SESSION["tiptime"][ $this->fetchParams["user_id"] ][ $this->fetchParams["controllerId"] ]['gridDataArr']['dhtmlxGrid'])){ // az összes adat ide van mentve
				$exportGridResults = $_SESSION["tiptime"][ $this->fetchParams["user_id"] ][ $this->fetchParams["controllerId"] ]['gridDataArr']['dhtmlxGrid'];
				$dataForHtml = [];
				$multiHeaderWidths = [];
				$this->getRetArrData(true, $gridID, $multiHeaderWidths, "", $exportGridResults, $dataForHtml, false, false, true);
				$table_content = $this->generatePdfDataHTML($dataForHtml, []);

				$content = '
					<html>
					<head>
						<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
					</head>
					<style>
						body
						{
							font-family: Arial, Helvetica, sans-serif;
							font-size: 15px;
						}

						table
						{
							width: 100%;
							border-collapse: collapse;
							border-spacing: 0;
							font-size: 100%;
						}

						table td, table th
						{
							border: 1px solid #000;
							width: auto;
							padding-left: 5px;
						}

						div.page-break {
							display: block;
							page-break-before: always;
							height: 0px;
						}

						h2 {
							text-align: center;
						}

						.hideCell {
							display: none !important;
						}
					</style>
					<body>
						<h2>' . Dict::getValue($this->getControllerPageTitleId()) . '</h2>
						<div class="pdfBody">
							<table>
								' . $table_content . '
							</table>
						</div>
					</body>
					</html>
				';

				$controllerId = $this->getControllerID();

				$fs = new FS;
				$fs->disableMySQLStore();
				$fs->setFileGroupID("NodePDF_".$controllerId);
				$fs->deleteFilesByUserAndFileGroupID();
				$fs->uploadTextFileFromContent($this->getExportFileName().".html", $content, "text/html");
				$fileID = $fs->getFileID();

				$this->setExportFSFileID($fileID);
			}
		}
	}

	/**
	 * Grid data - Dropdowns
	 */
	public function actionDropdown() {

		$this->layout = "//layouts/ajax";

		$gridID				= requestParam('gridID');
		$mode				= requestParam('mode');
		$id					= requestParam('id');
		$hiddenDialogInput	= requestParam('hiddenDialogInput_'.$gridID) ?: [];
		$dialogInput		= requestParam('dialogInput_'.$gridID) ?: [];
		if (App::getSetting(AppSettingsIdEnum::USE_COMPANY_AND_PAYROLL_RIGHTS)) {
			$dialogInput = Yang::arrayMerge(
				$this->extractDialogInputsFromRequest('dialogInput_', $gridID, ['company_id', 'payroll_id']),
				$dialogInput
			);
		}
		$searchInput		= requestParam('searchInput');
		$controller_id		= $this->getControllerID();

		'AnyCache';{

			$acEnable = useExperimental("enable caching of grid2 dropdowns");
			$acVerify = useExperimental("verify cache of grid2 dropdowns");

			if($acEnable) {
				$params = [];
				$acDependsOn = [$gridID,$id,$mode,$params,$searchInput,$hiddenDialogInput,$dialogInput,$controller_id];
				$acKey = AnyCache::key("actionDropdown",$acDependsOn);
				$acHit = AnyCache::has($acKey);
				if($acHit) if(!$acVerify) {print AnyCache::get($acKey);return;}
			}

		}

		if ($mode === "DIALOG" && $gridID === "dhtmlxGrid" && ($this->getControllerID() === "userRight" || $this->getControllerID() === "userRightExtended") && App::getSetting("useCompanyAndPayrollRights") == "1") {
			$gargUse = [
				"user_id"		=> $dialogInput["approver_user_id"] ?? "",
				"date"			=> $searchInput["date"] ?? "",
				"process_id"	=> $dialogInput["process_id"] ?? "",
				"related_model"	=> $dialogInput["related_model"] ?? "",
				"valid_from"	=> $dialogInput["valid_from"] ?? "",
				"valid_to"		=> $dialogInput["valid_to"] ?? ""
			];
		} else {
			$gargUse = [];
		}

		if ($mode === "DIALOG") {
			$default_value=requestParam('default_value');
			$params = Yang::arrayMerge($hiddenDialogInput, $dialogInput,array('default_value'=> $default_value));
			$dropdowns = $this->fetchDropdownData($id, $mode, false, null, $params, [], $gridID, $gargUse);
		} else { // GRID
			$dropdowns = $this->fetchDropdownData($id, $mode, false, null, $searchInput, [], $gridID);
		}

		$prettyLog = [];
		$prettyLog["log"] = $dropdowns["log"] ?? '';
		$prettyLog["col"] = $dropdowns["col"] ?? '';
		if (isset($dropdowns["data"]))
		{
			foreach ($dropdowns["data"] as $key => $val) {
				$prettyLog["data"][$key]["data"] = escapeJsonString($val["data"]);
			}
		}

		if (in_array($this->getControllerID(), ["fluctuationRate", "headCountDistribution"]) && Yang::customerDbPatchName() == "bn") {
			// Nem fér be a táblába 29 000 költséghely
		} else {
			Log::create($this, "DROPDOWN", ["data" => $prettyLog]);
		}

		$resp = $this->generateDropdownDataXML($dropdowns, $mode);

		$results = array(
			'id' => $id,
			'data' => $resp
		);

		if (isset($dropdowns['log'])) {
			$results = array_merge($results, array(
				'log' => $dropdowns['log'],
			));
		}
		if (isset($dropdowns['col'])) {
			$results = array_merge($results, array(
				'col' => $dropdowns['col'],
			));
		}

		$out = json_encode($results);

		if($acEnable) {
			$correctValue = $out;
			if($acVerify) AnyCache::check('actionDropdown',$acKey,$correctValue);
			// AnyCache::set($acKey,$correctValue); #completeThis // itt nem tudtam kitalálni, milyen tábláktól függünk
		}

		print $out;
	}

	/**
	 * CreateG2BMainGrid on searchBarReinitGrid
	 * @return void
	 */
	public function actionReloadDropdowns()
	{
		$gridID = requestParam('gridID');
		$resp	= $this->getColumnDropdowns($gridID);
		echo $resp;
	}

	/**
	 * Grid data - Auto complete
	 */
	public function actionAutocomplete() {
		$this->layout       = "//layouts/ajax";
		$mode               = requestParam('mode');
		$id                 = requestParam('id');
		$expression         = requestParam('expression');
		$search             = requestParam('search');
		$searchInput        = requestParam('searchInput');
		$gridID				= requestParam('gridID');
		$hiddenDialogInput	= requestParam('hiddenDialogInput_'.$gridID) ?: [];
		$dialogInput		= requestParam('dialogInput_'.$gridID) ?: [];

		$search = str_replace("4ee9a03852e5c5ce443cb71773691133", "&", $search);

		if ($search === "*") {
			$search = "";
		}

		if (strlen($search) > 0 && strlen($search) < 2) {
			$resp = "";

			$resp .= '<li data-value="">'.Dict::getValue("auto_complete_minlength", array('minLength' => 2)).'</li>';

			$results = array(
				'id' => $id,
				'data' => $resp
			);

			$json = json_encode($results);

			echo $json;

			return false;
		}

		if ($mode === "DIALOG" && $gridID === "dhtmlxGrid" && ($this->getControllerID() === "userRight" || $this->getControllerID() === "userRightExtended") && App::getSetting("useCompanyAndPayrollRights") == "1") {
			$gargUse = [
				"user_id"		=> $dialogInput["approver_user_id"],
				"date"			=> $searchInput["date"],
				"process_id"	=> $dialogInput["process_id"],
				"related_model"	=> $dialogInput["related_model"],
				"valid_from"	=> $dialogInput["valid_from"],
				"valid_to"		=> $dialogInput["valid_to"]
			];
		} else {
			$gargUse = [];
		}

		if ($mode === "DIALOG") {

			$default_value = requestParam('default_value');
			$params        = Yang::arrayMerge($hiddenDialogInput, $dialogInput,array('default_value'=> $default_value));
			$dropdowns     = $this->fetchDropdownData($id, $mode, true, $search, $params, [], $gridID, $gargUse);
		} else { // GRID
			$dropdowns     = $this->fetchDropdownData($id, $mode, true, $search, $searchInput, [], $gridID);
		}

		$resp = "";

		$i = 0;

		if (isset($dropdowns['data']) && is_array($dropdowns['data'])) {
			foreach ($dropdowns['data'] as $key => $value) {
				$resp .= '<li data-value="'.$key.'">'.$value["data"].'</li>';

				$i++;
			}
		}

		if (!$i) {
			if ($expression == "1" && substr($search, -1) == "*") {
				$resp .= '<li data-value="' . $search . '">' . $search . '</li>';
			} else {
				$resp .= '<li data-value="">'.Dict::getValue("auto_complete_no_results_found").'</li>';
			}
		}

		$results = array(
			'id' => $id,
			'data' => $resp
		);

		$json = json_encode($results);

		echo $json;
	}

	/**
	 * Grid preferences in DB
	 */
	public function actionSaveSitePreferences() {
		$this->layout = "//layouts/ajax";

		$column_order = requestParam('columnOrder');
		$column_visibility = requestParam('columnVisibility');

		$user_id = userID();
		$controller_id = $this->getControllerID();

		$gsp = new G2bSitePreference;
		$crit = new CDbCriteria();
		$crit->condition = "`user_id` = '$user_id' AND `controller_id` = '$controller_id' AND `status` = 2";
		$res_gsp = $gsp->find($crit);

		if ($res_gsp) {
			if ($column_order) {
				$res_gsp->column_order = $column_order;
			}
			if ($column_visibility) {
				$res_gsp->column_visibility = $column_visibility;
			}

			$res_gsp->modified_by = $user_id;
			$res_gsp->modified_on = date("Y-m-d H:i:s");

			$res_gsp->save();
		} else {
			$gsp = new G2bSitePreference;

			$gsp->user_id = $user_id;
			$gsp->controller_id = $controller_id;
			if ($column_order) {
				$gsp->column_order = $column_order;
			}
			if ($column_visibility) {
				$gsp->column_visibility = $column_visibility;
			}
			$gsp->status = Status::PUBLISHED;
			$gsp->created_by = $user_id;
			$gsp->created_on = date("Y-m-d H:i:s");

			$gsp->save();
		}
	}

	/**
	 * Add/Modify Dialog
	 */
	public function actionDialog($layout = '//Grid2/layouts/dialogLayout', $view = '/Grid2/dialog', $additionalParams = []) {
		$this->G2BInit();

		$this->layout = $layout;

		$dialogMode = (int)requestParam('dialogMode'); // G2BC_DIALOG_MODE_{ADD,MOD,DET}
		$editPK = requestParam('editPK');
		$actionSaveUrl = requestParam('actionSaveUrl');
		$generateFrom = requestParam('generate_from');
		$gridID = requestParam('gridID');
		$defaults = requestParam('defaults');
		$dialogMaxWidth = $this->getDialogMaxWidth();
		$lang = requestParam('lang');

		$defaultParams = ["generateFrom" => $generateFrom, "dialogMode" => $dialogMode, "editPK" => $editPK, "actionSaveUrl" => $actionSaveUrl, "gridID" => $gridID, "defaults" => $defaults, "dialogMaxWidth" => $dialogMaxWidth, "lang" => $lang];

		//TODO: refaktorálni kell az alsó switch-et, így csúnya

		switch ($generateFrom) {
			case 'competencyDialog':
				if (session_status() == PHP_SESSION_NONE) {
					session_start();
				}
				$_SESSION['competencyDialog']['rowId'] = $editPK;
				break;
			case 'copyWSOneDayDialog':
			case 'copyWSMoreDaysDialog':
				if (session_status() == PHP_SESSION_NONE) {
					session_start();
				}
				$_SESSION['copyWSDialog'] = [
					'employeeContractId' => requestParam('employeeContractId'),
					'startDate'             => requestParam('startDate'),
					'endDate'           => requestParam('endDate')
				];
				break;
			case 'editSchedulingAssistantOneDayDialog':
				if (session_status() == PHP_SESSION_NONE) {
					session_start();
				}
				$_SESSION['editSchedulingAssistantDialog'] = [
				'employeeContractId' => requestParam('employeeContractId'),
				'startDate'          => requestParam('startDate'),
				'endDate'            => requestParam('endDate')
				];
				break;
			case 'unlockSchedulingAssistantOneDayDialog':
				if (session_status() == PHP_SESSION_NONE) {
					session_start();
				}
				$_SESSION['unlockSchedulingAssistantDialog'] = [
				'employeeContractId' => requestParam('employeeContractId'),
				'startDate'          => requestParam('startDate'),
				'endDate'            => requestParam('endDate')
				];
				break;
			case 'modEmployeeExtraHoursDialog':
				if (session_status() == PHP_SESSION_NONE) {
					session_start();
				}
				$_SESSION['modEmployeeExtraHoursDialog'] = [
					'start' => $additionalParams["defaults"]["start"],
					'end'	=> $additionalParams["defaults"]["end"]
				];
				break;
		}

		$params = Yang::arrayMerge($additionalParams, $defaultParams);

		$this->render($view, $params);
	}

	/**
	 * Save action
	 */
	public function actionSave($data = array(), $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null) {
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		$gridID			= requestParam('gridID');
		$editPK			= requestParam('editPK');
		$mainGridID		= requestParam('gridID');
		$generateFrom	= requestParam('generateFrom');

		if (!empty($modelName)) {

		} else {
			if (!empty($generateFrom)) {
				$modelName = $this->LAGridDB->getModelName($generateFrom);

				$pk = $this->LAGridDB->getPrimaryKey($generateFrom);
			} else {
				$modelName = $this->LAGridDB->getModelName($gridID);

				$pk = $this->LAGridDB->getPrimaryKey($gridID);
			}
		}

		$columns = $this->columns();

		if (!count($data)) {
			$data		= requestParam('dialogInput_'.$generateFrom);
			$defaults	= requestParam('defaults');

			if ((!isset($data[$pk]) || empty($data[$pk])) && is_array($defaults) && count($defaults)) {
				$data = Yang::arrayMerge($defaults, $data);
			}
		}

		if (isset($data[$pk]) && $data[$pk]) {
			$m = $modelName::model()->findByPk($data[$pk]);
			$saveMode = "update";

		} else {
			$m = new $modelName('insert');
			$saveMode = "insert";

			$user_id = userID();

			$data["created_by"] = $user_id?$user_id:"visitor";
			$data["created_on"] = date("Y-m-d H:i:s");
		}

		/**
		 * Amennyiben be van kapcsolva a $bigFormMode (amivel több modelt magában
		 * foglaló formokat lehet létrehozni), a lekért adatok alapján kötelező
		 * mezőket ellenőrzi hogy ki vannak-e töltve. Ha nincsenek, hibaüzenetet ad
		 * a model aktuális oszlopához.
		 */
		$bigFormMode = $this->getBigFormMode($generateFrom);

		if ($bigFormMode && $m) {
			$bigFormLayout = $this->getBigFormLayout($generateFrom, $modelName, $contentId);

			foreach ($bigFormLayout as $col => $props) {
				$colArray = explode("__", $col);

				$column = $colArray[0];
				if (count($colArray) === 2) {
					$column = $colArray[1];
				}

				if ($props["mandatory"]) {
					if (isset($data[$column]) && empty($data[$column])) {
						//$m->addError($column,"error_field_required");
						$m->addGenericError("error_field_required",array("attribute"=>$props["label_text"]), $column);
					} else if (!isset($data[$column])) {
						//$m->addError($column,"error_field_required");
						$m->addGenericError("error_field_required",array("attribute"=>$props["label_text"]), $column);
					}
				}
			}
		}

		foreach ($data as $column => $value)
		{
			if (isset($columns[$column]['mandatory']))
			{
				if (method_exists($m, "attributeLabels")) {
					$labels = $m->attributeLabels();
					$label = $labels[$column] ?? "";
				} else {
					$label = "";
				}
				if (isset($data[$column]) && empty($data[$column])) {
					$m->addGenericError("error_field_required", ["attribute" => $label]);
				} else if (!isset($data[$column])) {
					$m->addGenericError("error_field_required", ["attribute" => $label]);
				}
			}

			// pw típusnál sha512-vel mentünk!
			if (isset($columns[$column]['col_type']) && $columns[$column]['col_type'] === "pw") {
				if (isset($data[$column.'_prev'])) {
					if (empty($data[$column.'_prev'])) {
						$m->addGenericError("error_prev_password_required");
					} else if (!empty($data[$column.'_prev'])) {
						$prev_password = hash('sha512', $data[$column.'_prev']);

						if ($m->password !== $prev_password) {
							$m->addGenericError("error_prev_password_incorrect");
						}
					}
				}
				$pwModified = false;

				// sha512 value
				if (!empty($value) && !empty($data[$column.'_retype']))
				{
					$pwModified			= true;
					$password_settings	= App::getSetting("passwordStrength");
					$minLength			= $password_settings[count($password_settings)-1]['minLength'];
					$matches			= $password_settings[count($password_settings)-1]['match'];

					if (strlen($value) < $minLength) {
						$m->addGenericError("error_password_minlength",array("minLength"=>$minLength));
						$pwModified = false;
					}

					$reqexp_valid = true;
					foreach ($matches as $match) {
						if (!preg_match("/$match/", $value)) {
							$reqexp_valid = false;
							$pwModified = false;
						}
					}

					if (!$reqexp_valid) {
						$m->addGenericError("error_password_security_conditions");
					}

					if ($value !== $data[$column.'_retype']) {
						$m->addGenericError("error_password_doesnt_match");
						$pwModified = false;
					}

                    if ($modelName === 'User') {
                        if ((App::getSetting('sendNewUserEmail') === '1') && ($column === 'password')) {
                            $_SESSION['tiptime']['origPasswd'] = $value;
                        }
                    }

					$data[$column] = hash('sha512', $value);

					if (isset($data[$column . '_prev'])) {
						if ($data[$column] === $m->password) {
							$m->addGenericError("error_old_new_password_must_be_different");
							$pwModified = false;
						}
					}
				} else if (isset($data[$column . '_prev'])) {
					if (empty($value)) {
						$m->addGenericError("error_field_required", ["attribute" => Dict::getValue("password")]);
					}
				} else if (isset($data[$pk]) && $data[$pk]) { // edit mode
					$data[$column] = $m->$column;
				}

				if (!empty($value) && $modelName === 'User' && $this->getControllerID() == "user" && $column == "password" && empty($data[$column.'_retype'])) {
					$m->addGenericError("error_password_doesnt_match");
				}

				// feltöltéskor mindig van password_date, hogy üres jelszónál ne validáljon...
				if (empty($editPK) || $pwModified) {
					$data[$column . '_date'] = ($saveMode == "insert" && (int)App::getSetting("newUserAutoPasswordChange")) ? App::getSetting("defaultStart") : date("Y-m-d H:i:s");
				}
			}

			$noSpacesInUser = (int)App::getSetting("noSpacesInUserName");
			if ($modelName === 'User' && $this->getControllerID() == "user" && $column == "username" && $noSpacesInUser && strpos($value, " ") !== false) {
				$m->addGenericError("error_no_spaces_in_username");
			}
			unset($columns[$column]);
		}
		if ($saveMode === "insert") {
			unset($columns['status']);
			foreach ($columns as $columnName => $columnProperties) {
				if ($columnName != "password_date" || ($columnName == "password_date" && empty($data[$columnName]))) {
					// Lehetne esetleg a window & edit -> false-t is vizsgálni, de bácsival ez a verzió megbeszélve
					$notSet = ($m->hasAttribute($columnName) && isset($m->$columnName) && $m->$columnName != '') ? $m->$columnName : '';
					$data[$columnName] = $columnProperties['default_value'] ?? $notSet;
				}
			}
		}

		if($this->getControllerID() === "forgottenPassword") $data['locked_until'] = null;
		$m->attributes = $data;
		$m->allReceivedAttributes=$data;
		$m->setTitleId($this->getControllerPageTitleId());
		$valid = $m->validate();
		$status = [];
		$warnings = $m->getWarnings();

		if ($valid) {
			if (!$vOnly) {
				$m->save();

				if ($this->getControllerID() === "userPwExpired" || requestParam('redirectOnSuccess') === "start/index") {
					$login = new LoginForm;
					$login->ttwaun = $m->username;
					$login->ttwapw = $data['password_retype'];

					$l_valid = $login->validate();
					if ($l_valid) {
						$login->login();
						$_SESSION["tiptime"]["just_logged_in"] = true;
					}
				}
			}

			// Log employee data changes to main log
			if (method_exists($m, "getOldAttributes") && method_exists($m, "getAttributes") && method_exists($m, "getIdentifyColumn"))
			{
				$oldData = $m->getOldAttributes();
				$newData = $m->getAttributes();
				$identifyColumn = $m->getIdentifyColumn();
				$formId = $generateFrom;
				$changesArray = [];
				// these columns changes are not saved
				$ignoreCols = ["modified_on", "modified_by", "employee_id", "employee_contract_id", "row_id", "created_by", "status", "group_id", "is_default"];
				$ignoreCols2 = ["modified_on", "modified_by", "row_id", "pre_row_id", "status", "created_on", "created_by"];

				// save column changes to json data rows - main log
				foreach ($newData as $key => $newDataRow)
				{
					$oldDataValue = $oldData[$key] ?? null;
					if ($newDataRow != $oldDataValue && ((!in_array($key, $ignoreCols) && $this->getControllerID() == "employee") || ($this->getControllerID() != "employee" && !in_array($key, $ignoreCols2))))
					{
						// When using add button saves with employeeBaseAbsenceTab -> override if column not in employee_base_absence table
						if ($formId == "employeeBaseAbsenceTab")
						{
							$columNames = EmployeeBaseAbsence::model()->getTableSchema()->getColumnNames();
							if (!in_array($key, $columNames)) {
								$formId = "employeeNewEmployeeTab";
							}
						}

						// when Group Tab save group_id aswell
						if ($key == "group_value")
						{
							$changesArrayData =
							[
								"model"					=> $modelName,
								"formId"				=> $formId,
								"prevValue"				=> $oldDataValue,
								"newValue"				=> $newDataRow,
								"changedColumn"			=> $key,
								"group_id"				=> $newData["group_id"]
							];
						} else {
							$changesArrayData =
							[
								"model"					=> $modelName,
								"formId"				=> $formId,
								"prevValue"				=> $oldDataValue,
								"newValue"				=> $newDataRow,
								"changedColumn"			=> $key
							];
						}
						if (!is_null($identifyColumn))
						{
							$changesArray[] = array_merge($changesArrayData, [$identifyColumn => $newData[$identifyColumn]]);
						}
					}
				}

				Log::create($this, "GRID_DATA_CHANGE", ["data" => $changesArray]);
			}


			$status = array(
				'status'	=> 1,
				'pkSaved'	=> $m->$pk,
				'error'		=> null,
			);
		} else {
			$error = MyActiveForm::_validate($m);

			$arr = (array) json_decode($error);
			$msg = "";

			foreach ($arr as $value) {
				foreach ($value as $val) {
					$msg .= $val . "<br/>";
				}
			}

			$status = array(
				'status'	=> 0,
				'pkSaved'	=> null,
				'error'		=> $msg,
			);
		}

		$status['warning_status'] = 1;
		$status['warning_msg'] = "";

		if (count($warnings)) {
			$status['warning_status'] = 0;
			$status['warning_msg'] = "";

			foreach ($warnings as $warn) {
				$params = [];

				if (isset($warn["params"])) {
					$params = $warn["params"];
				}

				$status['warning_msg'] .= Dict::getValue($warn["message"], $params) . "<br/>";
			}
		}

		if (!$ret)
		{
            if (Yang::session('mobileView', 0) && Yang::session('mobileBrowser', 0))
			{
				if ($status["status"] == "1") {
					Yang::setSessionValue('successMessage', 1);
					if (!empty(requestParam('redirectOnSuccess'))) {
                        $this->redirect("/" . requestParam('redirectOnSuccess'));
                    }
				} else {
					if (!empty(requestParam('redirectOnError'))) {
						Yang::setSessionValue('errorMessage', $status["error"]);
						$this->redirect("/" . requestParam('redirectOnError'));
					} else {
                        $this->beginContent("//mobile/mobile.basic", ['error' => $status["error"], 'errorTitle' => Dict::getValue("an_error_occured")]);
                        $this->renderPartial("//mobile/base/" . requestParam("layoutOnError"));
                        $this->endContent();
                    }
				}
            } else {
				echo json_encode($status);
				if ($this->getControllerID() === "forgottenPassword") { return $status["status"]; }
			}
		} else {
			return $status;
		}
	}

	/**
	 * A függvény a modellen alapuló táblába menti az adott grid-es felület beviteli mezejében megadott adatokat AJAX-hívást és validálást követően.
	 *
	 * Egy adott felületen a másolás gombbal használandó
	 *
	 * @param array $data
	 * @param string $modelName
	 */

	public function actionSaveCopy($data = array(), $modelName = null)
	{
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		$gridID = requestParam('gridID');
		$generateFrom = requestParam('generateFrom');

		$modelName = $this->LAGridDB->getModelName($gridID);

		if ($modelName)
		{
			if (!count($data)) {
				$data = requestParam('dialogInput_'.$generateFrom);
			}

			$m = new $modelName;

			$user_id = userID();

			$data["created_by"] = $user_id ? $user_id : "nouser";
			$data["created_on"] = date("Y-m-d H:i:s");

			$m->attributes = $data;
			$m->allReceivedAttributes=$data;
			$m->setTitleId($this->getControllerPageTitleId());
			$valid = $m->validate();
			$status = [];

			if ($valid) {
				if (method_exists($m, "setCopyMode")) {
					$m->setCopyMode(true);
				}
				$m->save();

				$status = array(
					'status'	=> 1,
					'error'		=> null
				);
			} else {
				$error = MyActiveForm::_validate($m);

				$arr = (array) json_decode($error);
				$msg = "";

				foreach ($arr as $value) {
					foreach ($value as $val) {
						$msg .= $val . "<br/>";
					}
				}

				$status = array(
					'status'	=> 0,
					'pkSaved'	=> null,
					'error'		=> $msg,
				);
			}
		} else {
			$status = array(
					'status'	=> 0,
					'pkSaved'	=> null,
					'error'		=> "Missing model name"
					);
		}

		echo json_encode($status);
	}

	public function actionSaveCellValue() {
		$status = [
			'status'	=> 1,
			'pkSaved'	=> null,
			'error'		=> "",
		];

		echo json_encode($status);
	}

	public function actionCheckInlineCell() {
		$status = [
			'status'	=> 1,
		];

		$column        = requestParam('column');
		$key       = requestParam('key');

		echo json_encode($status);
	}

	/**
	 * Delete action
	 */
	public function actionDelete($modelName = null, $hasRight = false) {
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		$gridID = requestParam('gridID');
		$gridID = empty($gridID) ? "dhtmlxGrid" : $gridID;

		$hasRight = $hasRight||$this->hasRight("delete", $gridID);

		$ids = requestParam('ids');

		$idsArray = explode(";", $ids);

		$modelsToDelete = [];
		$idsToDelete = [];

		$mainGridID = requestParam('mainGridID');

		$generateFrom = requestParam('generateFrom');

		$wizards = $this->wizards();

		$wizardMode = false;

		if (is_array($wizards) && count($wizards) && isset($wizards[$mainGridID]) && is_array($wizards[$mainGridID]) && count($wizards[$mainGridID])) {
			$wizardMode = true;
		}

		if ($wizardMode) {
			if (!empty($generateFrom)) { // wizard dialog-on belüli történetiség-törlések
				$currentTabParams = [];

				foreach($wizards[$mainGridID] as $tabId => $tabParams) {
					if ($tabParams["contentId"] === $generateFrom) {
						$currentTabParams = $tabParams;
					}
				}

				if (isset($currentTabParams["modelName"])) {
					$modelsToDelete[] = $currentTabParams["modelName"];
					$idsToDelete[$currentTabParams["modelName"]] = $idsArray;

					$row = $currentTabParams['modelName']::model()->findByAttributes(['row_id' => $idsArray]);
					if ((isset($row['fs_file_id']) && $row['fs_file_id'] !== NULL) || (isset($row['employee_image']) && $row['employee_image'] !== NULL))
					{
						if (isset($row['fs_file_id']) && $row['fs_file_id'] !== NULL)
						{
							$fileRow = FileStorage::model()->findByAttributes(['file_id' => $row['fs_file_id']]);
						}
						else
						{
							$fileRow = FileStorage::model()->findByAttributes(['file_id' => $row['employee_image']]);
						}

						$fileName = $fileRow['file_name'];
						$file = $fileRow['file_url'];

						if (is_file($file) && file_exists($file) && is_readable($file))
						{
							$customer = Yang::getParam('customerDbPatchName');
							$archivePath = 'upload' . DIRECTORY_SEPARATOR . 'archive' . DIRECTORY_SEPARATOR . $customer . DIRECTORY_SEPARATOR;

							if (!file_exists($archivePath) && !is_dir($archivePath)) {
								mkdir($archivePath, 0755, TRUE);
							}
							copy($file, $archivePath . $fileName);

							$fileFindByPk = FileStorage::model()->findByPk($fileRow['row_id']);
							$fileFindByPk->file_url = 'upload' . DIRECTORY_SEPARATOR . 'archive' . DIRECTORY_SEPARATOR . $customer . DIRECTORY_SEPARATOR . $fileName;
							$fileFindByPk->update();
							unlink($file);
						}
					}
				}
			} else { // wizard esetén mainGrid-ből történő törlés
				foreach ($idsArray as $mainId) {
					$editPKArr = [];

					if ($this->LAGridDB->isEnabledSplitRowID() && $delimiter = $this->LAGridDB->getRowIDDelimiter()) {
						$editPKArr = explode($delimiter, $mainId);
					}

					foreach($wizards[$mainGridID] as $tabId => $tabParams) {
						$modelsToDelete[] = $tabParams["modelName"];

						if (isset($tabParams["tabSQL"]) && !empty($tabParams["tabSQL"])) {
							$tabSQL = $tabParams["tabSQL"];

							foreach ($editPKArr as $id => $val) {
								$tabSQL = str_replace("{"."row_id_p".($id+1)."}", $val, $tabSQL);
							}

							$tabSQL = preg_replace('/\{[^}]+\}/', '', $tabSQL); // replace further {}'s to empty str

							$tabRES = dbFetchAll($tabSQL);

							if (is_array($tabRES) && count($tabRES)) {
								foreach ($tabRES as $res) {
									$idsToDelete[$tabParams["modelName"]][] = $res["row_id"];
								}
							}
						}
					}
				}
			}
		} else {
			$modelsToDelete[] = $this->LAGridDB->getModelName($gridID);
			$idsToDelete[$this->LAGridDB->getModelName($gridID)] = $idsArray;
		}

		if (count($modelsToDelete)) {
			$deleteSuccess = true;

			foreach ($modelsToDelete as $modelName) {
				if ($hasRight && isset($idsToDelete[$modelName]) && count($idsToDelete[$modelName])) {
					if (!empty($modelName)) {
						foreach ($idsToDelete[$modelName] as $id) {
							$model = $modelName::model()->findByPk($id);
							if (!is_null($model))
							{
								Log::create($this, "GRID_DATA_DELETE", ["data" => ["model" => $modelName, "id" => $id]]);
								if ($this->hasRight("force_delete")) {
									$model->delete();
								} else {
									$statusExists = $model->hasAttribute("status");

									if ($statusExists) {
										$model->setTitleId($this->getControllerPageTitleId());
										if($model->hasAttribute("modified_by"))
										{
											$model->modified_by = userID();
										}
										if($model->getLoginExist())
										{
											$model->status = Status::DELETED; // deleted
										}
										else
										{
											$model->status = Status::STATUS_REMOVED; // deleted
										}
										$model->save(false);
									} else {
										$model->delete();
									}
								}
							} else {
								if ($this->getControllerID() === "employee" && ($modelName == "Employee" || $modelName == "EmployeeContract")) {
									$deleteSuccess = $deleteSuccess && false;
								} else if ($this->getControllerID() === "employee") {
									// Többi tabon nem biztos van adat, nem feltétlen lesz mit törölni
								} else {
									$deleteSuccess = $deleteSuccess && false;
								}
							}
						}
					} else {
						$deleteSuccess = $deleteSuccess && false;
					}
				} else {
					if ($this->getControllerID() === "employee" && ($modelName == "Employee" || $modelName == "EmployeeContract")) {
						$deleteSuccess = $deleteSuccess && false;
					} else if ($this->getControllerID() === "employee") {
						// Nincs felvéve rekord a tabra
					} else {
						$deleteSuccess = $deleteSuccess && false;
					}
				}
			}

			if ($deleteSuccess) {
				$status = array(
					'status'	=> 1,
					'pkDeleted' => $ids,
				);
			} else {
				$status = array(
					'status'	=> 0,
					'message'	=> Dict::getValue("an_error_occured")
				);
			}
		} else {
			$status = array(
				'status'	=> 0,
			);
		}

		echo json_encode($status);
	}

	public function actionSaveFormValue() {
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		$data = requestParam('data');

		$user_id       = userID();
		$controllerId	= $this->getControllerID();

		$input = $data["input"];
		$value = $data["value"];

		$_SESSION["tiptime"][$user_id][$controllerId][$input] = $value;

		if (isset($data["auto_input"]) && isset($data["auto_value"])) {
			$auto_input = $data["auto_input"];
			$auto_value = $data["auto_value"];

			$_SESSION["tiptime"][$user_id][$controllerId][$auto_input] = $auto_value;
		}
	}

	/**
	 * Export XLS/XLSX action
	 */
	public function actionExportXLS() {
		$type = 'XLSX';
		if (requestParam('type')) {
			$type = requestParam('type');
		}

		$filename = '';
		if (requestParam('filename')) {
			$filename = requestParam('filename');
		}

		$this->G2BInit();

		$url = $this->generateExcel($type, $filename, $this->hasMultiGridMode());

		$response = array(
			'success' => true,
			'url' => $url,
		);

		echo json_encode($response);
	}

	/**
	* Export CSV action
	*/
	public function actionExportCSV()
	{
		$this->G2BInit();

		$filename = '';

		if (requestParam('filename')){
			$filename = requestParam('filename');
		}else{
			$filename = $this->getExportFileName();
		}

		$url = $this->generateCsv($filename);

		$response = [
			'success' => true,
			'url' => $url,
		];

		echo json_encode($response);

	}

	/**
	 * Export PDF action
	 */
	public function actionExportPDF() {
		$filename = '';
		if (requestParam('filename')) {
			$filename = requestParam('filename');
		}

		$url = $this->generatePDF($filename);

		$response = array(
			'success' => true,
			'url' => $url,
		);

		echo json_encode($response);
	}

	/**
	 * Export PDF from HTML with dompdf action
	 */
	public function actionExportPDFFromHTML() {
		$html = '';
		if (requestParam('controllerId')) {
			$controllerId = requestParam('controllerId');

			if (isset($_SESSION["tiptime"][$controllerId]["toPrint"])) {
				$html = $_SESSION["tiptime"][$controllerId]["toPrint"];
			}
		}

		$filename = '';
		if (requestParam('filename')) {
			$filename = requestParam('filename');
		}

		$preDefinedHTML = false;
		if (requestParam('preDefinedHTML')) {
			$preDefinedHTML = true;
		}

		$url = $this->generatePDFFromHTML($html, $filename, $preDefinedHTML);

		$response = array(
			'success' => true,
			'url' => $url,
		);

		echo json_encode($response);
	}

	public function actionGetActualControllerRights() {
		$this->layout = "//layouts/ajax";
		$this->G2BInit();

		$gridID = requestParam('gridID');
		$gridID = empty($gridID) ? "dhtmlxGrid" : $gridID;

		$controllerRights = $this->getControllerRights($gridID);

		$response = [];

		foreach ($controllerRights as $right => $value) {
			$response[$gridID][$right] = ($value ? 1 : 0);
		}

		echo json_encode($response);
	}

	/**
	 * A függvény lekérdezi a signature_fields_on_reports táblából, hogy az adott kimutatáshoz fel lett-e véve aláírás-
	 * mező. Az egyes kimutatások exportja esetén, a html generálásához használandó a funkció.
	 *
	 * @param string $controller_id
	 */

	private function getSignatureFields($controller_id)
	{
		$SQL = "
				SELECT
					`signatory`
				FROM `signature_fields_on_reports`
				WHERE
						`status` = ".Status::PUBLISHED."
					AND `controller_id` = '$controller_id'
				";

		$results = dbFetchAll($SQL);

		if (count($results) > 0) {
			return $results;
		} else {
			return false;
		}
	}

	/**
	 * A megadott paraméterek alapján (generateFrom, model, column, isChecked) a
	 * big_form_layout táblába menti, hogy az adott oszlop (input) megjelenjen-e
	 * a form-on.
	 */
	public function actionSaveBigFormLayout() {
		$generateFrom  = requestParam('generateFrom');
		$model         = requestParam('model');
		$column            = requestParam('column');
		$isChecked     = (int)requestParam('isChecked') ? 1 : 0;

		if ((int)App::getRight($this->getControllerId(), "uploadDialogEdit")) {
			$SQL = "
				INSERT INTO `big_form_layout`
					(`controller_id`, `generate_from`, `model`, `column`, `enabled`)
				VALUES
					('".$this->getControllerID()."', '$generateFrom', '$model', '$column', '$isChecked')
				ON DUPLICATE KEY UPDATE
					`enabled` = VALUES(`enabled`);
			";

			dbExecute($SQL);
		}

		$status = [];
		echo json_encode($status);
	}

	/**
	 * A megadott paraméterek alapján (generateFrom, model, column, isChecked) a
	 * big_form_layout táblába menti, hogy az adott oszlop (input) kötelező-e
	 * a form-on. (Csak akkor érhető el, ha a mező model-szinten nem kötelező!)
	 */
	public function actionSaveBigFormMandatory() {
		$generateFrom  = requestParam('generateFrom');
		$model         = requestParam('model');
		$column            = requestParam('column');
		$isChecked     = (int)requestParam('isChecked') ? 1 : 0;

		if ((int)App::getRight($this->getControllerId(), "uploadDialogEdit")) {
			$SQL = "
				INSERT INTO `big_form_layout`
					(`controller_id`, `generate_from`, `model`, `column`, `mandatory`)
				VALUES
					('".$this->getControllerID()."', '$generateFrom', '$model', '$column', '$isChecked')
				ON DUPLICATE KEY UPDATE
					`mandatory` = VALUES(`mandatory`);
			";

			dbExecute($SQL);
		}

		$status = [];
		echo json_encode($status);
	}

	/**
	 * Adatbázisból generált azonosítókat (emp_id) lehet a funkcióval növelni, a
	 * duplikálás lehetőségének teljes kizárásával.
	 *
	 * @param GET/POST identifier_name: emp_id/employee_id/employee_contract_id az itt megadott
	 * azonosítót generáljuk.
	 */
	public function actionIncreaseGlobalIdentifier() {
		$identifier_name = requestParam('identifier_name');

		if (empty($identifier_name) || !in_array($identifier_name, ["emp_id","employee_id","employee_contract_id",])) {
			$status = [
				"identifier_id" => 0,
			];
			echo json_encode($status);

			return false;
		}

		$id = App::getIncreasedGlobalIdentifier($identifier_name);

		$status = [
			"identifier_id" => $id,
		];
		echo json_encode($status);
	}


	private function extractDialogInputsFromRequest(string $prefix, string $except, array $fields): array
	{
		$response = [];
		$found = array_fill_keys($fields, false);
		$dialogsInputs = array_filter($_REQUEST, function($key) use ($prefix, $except) {
			return strpos($key, $prefix) === 0
				&& $key !== $prefix . $except
				&& is_array($_REQUEST[$key]);
		}, ARRAY_FILTER_USE_KEY);
		foreach ($dialogsInputs as $dialogInputs) {
			foreach ($fields as $field) {
				if (!$found[$field] && isset($dialogInputs[$field])) {
					$response[$field] = $dialogInputs[$field];
					$found[$field] = true;
				}
			}
			if (!in_array(false, $found, true)) {
				break;
			}
		}
		return $response;
	}
}

?>

<?php
Yang::loadComponentNamespaces('Grid2Core');

class Grid2Controller extends Controller
{
	use Grid2; // Base functions
	use Grid2Actions; // Yii Actions
	use Grid2Skeleton; // Base DHTMLXGrid functions
	use Grid2Data; // Grid data & Dropdown data fetch
	use Grid2DataGen; // Data generator helpers
	use Grid2Form; // Form render
	use Grid2Export; // Export base functions
	use Grid2ExportPDF; // Export PDF
	use Grid2ExportPDFFromHTML; // Export PDF from HTML with dompdf
	use Grid2ExportExcel; // Export Excel
	use Grid2StatusButtons; // Status buttons
	use Grid2JasperReports; // Export with Jasper Reports
	use Grid2PreDefinedBars;	// Defined

	/**
	 * Grid2 constants
	 */
	const G2BC_QUERY_MODE_MODEL		= 0;
	const G2BC_QUERY_MODE_SQL		= 1;
	const G2BC_QUERY_MODE_ARRAY		= 2;

	const G2BC_DIALOG_MODE_ADD		= 0; // add
	const G2BC_DIALOG_MODE_MOD		= 1; // edit
	const G2BC_DIALOG_MODE_DET		= 2; // details

	const G2BC_FORM_MODE_SEARCHBAR	= 0;
	const G2BC_FORM_MODE_DIALOG		= 1;

	const G2BC_SEARCH_EMPLOYEE_WITH_FROM_TO				= 0;
	const G2BC_SEARCH_EMPLOYEE_WITH_DATE				= 1;
	const G2BC_SEARCH_EMPLOYEE_WITH_YEARMONTH			= 2;
	const G2BC_SEARCH_EMPLOYEE_WITH_YEAR				= 3;
	const G2BC_SEARCH_EMPLOYEE_WITH_Evaluation_Period	= 4;
	const G2BC_SEARCH_COG2_WITH_HIDDENYEARMONTH			= 5;
	const G2BC_SEARCH_EMPLOYEE_WITH_DATETIME			= 6;

	const G2BC_SEARCH_WITH_COMPANY						= 1;
	const G2BC_SEARCH_WITH_PAYROLL						= 2;
	const G2BC_SEARCH_WITH_WORKGROUP					= 4;
	const G2BC_SEARCH_WITH_UNIT							= 8;
	const G2BC_SEARCH_WITH_COMPANY_ORG_GROUP1			= 16;
	const G2BC_SEARCH_WITH_COMPANY_ORG_GROUP2			= 32;
	const G2BC_SEARCH_WITH_COMPANY_ORG_GROUP3			= 64;
	const G2BC_SEARCH_WITH_COST							= 128;
	const G2BC_SEARCH_WITH_COSTCENTER					= 256;
	const G2BC_SEARCH_WITH_EMPLOYEECONTRACT				= 512;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION1			= 1024;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION2			= 2048;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION3			= 4096;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION4			= 8192;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION5			= 16384;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION6			= 32768;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION7			= 65536;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION8			= 131072;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION9			= 262144;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION10		= 524288;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION1	= 1048576;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION2	= 2097152;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION3	= 4194304;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION4	= 8388608;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION5	= 16777216;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION6	= 33554432;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION7	= 67108864;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION8	= *********;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION9	= *********;
	const G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION10	= *********;
	const G2BC_SEARCH_WITH_FLEX_GROUPS					= 1073741824;

	const G2BC_SEARCH_WITH_DEFAULT_GROUP_FILTER			= 527; //Company + Payroll + Workgroup + Unit + Employee

	protected $LAGridDB			= null;
	protected $LAGridRights		= null;
	protected $nodePdfRowNumber = 40; // sorok száma oldalanként a PDF-ben
	public $layout = null;

	public function __construct($controllerID, $module = null) {
		$this->setControllerID($controllerID);

		$this->LAGridDB			= new LAGridDB($this);
		$this->LAGridRights		= new LAGridRights($this);

		/*
		 * Ne csak js-ből ellenőrizzük le hogy be van-e lépve hanem oldaltöltésnél is, így nem kell megvárni a script
		 * futását
		 * TODO:: KIVENNI HA GONDOT OKOZNA AZ OLDALAKNÁL
		 * GONDOT OKOZOTT, KIVETTEM - TA (2019.12.02.)
		 * */
		/*if (Yang::isGuest()){ //Nincs belépve
			$this->redirect(baseURL().'/login/login');
		}*/

		parent::__construct($controllerID, $module);
	}

	public static function dhtmlxScripts() {
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/dhtmlxcommon.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/_mod_dhtmlxSuite/dhtmlxGrid/codebase/dhtmlxgrid.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxCombo/codebase/dhtmlxcombo.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/dhtmlxgridcell.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/_mod_dhtmlxSuite/dhtmlxGrid/codebase/excells/dhtmlxgrid_excell_sub_row.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/excells/dhtmlxgrid_excell_combo.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/excells/dhtmlxgrid_excell_link.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_validation.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_export.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/_mod_dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_pgn.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_srnd.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/_mod_dhtmlxSuite/dhtmlxDataProcessor/codebase/dhtmlxdataprocessor.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxConnector/codebase/connector.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_math.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_splt.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/_mod_dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_filter.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_group.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxColorPicker/codebase/dhtmlxcolorpicker.js');
		Yang::registerScriptFile(baseURL()."/".EXT_FOLDER.'/_mod_dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_mcol.js');

		echo '<link rel="stylesheet" type="text/css" href="'.baseURL()."/".EXT_FOLDER.'/_mod_dhtmlxSuite/dhtmlxGrid/codebase/ext/dhtmlxgrid_pgn_bricks.css" />';
		echo '<link rel="stylesheet" type="text/css" href="'.baseURL()."/".EXT_FOLDER.'/_mod_dhtmlxSuite/dhtmlxGrid/codebase/dhtmlxgrid.css" />';
		echo '<link rel="stylesheet" type="text/css" href="'.baseURL()."/".EXT_FOLDER.'/dhtmlxSuite/dhtmlxCombo/codebase/dhtmlxcombo.css" />';
		echo '<link rel="stylesheet" type="text/css" href="'.baseURL()."/".EXT_FOLDER.'/_mod_dhtmlxSuite/dhtmlxGrid/codebase/skins/dhtmlxgrid_dhx_terrace.css" />';

		echo '
			<script type="text/javascript">
				dhtmlXGridObject.prototype.i18n.paging={
					results:"'.Dict::getValue("dhtmlxGrid_results").'",
					records:"'.Dict::getValue("dhtmlxGrid_records").'",
					to:"'.Dict::getValue("dhtmlxGrid_to").'",
					page:"'.Dict::getValue("dhtmlxGrid_page").'",
					perpage:"'.Dict::getValue("dhtmlxGrid_perpage").'",
					first:"'.Dict::getValue("dhtmlxGrid_first").'",
					previous:"'.Dict::getValue("dhtmlxGrid_previous").'",
					found:"'.Dict::getValue("dhtmlxGrid_found").'",
					next:"'.Dict::getValue("dhtmlxGrid_next").'",
					last:"'.Dict::getValue("dhtmlxGrid_last").'",
					of:"'.Dict::getValue("dhtmlxGrid_of").'",
					notfound:"'.Dict::getValue("dhtmlxGrid_notfound").'"
				}
			</script>
		';
	}
}


<?php

'yii2-only`;

	namespace app\components\Grid2\Export;
	use PHPExcel_Worksheet_PageSetup;
	use TCPDF;
	use app\components\Grid2\Grid2Controller;
	use Yang;

`/yii2-only';


trait Grid2ExportPDF
{
	protected function generatePDF($filename = null) {
		ini_set('max_execution_time', 600);
		ini_set('memory_limit', '512M');

		error_reporting(E_ERROR);

		$this->G2BInit();
		$this->setColumnProperties();

		$modelName = $this->LAGridDB->getModelName();

		$attributeLabels = array();

		if ($this->LAGridDB->getMode() === Grid2Controller::G2BC_QUERY_MODE_MODEL && class_exists($modelName)) {
			$attributeLabels = $modelName::model()->attributeLabels();
		} else if ($this->LAGridDB->getMode() === Grid2Controller::G2BC_QUERY_MODE_SQL
				 || $this->LAGridDB->getMode() === Grid2Controller::G2BC_QUERY_MODE_ARRAY) {
			$attributeLabels = $this->attributeLabels();
		}

		$title = "TTWA Report";

		if (empty($filename)) {
			$filename = $this->getExportFileName();
		}

		$filename .= date("_Y.m.d_H.i.s");
		$filename .= ".pdf";

		$columns = $this->columns();

		$searchBar = requestParam('searchInput');
		$results = $this->fetchGridData($searchBar);

		$dropdownData = array();

		$colInd = 0;
		$rowInd = 1;

		require_once(Yang::getBasePath() . '/extensions/tcpdf/tcpdf.php');

		$pdf = new TCPDF(PHPExcel_Worksheet_PageSetup::ORIENTATION_LANDSCAPE, 'pt', PHPExcel_Worksheet_PageSetup::PAPERSIZE_A4, true, 'UTF-8', false);

		$pdf->SetTitle($title);
		$pdf->SetAuthor($title);
		$pdf->SetSubject($title);
		$pdf->SetKeywords($title);
		$pdf->SetCreator($title);

		$pdf->SetFont('freeserif');

		$pdf->setFontSubsetting(FALSE);

		$pdf->setPrintHeader(FALSE);

		$pdf->setPrintFooter(FALSE);

		$pdf->AddPage();

		$offset = 20;

		$pageWidth = $pdf->getPageWidth() - (2*$offset); // offset
		$pageHeight = $pdf->getPageHeight() - (2*$offset);
		$headerHeight = 45;
		$columnProperties = $this->getColumnProperties();
		$columnCount = count(explode(";",$columnProperties["columns"]));
		$columnWidth = round($pageWidth/$columnCount);

		$pdf->SetFillColor(209, 229, 254);

		$lineStyle = Array('width' => 0.1, 'cap' => 'round', 'join' => 'round', 'dash' => '0', /*border: 'color' => array(209, 229, 254)*/);
		$pdf->SetLineStyle($lineStyle);

		$pdf->SetMargins($offset, $offset);
//		$pdf->SetAutoPageBreak(TRUE);
//		$pdf->SetFooterMargin($offset);
		$pdf->SetAutoPageBreak(TRUE, 0);

		$pdf->setX($offset); // offset
		$pdf->setY($offset, false);

		$heightMax = 0;

		foreach ($columns as $column => $attrs) {
			if ( $this->isColumnVisibleInGrid($attrs) ) {
				$title = isset($attributeLabels[$column])?$attributeLabels[$column]:$column;

				$linesNum = $pdf->getNumLines($title,$columnWidth);
				// calculation cell height
				$height = $linesNum*$pdf->getFontSize() + $pdf->getFontSize()*0.5*($linesNum + 1);

				if ($height > $heightMax) {
					$heightMax = $height;
				}
			}
		}

		foreach ($columns as $column => $attrs) {
			if ( $this->isColumnVisibleInGrid($attrs) ) {
				$title = isset($attributeLabels[$column])?$attributeLabels[$column]:$column;

				// http://www.tcpdf.org/doc/code/classTCPDF.html#aa81d4b585de305c054760ec983ed3ece
				$newLine = 0;
				if ($colInd === $columnCount-1) {
					$newLine = 1;
				}

				$resetHeight = true;

				$pdf->MultiCell($columnWidth, $heightMax, $title, 1, 'C', 1, $newLine, '', '', $resetHeight, 0, false, true, $heightMax, 'M', false);

				if (isset($attrs["col_type"]) && ($attrs["col_type"] === "combo" || $attrs["col_type"] === "auto")) {
					$dropdownData[$colInd] = $this->fetchDropdownData($colInd, "GRID");
				}

				$colInd++;
			}
		}

		$rowInd++;

		$pdf->SetFillColor(255, 255, 255);

		$sumHeight = 0;
		$sumHeight += $heightMax;

		foreach ($results['data'] as $ind => $properties) {
			$dataColumns = $properties["columns"];

			$heightMax = 0;

			$colInd = 0;

			foreach ($columns as $column => $attrs) {
				if ( $this->isColumnVisibleInGrid($attrs) ) {
					$value = "";

					if (isset($dataColumns[$column])) {
						if (isset($attrs["col_type"]) && ($attrs["col_type"] === "combo" || $attrs["col_type"] === "auto")) {
							if (isset($dropdownData[$colInd]["data"])) {
								$values = $dropdownData[$colInd]["data"];
								if (isset($values[$dataColumns[$column]['data']]["data"])) {
									$value = $values[$dataColumns[$column]['data']]["data"];
								}
							}
						} else {
							$value = $dataColumns[$column]['data'];
						}
					}

					$linesNum = $pdf->getNumLines($value,$columnWidth);
					// calculation cell height
					$height = $linesNum*$pdf->getFontSize() + $pdf->getFontSize()*0.5*($linesNum + 1);

					if ($height > $heightMax) {
						$heightMax = $height;
					}

					$colInd++;
				}
			}

			$sumHeight += $heightMax;

			if ($sumHeight > $pageHeight) {
				$sumHeight = 0;
				$sumHeight += $heightMax;

				$pdf->AddPage();
			}

			$colInd = 0;

			foreach ($columns as $column => $attrs) {
				if ( $this->isColumnVisibleInGrid($attrs) ) {
					$value = "";

					if (isset($dataColumns[$column])) {
						if (isset($attrs["col_type"]) && ($attrs["col_type"] === "combo" || $attrs["col_type"] === "auto")) {
							if (isset($dropdownData[$colInd]["data"])) {
								$values = $dropdownData[$colInd]["data"];
								if (isset($values[$dataColumns[$column]['data']]["data"])) {
									$value = $values[$dataColumns[$column]['data']]["data"];
								}
							}
						} else {
							$value = $dataColumns[$column]['data'];
						}
					}

					$newLine = 0;
					if ($colInd === $columnCount-1) {
						$newLine = 1;
					}

					$resetHeight = true;

					$pdf->MultiCell($columnWidth, $heightMax, $value, 1, 'C', 1, $newLine, '', '', $resetHeight, 0, false, true, $heightMax, 'M', false);

					$colInd++;
				}
			}

			$rowInd++;
		}

		$pdf->Output(Yang::getBasePath().'/../webroot/reports/'.$filename, 'F');

		$url = baseURL().'/reports/'.$filename;

		return $url;
	}
}

?>
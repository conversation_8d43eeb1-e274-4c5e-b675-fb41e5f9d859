<?php #yii2: done

'yii2-only`;

	namespace app\components\Grid2\Export;
	use app\components\API\WriteUserEvent;
	use app\components\Grid2\Grid2Controller;
	use Yang;

`/yii2-only';


trait Grid2JasperReports
{
	protected $jasperReportSettings = [];

	protected $jasperReportHeadFontColor = '#FEFEFF';

	protected $jasperReportDataFontColor = '#575b66';

	protected $jasperReportBlueStyle = '
			<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
					<topPen lineWidth="0.5" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineColor="#000000"/>
				</box>
			</style>
			<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
					<topPen lineWidth="0.5" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineColor="#000000"/>
				</box>
			</style>
			<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
					<topPen lineWidth="0.5" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineColor="#000000"/>
				</box>
			</style>
			<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
					<topPen lineWidth="0.5" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineColor="#000000"/>
				</box>
			</style>
			<style name="Table 1_CH" mode="Opaque" backcolor="#039BE5">
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
					<topPen lineWidth="0.5" lineColor="#50b9e6"/>
					<leftPen lineWidth="0.5" lineColor="#50b9e6"/>
					<bottomPen lineWidth="0.5" lineColor="#50b9e6"/>
					<rightPen lineWidth="0.5" lineColor="#50b9e6"/>
				</box>
			</style>
			<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
					<topPen lineWidth="0.5" lineColor="#e6e6e6"/>
					<leftPen lineWidth="0.5" lineColor="#e6e6e6"/>
					<bottomPen lineWidth="0.5" lineColor="#e6e6e6"/>
					<rightPen lineWidth="0.5" lineColor="#e6e6e6"/>
				</box>
			</style>
	';

    /**
     * Init curl connection
     * @param string $url
     * @param array $header
     * @param mixed $request
     * @param integer $returnTransfer
     * @return mixed
     */
    private function createCurlConnection($url, $header, $request, $returnTransfer = 0)
    {
        $curlSslVerifyPeer = (int)App::getSetting('curlSslVerifyPeer');

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, $returnTransfer);
        curl_setopt($curl, CURLOPT_HEADER , false);
        curl_setopt($curl, CURLOPT_POST, false);
        curl_setopt($curl, CURLOPT_POSTFIELDS, false);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($curl, CURLOPT_TIMEOUT, 300);

        // Special setting for imperfect SSL handling
        if ($curlSslVerifyPeer === 0) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }

        $resultOfCurl = $this->executeCurl($curl,$returnTransfer);

        curl_close($curl);

        return $resultOfCurl;
    }

    /**
     * Execute curl
     * @param [type] $curl
     * @param int $returnTransfer
     * @return mixed
     */
    private function executeCurl($curl, $returnTransfer)
    {
        $data = curl_exec($curl);

        if ($returnTransfer && $data !== false) {

            return $data;
        } else if (!$returnTransfer && $data !== false) {

            return true;
        } else {

            return false;
        }
    }

    /**
     * Set report
     *
     */
	public function actionSetReport()
    {

        $host     = requestParam('host');
        $callback = requestParam('callback');
        $url      = $host . 'setReport?callback=' . $callback;
        $header   = ['Expect:', 'Content-Type: application/json'];
        $request  = json_encode(["callback" => $callback]);
        $response = $this->createCurlConnection($url, $header, $request, 1);

        echo $response;

		return $response;
    }

    /**
     * Run report
     *
     */
    public function actionRunReport()
    {

        $host     = requestParam('host');
        $reportId = requestParam('report_id');
        $callback = requestParam('callback');
        $url      = $host . 'runReport?report_id=' . $reportId . '&callback=' . $callback;
        $header   = ['Expect:', 'Content-Type: application/json'];
        $request  = json_encode(["report_id" => $reportId, "callback" => $callback]);
        $response = $this->createCurlConnection($url, $header, $request, 1);

        echo $response;
    }

    /**
     * Run report
     *
     */
    public function actionGetReport()
    {

        $host     = requestParam('host');
        $reportId = requestParam('report_id');
        $callback = requestParam('callback');
        $url      = $host . 'getReport?report_id=' . $reportId . '&callback=' . $callback;
        $header   = ['Expect:', 'Content-Type: application/json'];
        $request  = json_encode(["report_id" => $reportId, "callback" => $callback]);
        $response = $this->createCurlConnection($url, $header, $request, 1);
        $this->getReportResponse($response);
        echo $response;
    }

    private function getReportResponse($response)
    {
        $responseArray = substr($response, strpos($response, '('));
        $responseArray = substr($responseArray, 0, -1);
        $responseArray = trim($responseArray);
        $responseArray = trim($responseArray,'()');
        $responseArray = json_decode($responseArray);
        if (!is_null($responseArray) && $responseArray->{'report_status'} == 'DONE') 
        {
            $reportId = $responseArray->{'report_id'};
            $reportStatus = $responseArray->{'report_status'};
            $reportPath = $responseArray->{'report_path'};
            $SQL = "UPDATE `report`
                    SET
                        `report_path` = '$reportPath',
                        `report_status` = '$reportStatus'
                    WHERE
                        `report_id` = '$reportId'
                        ";
            dbExecute($SQL);
        }
    }

	public function actionInitJReport()
    {
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['index' => '1']);
		$this->layout = 'ajax';

		$gridID = requestParam('gridID');

		$database = Yang::getParam('dbName');

		$host = Yang::getParam('serverUrl');

		$reportId = requestParam("reportId");
		$reportMode = requestParam("reportMode");
		$fileID = requestParam("fileID");
		$fileName = requestParam("fileName");
		$fileType = requestParam("fileType");
		$searchInput = requestParam("searchInput");
		$orientation = requestParam("orientation");

        $this->setReportOrientation($orientation);

		$reportConnection = null;
		//if ($reportMode === "JASPER") {
			$reportConnection = Yii::app()->reportDb;
		//}

		$gridLabels = $this->getGridLabels();

		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($gridLabels[$gridID])) {
				$gridLabels = $gridLabels[$gridID];
			} else {
				$gridLabels = [];
			}
		}

		foreach ($gridLabels as $column => $header) {
			$gridLabels[$column] = htmlentities($header);
		}

		$reportParametersArr = [];
		$reportParametersArr["filters"] = $searchInput;
		$reportParametersArr["labels"] = $gridLabels;

		$reportParameters = html_entity_decode(json_encode($reportParametersArr));

		$reportParameters = "{}";

		$reportDir = requestParam("reportDir");

		$user_id = userID();
		$controllerId = $this->getControllerID();
		$pageTitle = $this->getControllerPageTitle();

		$orientation = $this->getReportOrientation();

		$reportData = [];

		//if ($reportMode === "JASPER") {
			$reportGetSQL = "
				SELECT * FROM `report` WHERE `report_id` = '{$reportId}'
			";

			$reportData = $reportConnection->createCommand($reportGetSQL)->queryAll();
		//}

		$report_created = '';
		if (isset($reportData[0]['report_created'])) {
			$report_created = $reportData[0]['report_created'];
		} else {
			$report_created = date("Y-m-d H:i:s");
		}

		$saveReportSQL = "
			INSERT INTO `report` VALUES (NULL, '{$user_id}', '{$reportId}', '{$reportMode}', '{$orientation}', '{$fileID}', '{$controllerId}', '{$pageTitle}', '{$fileName}', '{$fileType}', NULL, '{$report_created}', 'WAIT', '{$user_id}', NOW(), NULL, NULL)
		";

		dbExecute($saveReportSQL);

		$jrxml = "";
		$on_before_query_sql = "";
		if ($reportMode === "JASPER") {
			$jrxml = $this->generateJRXMLPDF($searchInput);

			$jrxml = str_replace("{dateTime}", $report_created, $jrxml);
		}

		if ($this->LAGridDB->hasOnBeforeQuerySQL()) {
			$on_before_query_sql = $this->LAGridDB->getOnBeforeQuerySQL();
		}

		$reportInitSQL = "
			UPDATE `report` SET `orientation` = '{$orientation}', `file_name` = '{$fileName}', `file_type` = '{$fileType}', `database` = '{$database}', `host` = '{$host}', `fs_file_id` = '{$fileID}', `jrxml` = '{$jrxml}', `on_before_query_sql` = '{$on_before_query_sql}', `report_parameters` = '{$reportParameters}'" . ($reportMode === "JASPER"?", `report_status` = 'WAIT'":"") . " WHERE `report_id` = '{$reportId}'
		";

		//if ($reportMode === "JASPER") {
			$reportConnection->createCommand($reportInitSQL)->execute();
		//}

		$resp = [
			"status" => "ok",
		];

		echo json_encode($resp);
	}

	protected function initJRSettings() {
		$marginTop		= 30;
		$marginBottom	= 30;
		$marginLeft		= 30;
		$marginRight	= 30;

		$pageWidth		= 595;
		$pageHeight		= 842;
		$orientationString = 'orientation="Portrait"'; // Landscape

		$orientation = $this->getReportOrientation(); // landscape, portrait

		if ($orientation === 'landscape') {
			$pageWidth = 842;
			$pageHeight = 595;

			$orientationString = 'orientation="Landscape"';
		}

		$this->jasperReportSettings["reportEncoding"]      = Yang::getParam('reportEncoding');
		$this->jasperReportSettings["marginTop"]			= $marginTop;
		$this->jasperReportSettings["marginBottom"]			= $marginBottom;
		$this->jasperReportSettings["marginLeft"]			= $marginLeft;
		$this->jasperReportSettings["marginRight"]			= $marginRight;
		$this->jasperReportSettings["pageWidth"]			= $pageWidth;
		$this->jasperReportSettings["pageHeight"]			= $pageHeight;
		$this->jasperReportSettings["orientationString"]	= $orientationString;
		$this->jasperReportSettings["realPageWidth"]		= $pageWidth - $marginLeft - $marginRight;
		$this->jasperReportSettings["realPageHeight"]		= $pageHeight - $marginTop - $marginBottom;
	}

	protected function getJRSettings() {
		return $this->jasperReportSettings;
	}

	protected function generateJRXMLPDF($filter) {
		$this->G2BInit();

		$this->initJRSettings();
		$settings = $this->getJRSettings();

		$JRXML = '';

		$JRXML .= '<?xml version="1.0" encoding="'.$settings["reportEncoding"].'"?>
		<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="AllAccounts" pageWidth="'.$settings["pageWidth"].'" pageHeight="'.$settings["pageHeight"].'" '.$settings["orientationString"].' whenNoDataType="AllSectionsNoDetail" columnWidth="'.$settings["realPageWidth"].'" leftMargin="'.$settings["marginLeft"].'" rightMargin="'.$settings["marginRight"].'" topMargin="'.$settings["marginTop"].'" bottomMargin="'.$settings["marginBottom"].'" isSummaryWithPageHeaderAndFooter="true" uuid="25e79335-e837-409f-965a-37134e9a3299">
			<style name="Sans_Normal" isDefault="true" fontName="DejaVu Sans" fontSize="10"/>
			<style name="Sans_Bold" fontName="DejaVu Sans" fontSize="10" isBold="true"/>
			<style name="Sans_Italic" fontName="DejaVu Sans" fontSize="10" isItalic="true"/>';

		$JRXML .= $this->jasperReportBlueStyle;

		// QUERY & FIELDS
		$gridID = requestParam('gridID');

		$columns = $this->columns();

		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($columns[$gridID])) {
				$columns = $columns[$gridID];
			} else {
				$columns = array();
			}
		}

		$gridLabels = $this->getGridLabels();

		$sql = "";

		if ($this->LAGridDB->getMode() === Grid2Controller::G2BC_QUERY_MODE_MODEL) {
			$model = null;
			$crit = null;
			$sql = "";

			$this->generateModelCrit($filter, $model, $crit, $sql);
		} else if ($this->LAGridDB->getMode() === Grid2Controller::G2BC_QUERY_MODE_SQL) {
			$sql = "";

			$this->generateSQL($filter, $sql, $gridID, true);
		}

//		$sql = str_replace(["\r\n", "\n"], [" ", " "], $sql);
		$sql = str_replace("'", "\'", $sql);

		$JRXML .= '
			<subDataset name="Table Dataset 1" uuid="2e44a5a2-70fd-45ed-b232-a71049026459">
				<queryString language="SQL">
					<![CDATA['.$sql.']]>
				</queryString>
		';

		$columnCount = 0;

		$dropdownData = [];

		foreach ($columns as $column => $attrs) {
			if ( $this->isColumnVisibleInExport($attrs) ) {
				$JRXML .= '<field name="'.$column.'" class="java.lang.String"/>';

				if (isset($attrs["col_type"]) && ($attrs["col_type"] === "combo" || $attrs["col_type"] === "auto")) {
					$dropdownData[$columnCount] = $this->fetchDropdownData($columnCount, "GRID", /*$autoComplete =*/ false, /*$search =*/ null, /*$parameters =*/ array(), /*$columns =*/ array(), $gridID);
				}

				$columnCount++;
			}
		}

		$JRXML .= '
				<group name="Group1"/>
			</subDataset>
		';

		$width = $settings["realPageWidth"];

		$halfWidth = round($width/2);

		if ($this->isReportHeaderEnabled()) {
			$JRXML .= '
				<pageHeader>
					<band height="25" splitType="Stretch">';
						if ($this->isReportHeaderLineEnabled()) {
							$JRXML .= '<line>
								<reportElement uuid="f4c6a6c4-5b89-4225-9d25-e98b0c74ed6f" key="line-2" x="0" y="0" width="'.$width.'" height="1"/>
								<graphicElement fill="Solid">
									<pen lineWidth="1.0" lineStyle="Solid"/>
								</graphicElement>
							</line>';
						}
						$JRXML .= '<textField isBlankWhenNull="true">
							<reportElement uuid="6fab8474-3957-4d19-9a31-2b63212d3b43" key="textField-1" x="0" y="0" width="'.$halfWidth.'" height="24"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Left" verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA['.$this->getReportHeaderLeftContent().']]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement uuid="6fab8474-3957-4d19-9a31-2b63212d3b44" key="textField-2" x="'.$halfWidth.'" y="0" width="'.$halfWidth.'" height="24"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA['.$this->getReportHeaderRightContent().']]></textFieldExpression>
						</textField>';
					$JRXML .= '</band>
				</pageHeader>
			';
		}

		if ($this->isReportFooterEnabled()) {
			$JRXML .= '
				<pageFooter>
					<band height="25" splitType="Stretch">';
						$JRXML .= '<textField isBlankWhenNull="true">
							<reportElement uuid="6fab8474-3957-4d19-9a31-2b63212d3b43" key="textField-1" x="0" y="0" width="'.$halfWidth.'" height="24"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Left" verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA['.$this->getReportFooterLeftContent().']]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement uuid="6fab8474-3957-4d19-9a31-2b63212d3b44" key="textField-2" x="'.$halfWidth.'" y="0" width="'.$halfWidth.'" height="24"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA['.$this->getReportFooterRightContent().']]></textFieldExpression>
						</textField>';
						if ($this->isReportFooterLineEnabled()) {
							$JRXML .= '<line>
								<reportElement uuid="f4c6a6c4-5b89-4225-9d25-e98b0c74ed6f" key="line-2" x="0" y="24" width="'.$width.'" height="1"/>
								<graphicElement fill="Solid">
									<pen lineWidth="1.0" lineStyle="Solid"/>
								</graphicElement>
							</line>';
						}
					$JRXML .= '</band>
				</pageFooter>
			';
		}

		$columnWidth = round($width/$columnCount);
		$actualWidth = 0;

		$columnNo = 0;

		// DATATABLE
		$JRXML .= '
			<summary>
				<band height="149" splitType="Stretch">
					<componentElement>
						<reportElement key="table" style="Sans_Italic" stretchType="RelativeToBandHeight" x="0" y="0" width="'.$width.'" height="149" uuid="5416263b-5be2-4f24-b86a-9f60e142ef97"/>
						<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
							<datasetRun subDataset="Table Dataset 1" uuid="4b8f3479-8007-4959-a908-e5b4a1bb74cc">
								<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
							</datasetRun>
							<jr:columnGroup width="'.$width.'" uuid="ebd8f5ec-8e68-4f34-99ab-cb3dbd88aebe">';
								foreach ($columns as $column => $attrs) {
									if ( $this->isColumnVisibleInExport($attrs) ) {
										$label = $column;

										if (isset($gridLabels[$column])) {
											$label = $gridLabels[$column];
										}

										if (isset($attrs["report_width"]) && $attrs["report_width"]) {
											$columnWidth = round($width * ($attrs["report_width"]/100));
										}

										if ($columnNo === $columnCount-1) {
											$columnWidth = $width - $actualWidth;
										} else {
											$actualWidth += $columnWidth;
										}

										if ($columnWidth < 0) {
											$columnWidth = 0;
										}

										$JRXML .= '<jr:column width="'.$columnWidth.'" uuid="1f8902be-ee8f-4fe1-94e9-ef4f8151c5b7">
											<jr:columnHeader style="Table 1_CH" height="32">
												<textField>
													<reportElement x="0" y="0" width="'.$columnWidth.'" height="32" uuid="6c7ff139-0d16-4db0-a5d3-5239fc3bb068" forecolor="'.$this->jasperReportHeadFontColor.'" />
													<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2" />
													<textFieldExpression><![CDATA["'.$label.'"]]></textFieldExpression>
												</textField>
											</jr:columnHeader>
											<jr:detailCell style="Table 1_TD" height="16">
												<textField>
													<reportElement x="0" y="0" width="'.$columnWidth.'" height="16" uuid="a8023efe-559b-446c-abf6-567c1f9273d4" forecolor="'.$this->jasperReportDataFontColor.'" />
													<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2" />
													<textFieldExpression><![CDATA[$F{'.$column.'}]]></textFieldExpression>
												</textField>
											</jr:detailCell>
										</jr:column>';

										$columnNo++;
									}
								}
							$JRXML .= '</jr:columnGroup>
						</jr:table>
					</componentElement>
				</band>
			</summary>
		';

		$JRXML .= '</jasperReport>';

		return $JRXML;
	}
}

?>
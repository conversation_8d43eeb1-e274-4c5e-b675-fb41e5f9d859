<?php

trait Grid2ExportExcel
{
	protected $controller_id;
	protected $showFilter = false;

    protected array $xlsxExportEmployeeControlController = [
        'employeecontrol'
    ];
	protected $xlsxWriterSecondLineOptionDisable = [
		'wfm/annualOvertimeReport',
		'customers/bridgestone/overtimeExtendedReport'
	];

	// CSV-be tömbből menjen az adat (memory overflow lehet), egyébként stringbe fűzi
	protected $csvExportArrayControllers = [
		"customers/tebekescsaba/payrollTransferTeBekescsaba",
	];

	//CSV karakter kódolás feltétel miatt
	protected $csvExportArrayISO = [
		"customers/felina/reportWorktimeData",
		"customers/tebekescsaba/payrollTransferTeBekescsaba",
	];

    protected $xlsxWriterHeaderDeleteFirst = array(
		'customers/carrier/reportAbsenceAndOvertime',
        'customers/carrier/reportFactoryWorktimeClosing',
        'csm/reportEmployeeCompetency',
        'headCountDistribution',
	);

	protected function generateExcel($type, $filename = null, $hasMultiGridMode = false) {
		ini_set('max_execution_time', 7200);
        $memoriaLimit = App::getSetting("gridexcelexport") < 1024 ? '1024M' : App::getSetting("gridexcelexport").'M';
        ini_set('memory_limit', $memoriaLimit);

		$this->controller_id = Yii::app()->controller->id;

		$this->showFilter = (bool)App::getSetting("show_filter_in_excel_header");

		return $this->generateExcelXlsWriter($type, $filename, $hasMultiGridMode);

	}

	protected function generateExcelXlsWriter($type, $filename = null, $hasMultiGridMode = false){
		$this->G2BInit();
		$gridID = requestParam('gridID');
		$modelName = $this->LAGridDB->getModelName($gridID);
		$attributeLabels = [];

		if ($this->LAGridDB->getMode() === Grid2Controller::G2BC_QUERY_MODE_MODEL && class_exists($modelName)) {
			$attributeLabels = $modelName::model()->attributeLabels();
		} else {
			$attributeLabelsClass = [];

			if (method_exists($this, "attributeLabels") && count($this->attributeLabels())) {
				$attributeLabelsClass = $this->attributeLabels();
			}
			$attributeLabelsModel = [];
			if (!is_null($modelName) && class_exists($modelName)) {
				$attributeLabelsModel = $modelName::model()->attributeLabels();
			}
			$attributeLabels = Yang::arrayMerge($attributeLabelsModel, $attributeLabelsClass);
		}


		$title = "TTWA Report";
		$title2 = "TTWA Report";
		require_once Yang::getBasePath().'/extensions/XLSXWriter/xlsxwriter.class.php';
		$objphpwriter = new XLSXWriter();
		$objphpwriter->setAuthor($title2);
		if (empty($filename)) {
			$filename = $this->getExportFileName();
		}

		$searchBar = requestParam('searchInput');
		$results = $this->fetchGridData($searchBar, false, true);
		$columns = $this->columns();
		if ($hasMultiGridMode && !empty($gridID)) {
            $columns = isset($columns[$gridID]) ? $columns[$gridID] : [];
		}
		if ($hasMultiGridMode && !empty($gridID)) {
            $attributeLabels = isset($attributeLabels[$gridID]) ? $attributeLabels[$gridID] : [];
		}

        if (isset($results['defaultFileName'])) {
            $filename = $results['defaultFileName'] . '.xlsx';
        } else {
            $filename .= date('_Y.m.d_H.i.s') . '.xlsx';
        }

		$wfmAnnualOvertimeReport = $this->controller_id == 'wfm/annualOvertimeReport';

        $values_rows = [];
		$dropdownData = [];
		$colInd = 0;
		$header_row_type = [];
		$headerstyle_widths = [];
		$values_widths = [];
		$headers = $this->headers();
		$rowSpanPositionUse = false;
		$maxRowSpan = 0;
		$excelExportStylePlus = [];
        $footerDatas = [];
        $rowSpanColIndex = false;
		if (count($headers) > 0)
		{
            if (in_array($this->controller_id, $this->xlsxWriterHeaderDeleteFirst))
            {
                $rowSpanColIndex = true;
                for($i = 0; $i < count($headers) - 1; $i++)
                {
                    $headers[$i] = $headers[$i+1];
                }
                unset($headers[count($headers)-1]);
            }

			// Check for row span position use & max row span nr.
			foreach ($headers as $key => $value)
			{
				foreach ($value as $key2 => $value2)
				{
					if (isset($value2["row_span"])) {
						if ((int)$value2["row_span"] > $maxRowSpan) {
							$maxRowSpan = (int)$value2["row_span"];
						}
						if (isset($value2["row_span_position"])) {
							$rowSpanPositionUse = true;
						}
					}
				}
			}

            $footerDatas = $headers[0] ?? [];
			foreach ($headers as $key => $value)
			{
				$colInd = 0;
				foreach ($value as $key2 => $value2)
				{
					$title = isset($value2['label_text'])?$value2['label_text']:"";
					$title = str_replace(["<BR>","<br/>",], ["\r\n","\r\n",], $title);
					if (isset($value2["col_type"]) && ($value2["col_type"] === "combo" || $value2["col_type"] === "auto"))
					{
                        $dropdownData[$colInd] = $hasMultiGridMode
                            ? $this->fetchDropdownData($colInd, "GRID", false, null, array(), array(), $gridID)
                            : $this->fetchDropdownData($colInd, "GRID");
                        ;
					}

                    $columnsType = $this->setExcelColumnsType($value2);

					// Row span position filling
					if ($rowSpanPositionUse)
					{
						if (isset($value2["row_span_position"]) && $value2["row_span"] > 0)
						{
							for ($i = 0; $i < $maxRowSpan; $i++)
							{
								$header_row_type[$i][$value2["row_span_col_index"]] = [
									"type" 		=> $columnsType,
									"row_span" 	=> ($wfmAnnualOvertimeReport) ? null : ($value2["row_span"] ?? 1),
									"col_span" 	=> ($wfmAnnualOvertimeReport) ? ($value2["col_span"] ?? null) : ($value2["col_span"] ?? 1),
									"title" 	=> (((int)$i + 1) == $value2["row_span_position"]) ? $title : ""
								];
							}
						} else {
							$header_row_type[$key][$value2["row_span_col_index"]] = [
								"type" 		=> $columnsType,
								"row_span" 	=> ($wfmAnnualOvertimeReport) ? ($value2["row_span"] ?? null) : $value2["row_span"] ?? 1,
								"col_span" 	=> ($wfmAnnualOvertimeReport) ? ($value2["col_span"] ?? null) : $value2["col_span"] ?? 1,
								"title" 	=> $title
							];
						}
						foreach ($header_row_type as $index => $val) {
							ksort($header_row_type[$index], SORT_NUMERIC);
						}
					}
                    else
                    {
						$header_row_type[$key][$key2] = [
							"type" 		=> $columnsType,
							"row_span" 	=> isset($value2["row_span"]) ? $value2["row_span"] : 1,
							"col_span" 	=> isset($value2["col_span"]) ? $value2["col_span"] : 1,
                            "row_span_col_index" => isset($value2["row_span_col_index"]) ? $value2["row_span_col_index"] : null,
							"title" 	=> $title
						];
					}

					$colInd++;
				}
			}
		}
		else
		{
			$titleNumber = 0;
			foreach ($columns as $column=>$attrs)
			{

				if ( !$this->isColumnVisibleInExport($attrs) ) {
                    if (isset($attrs["col_type"]) && $attrs["grid"] && ($attrs["col_type"] === "combo" || $attrs["col_type"] === "auto")) {
                        $colInd++;
                    }
                    continue;
                }
                $title = isset($attributeLabels[$column]) ? $attributeLabels[$column] : "";
                if (strlen($title) <= 0) {
                    continue;
                }
                if (array_key_exists($title, $header_row_type))
                {
                    $titleNumber++;
                    $title .= '_' . $titleNumber;
                }

                $values_widths[$colInd] = isset($attrs["report_width"]) && $attrs["report_width"] > 0 ? $attrs["report_width"] : strlen(
                        $title
                    ) + 5;
                if (isset($attrs["col_type"]) && ($attrs["col_type"] === "combo" || $attrs["col_type"] === "auto")) {
                    $dropdownData[$colInd] = $hasMultiGridMode
                        ? $this->fetchDropdownData($colInd, "GRID", false, null, array(), array(), $gridID)
                        : $this->fetchDropdownData($colInd, "GRID");
                }

                if (isset($attrs["wrap_text"]) && $attrs["wrap_text"]) {
                    $excelExportStylePlus = ['wrap_text' => true];
                }

                $title = str_replace(["<BR>", "<br/>",], ["\r\n", "\r\n",], $title);
                $header_row_type[$title] = $this->setExcelColumnsType($attrs);
                $colInd++;

			}
		}

		$values_rows									= [];
		$convertFrameManagementExportIntoInteger		= (int)App::getSetting('convertFrameManagementExportIntoInteger');
        $convertFrameManagementExportIntoIntegerColumns	= ['frame_daily_worktime_5_2', 'daily_worktime_work_scheduled', 'worktime', 'frame_balance', 'scheduled_frame_balance'];
		$styles_header =
		[
			'wrap_text'		=> true,
			'font'			=> 'Calibri',
			'font-size'		=> 11,
			'font-style'	=> 'bold',
			'fill'			=> '#d1e5fe',
			'halign'		=> 'center',
			'valign'		=> 'center',
			'widths'		=> $values_widths
		];
        $styles1					= ['font' => 'Calibri','font-size' => 11];
		$secondLineOption			= (in_array($this->controller_id, $this->xlsxWriterSecondLineOptionDisable)) ? false : true;
		$title2						= $results['defaultSheetName'] ?? $title2;
		if (count($header_row_type) > 0) {
            $objphpwriter->writeSheetHeader($title2, $header_row_type, $styles_header, $secondLineOption, $rowSpanColIndex);
        }

		$rowStyles				= [];
		$rowCellTypes			= [];
		$numericForced			= [];
        $columnsConvertDecimal	= $this->setColumnsConvertDecimal();
		$resultsData			= [];
		$resultsData			= $results['data'];
        $maxRowId				= 0;
		foreach ($resultsData as $v1=>$properties) {
			$dataColumns = $properties["columns"];
			$colInd = 0;
			foreach ($columns as $column => $attrs) {

				if ( !$this->isColumnVisibleInExport($attrs) ) {
                    if (isset($attrs["col_type"]) && $attrs["grid"] && ($attrs["col_type"] === "combo" || $attrs["col_type"] === "auto")) {
                        $colInd++;
                    }
                    continue;
                }
                $value = "";
                $style = [];
                $cellType = [];
                $numericForce = false;

                if (isset($dataColumns[$column])) {
                    if (isset($attrs["col_type"]) && ($attrs["col_type"] === "combo" || $attrs["col_type"] === "auto")
                        && !in_array($this->controller_id, $this->xlsxExportEmployeeControlController)) {
                        if (isset($dropdownData[$colInd]["data"])) {
                            $values = $dropdownData[$colInd]["data"];
                            if (isset($values[$dataColumns[$column]['data']]["data"])) {
                                $value = $values[$dataColumns[$column]['data']]["data"];
                            }
                        }
                    } else {
                        $value = $dataColumns[$column]['data'];
                    }

                    if (isset($attrs["cell_type"]) && $attrs["cell_type"] == "general") {
                        $cellType = ["number_format" => "GENERAL", "number_format_type" => "n_auto"];
                    }

                    $style = $this->setIndividuelCellStyles($style, $dataColumns[$column]);

                    // Bg color
                    if (isset($attrs["col_type"]) && $attrs["col_type"] == "cp") {
                        $style = array_merge($style, ["fill" => $dataColumns[$column]["data"]]);
                    }
                }

                if($convertFrameManagementExportIntoInteger == 1 && in_array($column, $convertFrameManagementExportIntoIntegerColumns))
                {
                    $value = $this->setConvertFrameManagementExportIntoIntegerValue($value);
                }
                else if (!($attrs["export_as"] === "time" || $attrs["export_as"] === "timeMoreOneDay") && (in_array($column, $columnsConvertDecimal) || $wfmAnnualOvertimeReport) && strpos($value, ":") !== false )
                {
                    // Bérátadás napi nézet exportot elrontotta a schedule_sum
                    if ($column == 'schedule_sum' && $this->controller_id == "payrollTransfer")
                    {

                    }
                    else
                    {
                        if (strpos($column, "percent") !== false && $wfmAnnualOvertimeReport && strpos($value, ":") !== false) {
                            $numericForce = true;
                        }
                        $value = $this->setConvertValueDecimal($value);

                        if ($wfmAnnualOvertimeReport) {
                            $value = number_format((float) $value, 2, '.', '');
                        }
                    }
                }
                else if (isset($attrs["export_as"]) && ($attrs["export_as"] === "time" || $attrs["export_as"] === "timeMoreOneDay")) {
                    $value = $this->setConvertExcelDatetimeFormat($value);
                }
                else if (isset($attrs["export_as"]) && $attrs["export_as"] === "decimal") {
                    $value = str_replace(",", ".", $value);
                }
                else if (isset($attrs["export_as"]) && $attrs["export_as"] === "date_dot") {
                    $value = str_replace(".", "-", $value);
                }
                else if (isset($attrs["export_as"]) && $attrs["export_as"] === "numeric_percent") {
                    $value = str_replace("%", "", $value);
                    $value = (int)$value / 100;
                }


                $values_rows[] = str_replace(" <br /> ", "\r", $value);
                $rowStyles[] = array_merge($style, $excelExportStylePlus);
                $rowCellTypes[] = $cellType;
                $numericForced[] = $numericForce;

                $value_width = strlen($value);
                if (isset($values_widths[$colInd]) && $values_widths[$colInd] < $value_width) {
                    $values_widths[$colInd] = $value_width;
                }
                $colInd++;

			}

			if (count($rowStyles) > 0) {
				$objphpwriter->writeSheetRow($title2, $values_rows, $rowStyles, $rowCellTypes, $numericForced);
			} else {
				$objphpwriter->writeSheetRow($title2, $values_rows, $styles1, $rowCellTypes, $numericForced);
			}
			$values_rows = array();
			$rowStyles = [];
            $maxRowId++;

		}

        if (!empty($footerDatas)) {
            $fromRowNumber = $maxRowSpan + 1;
            $toRowNumber = $maxRowSpan + $maxRowId;
            $colNumber = 0;
            $footerDataRow = [];
            foreach($footerDatas as $key => $value)
            {
                $colNumber++;
                $footerDataValues = $this->setFooterValues($value);
                switch($footerDataValues['footerColType'])
                {
                    case 'text': {
                        $footerDataRow[] = $footerDataValues['footerLabelText'];
                            break;
                    }
                    case 'number': {
                        $colNumberAlphabet = $this->getAlphabet($colNumber);
                        $footerDataRow[] = $footerDataValues['footerLabelText'] . "($colNumberAlphabet$fromRowNumber:$colNumberAlphabet$toRowNumber)";
                            break;
                    }
                    default: {
                            $footerDataRow[] = "";
                    }
                }
            }
            $objphpwriter->writeSheetRow($title2, $footerDataRow, null, ['number_format' => '@', 'number_format_type' => 'n_string']);
        }

		$excelExportShowFooterCreatedOnControllers = explode(';', App::getSetting('ExcelExportShowFooterCreatedOnByControllerIDs'));
		if (in_array($this->controller_id, $excelExportShowFooterCreatedOnControllers)) {
			$rowFooterCreatedOn[] = Dict::getValue("created_on") . ": " . date("Y-m-d H:i");
			
			$objphpwriter->writeSheetRow($title2, $rowFooterCreatedOn, null, ['number_format' => '@', 'number_format_type' => 'n_string']);
		}

		if($this->showFilter)
		{
            if (!is_array($searchBar)){
            	$searchBar = [];
            }
			$searchFields = $this->generateSearchFilterText($searchBar);
			$objphpwriter->writeSheetRow($title2, []);
			foreach ($searchFields as $filterRow) {
				$objphpwriter->writeSheetRow($title2, $filterRow, null, ['number_format' => '@', 'number_format_type' => 'n_string']);
			}
		}

		if (isset($this->excelMultiSheets) && $this->excelMultiSheets == 1 && isset($results['multiSheetNames']) && isset($results['multiSheetDataCorrected'])) {
			$objphpwriter = $this->xlsxWriterMultiSheets($objphpwriter, $results['multiSheetNames'], $results['multiSheetDataCorrected'], $columns);
		}

		$objphpwriter->writeToFile(Yang::getBasePath().'/../webroot/reports/'.$filename);
		$url = baseURL().'/reports/'.$filename;
		return $url;
	}

	/**
	 * Multi sheet
	 * @param XLSXWriter $xlsWriter
	 * @param array $sheetNames
	 * @param array $datas
	 * @return XLSXWriter
	 */
	protected function xlsxWriterMultiSheets(XLSXWriter $xlsWriter, array $sheetNames = [], array $datas = [], array $columns = [])
	{
		$stylesHeader =
		[
			'wrap_text'		=> true,
			'font'			=> 'Calibri',
			'font-size'		=> 11,
			'font-style'	=> 'bold',
			'fill'			=> '#d1e5fe',
			'halign'		=> 'center',
			'valign'		=> 'center',
			'widths'		=> '100'
		];
		foreach ($datas as $key => $data) 
        {
            $headerRowType = [];
            if (isset($data[0]['column_type']) && is_array($data[0]['column_type'])) {
                foreach ($data[0]['column_type'] as $dColumnType => $dColType) {
                    $headerRowType[$data[0][$dColumnType]] = $this->setExcelColumnsType($columns[$dColType]);
                }
            } else {
                foreach ($data[0] as $dColumnType => $dColType) {
                    $headerRowType[$dColType] = "string";
                }
            }
            $xlsWriter->writeSheetHeader($sheetNames[$key], $headerRowType, $stylesHeader, true, false);
            unset($data[0]);
            foreach($data as $dataKey => $value) {
			    $xlsWriter->writeSheetRow($sheetNames[$key], $value, null, ["number_format" => "GENERAL", "number_format_type" => "n_auto"]);
            }
		}
        return $xlsWriter;
    }

    /**
     * @param array $searchBar
     * @return array
     */
	protected function generateSearchFilterText(array $searchBar) : array
	{
	    $searchFields = [];
		$search = $this->search();

		if (!is_null(Yang::session('datetime')) && in_array($this->getControllerID(), ['wfm/reportAttendance']))
		{
			$searchBar['date_time'] = Yang::session('datetime');
			$search['date_time']['label_text'] = Dict::getValue('date');
		}

		foreach ($searchBar as $column => $value) {
	        $dict =  $search[$column]['label_text'] ?: $column;
            $searchFields[] = [$dict . ' = ' . $this->getSearchFilterTextValue($column, $value, $search[$column])];
        }

	    return $searchFields;
    }

    /**
     * @param string $column
     * @param $value
     * @return string|null
     */
	protected function getSearchFilterTextValue(string $column, $value, $searchInfo)
	{
		$userId = userID();
		$controllerId = $this->getControllerID();
		$result = [];
		$published = Status::PUBLISHED;

		if (!is_array($value)) {
			$value = [$value];
		}

		foreach ($value as $row)
		{
			if (empty($row)) { continue; }
			if ($searchInfo['col_type'] == 'combo' && $searchInfo['options']['mode'] == Grid2Controller::G2BC_QUERY_MODE_ARRAY)
			{
				foreach ($searchInfo['options']['array'] as $row) {
					if ($row['id'] == $value[0]) {
						$result[] = $row['value'];
						break;
					}
				}
			}
			else if($row == 'ALL')
			{
				$result[] = Dict::getValue("all");
			}
			else if (strpos($column, 'company_org_group') !== false)
			{
				$table = substr($column, -3) == '_id' ? substr($column, 0, -3) : $column;
				$model = str_replace(' ', '', ucwords(strtr($table, ['-' => ' ', '_' => ' '])));
				$col = 'company_org_group_id';
				$name = "company_org_group_name";
				$result[] = $model::model()->findByAttributes([$col => $row, 'status' => $published])->$name;
			}
			else if (substr($column, -3) == '_id')
			{
				$table = substr($column, 0, -3);
				$model = str_replace(' ', '', ucwords(strtr($table, ['-' => ' ', '_' => ' '])));
				$table == "user" ? $name = $table . "name" : $name = $table . "_name";
				$result[] = $model::model()->findByAttributes([$column => $row, 'status' => $published])->$name;
			}
			else if (class_exists(str_replace(' ', '', ucwords(str_replace('-', ' ', $column)))))
			{
				$model = str_replace(' ', '', ucwords(str_replace('-', ' ', $column)));
				$name = $column . "_name";
				$result[] = $model::model()->findByAttributes([$column . '_id' => $row, 'status' => $published])->$name;
			}
			else if ($column == "mep")
			{
				$result[] = Company::model()->findByAttributes(['company_id' => $row, 'status' => $published])->company_name;
			}
			else if ($column == 'employee_contract')
			{
				$result[] = $_SESSION["tiptime"][$userId][$controllerId]["searchInput_auto_employee_contract"];
			}
			else
			{
				$result[] = Dict::getValue($row) ?: $row;
			}
		}

		return implode(" ,", $result);
    }

	protected function generateCsv($filename=''){

		$this->G2BInit();
		$gridID = requestParam('gridID');
		$modelName = $this->LAGridDB->getModelName($gridID);
		$attributeLabels = [];

		if ($this->LAGridDB->getMode() === Grid2Controller::G2BC_QUERY_MODE_MODEL && class_exists($modelName)) {
			$attributeLabels = $modelName::model()->attributeLabels();
		} else {
			$attributeLabelsClass = [];

			if (method_exists($this, "attributeLabels") && count($this->attributeLabels())) {
				$attributeLabelsClass = $this->attributeLabels();
			}
			$attributeLabelsModel = [];
			if (class_exists($modelName)) {
				$attributeLabelsModel = $modelName::model()->attributeLabels();
			}
			$attributeLabels = Yang::arrayMerge($attributeLabelsModel, $attributeLabelsClass);
		}

		$userId = userID();
		$controllerId = $this->getControllerID();

		$csvFile = '';

		// Ha erkezik keresesi feltetel, akkor fetchgrid, ha nem, akkor a regi sessionos csv export

		$searchBar = requestParam('searchInput');
        $useExportAttributeInCSVExport = isset($searchBar['useExportAttributeInCSVExport'])
            && $searchBar['useExportAttributeInCSVExport'] != 0;
        $isDataArrayResults = in_array($controllerId, $this->csvExportArrayControllers)
            || $useExportAttributeInCSVExport;

		$filename .= date("_Y.m.d_H.i.s");

		if ($searchBar <> ''){
			if ($isDataArrayResults) {
                $results = $this->fetchGridData($searchBar,true);
			} else {
                $results = $this->fetchGridData($searchBar,true,false,true);
			}

			$columns = $this->columns();
			$values_rows = array();
			$headers = $this->headers();

			$header = array();

			if (count($headers) > 0){
				foreach ($headers as $key => $value){
					foreach ($value as $key2 => $value2){
						$title = isset($value2['label_text'])?$value2['label_text']:"";
						$title = str_replace(["<BR>","<br/>",], ["\r\n","\r\n",], $title);
						array_push($header, $title);
					}
				}
			}else{
				foreach ($columns as $column=>$attrs){
					if ( $this->isColumnVisibleInExport($attrs) && $this->isColumnVisibleInGrid($attrs)) {
						$title = isset($attributeLabels[$column])?$attributeLabels[$column]:"";
						array_push($header, $title);
					}

				}
			}

			// write header to CSV file variable
			$csvFile .= implode(';', $header);
			$csvFile .= "\n";

			$convertFrameManagementExportIntoInteger = App::getSetting('convertFrameManagementExportIntoInteger');

			if (!in_array($controllerId, $this->csvExportArrayControllers)) {
                if ($useExportAttributeInCSVExport) {
                    foreach (($results['data'] ?? []) as $rowData) {
                        $csvRow = '';
                        foreach (($rowData['columns'] ?? []) as $columnName => $fieldData) {
                            $attrs = $columns[$columnName] ?? [];
                            if ($this->isColumnVisibleInExport($attrs)) {
                                $csvRow .= !empty($csvRow) ? ';' : '';
                                $csvRow .= $fieldData['data'] ?? '';
                            }
                        }
                        $csvFile .= $csvRow . "\n";
                    }
                } else {
                    $csvFile .= $results['data'];
                }
			} else {
				foreach ($results['data'] as $v1=>$properties) {
					$dataColumns = $properties["columns"];
					$values_rows = array();
					foreach ($columns as $column => $attrs) {
						if ( $this->isColumnVisibleInExport($attrs) && $this->isColumnVisibleInGrid($attrs)) {
							$value = "";

							if (isset($dataColumns[$column])) {
								$value = $dataColumns[$column]['data'];
							}

							if( $convertFrameManagementExportIntoInteger == 1
								&& (
									$column == 'frame_daily_worktime_5_2' ||
									$column == 'daily_worktime_work_scheduled' ||
									$column == 'worktime' ||
									$column == 'frame_balance' ||
                                    $column == 'scheduled_frame_balance'
								) ){
								{
									$explodedValue = explode(':',$value);
									$hourToMinutes = (int)$explodedValue[0] * 60;
									$minutes = (int)$explodedValue[1];
									$value = $hourToMinutes + $minutes;
								}
							}else if (
									$column == 'frame_daily_worktime_5_2' ||
									$column == 'daily_worktime_work_scheduled' ||
									$column == 'worktime' ||
									$column == 'frame_balance' ||
									$column == 'annual_ot_balance_orig' ||
									$column == 'worktime_hours' ||
									$column == 'monthly_accounted_ot' ||
									$column == 'weekday_ot' ||
									$column == 'weekend_ot' ||
									$column == 'cumulative_ot' ||
									$column == 'current_ot_ratio' ||
									$column == '300_hour' ||
									$column == 'monthly_difference'||
									$column == 'unpaid_ot' ||
                                    $column == 'scheduled_frame_balance'
								)
							{
									$explodedValue = explode(':',$value);
									$hourToMinutes = (int)$explodedValue[0];
									$minutes = (int)$explodedValue[1] / 60;
									$value = $hourToMinutes + $minutes;
							}
							$values_rows[] = $value;

						}
					}
					$csvFile .= implode(';', $values_rows);
					$csvFile .= "\n";
				}
			}
		}else{
			$gridData = $_SESSION["tiptime"][$userId][$controllerId]["gridDataArr"]["dhtmlxGrid"];
			if (isset($gridData))
			{
				foreach ($gridData as $dataRow)
				{
					$csvFile .= implode(';', $dataRow);
					$csvFile .= "\n";
				}
			}
		}

		if (!in_array($controllerId, $this->csvExportArrayISO)) {
			if ($controllerId != "customers/flex/reportEmployees") {
				$csvFile = mb_convert_encoding($csvFile, "WINDOWS-1252", "UTF-8");
			} else {
				$csvFile = iconv('UTF-8', 'UTF-8', $csvFile);
			}
		} else {
			$csvFile = iconv('UTF-8', 'ISO-8859-2', $csvFile); // Ms Excel miatt
		}

		$filename .= '.csv';
		if ($controllerId != "customers/flex/reportEmployees") {
			$trans = array("." => ",");
			$csvFile = strtr($csvFile, $trans);
		}
		$f = fopen("./reports/" . $filename, 'w');
		fwrite($f, $csvFile);
		fclose($f);

		$url = baseURL().'/reports/' . $filename;

		return $url;
	}

    /**
     * Summary of setExcelColumnsType
     * @param null|array $exportAsArray
     * @return string
     */
    private function setExcelColumnsType($exportAsArray)
    {
        $type = "string";
        if (isset($exportAsArray["export_as"]))
        {
            switch ($exportAsArray["export_as"]) {
                case "numeric":
                    $type = "integer";
                    break;
                case "numeric_percent":
                    $type = $exportAsArray['export_format'];
                    break;
                case "decimal":
                    $type = isset($exportAsArray['export_format']) && !empty($exportAsArray['export_format']) ? $exportAsArray['export_format'] : "0.00";
                    break;
                case "date":
                    $type = "YYYY-MM-DD";
                    break;
                case "time":
                    $type = isset($exportAsArray['export_format']) && !empty($exportAsArray['export_format']) ? $exportAsArray['export_format'] : "HH:MM:SS";
                    break;
                case "timeMoreOneDay":
                    $type = "[H]:MM:SS";
                    break;
                case "datetime":
                    $type = "YYYY-MM-DD HH:MM:SS";
                    break;
                case "date_dot":
                    $type = "YYYY.MM.DD";
                    break;
            }
        }
        return $type;
    }

    /**
     * Summary of setExcelColumnsFormat
     * @param null|string $exportAsArray
     * @return string
     */
    private function setConvertExcelDatetimeFormat($value)
    {
        if (is_null($value) || empty($value)) { return ""; }

        $explodedValue = explode(':',$value);
        $hour = $explodedValue[0] ?? (string)'00';
        $minutes = $explodedValue[1] ?? (string)'00';
        $second = $explodedValue[2] ?? (string)'00';
        $value = "1900-01-00 " . $hour. ':'. $minutes. ':'. $second;
        return $value;
    }

    /**
     * Summary of setColumnsConvertDecimal
     * @return array<string>
     */
    private function setColumnsConvertDecimal()
    {
        if ($this->controller_id == "wfm/frameManagement" && (int)App::getSetting('exportFrameManagementInTimeFormat') === 1)
        {
            return [];
        }

        $columnsConvertDecimal = ['frame_daily_worktime_5_2', 'daily_worktime_work_scheduled', 'worktime', 'frame_balance', 'scheduled_frame_balance', 'annual_ot_balance_orig',
        'worktime_hours', 'monthly_accounted_ot', 'accounted_ot', 'weekday_ot', 'weekend_ot', 'cumulative_ot', 'unpaid_ot_for_mik', 'current_ot_ratio',
        '300_hour', 'monthly_difference', 'unpaid_ot', 'overtime', 'balance', 'fullworktime', 'schedule_worktime', 'absence_worktime', 'schedule_sum',
        'paid_absences', 'unpaid_absences', 'scheduled_time', 'worked_time', 'accepted_overtime_hours', 'not_accepted_overtime_hours', 'ot_sum',
        'absence_hours', 'night_shift_allowance', 'sunday_allowance', 'holiday_allowance', 'afternoon2', 'overtime_weekend', 'overtime_weekday', 'overtime_ej',
        'overtime_du', 'overtime_de', 'calc_balance_real_sum', 'pay_overtime', 'accounted_hours', 'justified_sickpay', 'used_absence', 'absence_other',
        'overtime100', 'rest_of_restdays'];

        if ($this->controller_id == "wfm/frameManagement" && Yang::customerDbPatchName() == "kedrion") {
            $columnsConvertDecimal[] = "paid_ot";
            $columnsConvertDecimal[] = "ot_value";
            $columnsConvertDecimal[] = "annual_ot_balance";
        }

        if ($this->controller_id == "wfm/employeeCalcReport" && (int)App::getSetting("wfmEmployeeCalcRep2Decimal")) {
            $columnsConvertDecimal[] = 'value';
        }
        if ($this->controller_id == "customers/stricker/monthlyHourDistribution") {
            $columnsConvertDecimal[] = 'potential_hours';
            $columnsConvertDecimal[] = 'overtime_hours';
            $columnsConvertDecimal[] = 'actual_worked_hours';
        }
        return $columnsConvertDecimal;
    }

    /**
     * Summary of setIndividuelCellStyles
     * @param array $style
     * @param mixed $dataColumnsColumn
     * @return array
     */
    private function setIndividuelCellStyles($style, $dataColumnsColumn)
    {
        //set individual cell styles
        if (isset($dataColumnsColumn["align"])) {
            $style = array_merge($style, ["halign" => $dataColumnsColumn["align"]]);
        }

        if (isset($dataColumnsColumn["excel_bgColor"])) {
            $style = array_merge($style, ["fill" => $dataColumnsColumn["excel_bgColor"]]);
        }

        if (isset($dataColumnsColumn["excel_color"])) {
            $style = array_merge($style, ["color" => $dataColumnsColumn["excel_color"]]);
        }

        if (isset($dataColumnsColumn["excel_font_style"])) {
            $style = array_merge($style, ["font-style" => $dataColumnsColumn["excel_font_style"]]);
        }
        return $style;
    }

    /**
     * Summary of setConvertFrameManagementExportIntoIntegerValue
     * @param mixed $value
     * @return int
     */
    private function setConvertFrameManagementExportIntoIntegerValue($value)
    {
        $isNegative = substr($value, 0, 1) == "-";
        $explodedValue = explode(':',$value);
        $hourToMinutes = (int)$explodedValue[0] * 60;
        $minutes = ($hourToMinutes >= 0 && !$isNegative) ? (int)$explodedValue[1] : (int)-$explodedValue[1];
        $value = $hourToMinutes + $minutes;
        if ($value > 0 && $isNegative) {
            $value *= -1;
        }
        return $value;
    }

    /**
     * Summary of setConvertValueDecimal
     * @param mixed $value
     * @return float
     */
    private function setConvertValueDecimal($value)
    {
        $isNegative = substr($value, 0, 1) == "-";
        $explodedValue = explode(':',$value);
        $hourToMinutes = (int)$explodedValue[0];
        $minutes = ($hourToMinutes >= 0 && !$isNegative) ? (int) $explodedValue[1] / 60 : (int) -$explodedValue[1] / 60;
        $value = $hourToMinutes + $minutes;
        if ($value > 0 && $isNegative) {
            $value *= -1;
        }
        return $value;
    }

    /**
     * Summary of setFooterValues
     * @param array $value
     * @return array
     */
    private function setFooterValues($value)
    {
        $footerValues = [
            'footerLabelText' => isset($value['footer_label_text']) && !empty($value['footer_label_text']) ? $value['footer_label_text'] : "",
            'footerAlign' => isset($value['footer_align']) && !empty($value['footer_align']) ? $value['footer_align'] : "",
            'footerColSpan' => isset($value['footer_col_span']) && !empty($value['footer_col_span']) ? $value['footer_col_span'] : "",
            'footerColType' => isset($value['footer_col_type']) && !empty($value['footer_col_type']) ? $value['footer_col_type'] : "",
            'footerColTypeFormat' => isset($value['footer_col_type_format']) && !empty($value['footer_col_type_format']) ? $value['footer_col_type_format'] : "",
        ];

        return $footerValues;
    }

    /**
     * Summary of getAlphabet
     * @param int $number
     * @return string
     */
    private function getAlphabet($number)
    {
        $range = XlsxToArray::setExcelAlphabetRange();
        return $range[$number - 1];
    }
}

?>
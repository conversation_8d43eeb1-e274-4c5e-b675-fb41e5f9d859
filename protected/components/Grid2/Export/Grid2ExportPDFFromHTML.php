<?php #yii2: done

'yii2-only`;

	namespace app\components\Grid2\Export;
	use Yang;

`/yii2-only';


trait Grid2ExportPDFFromHTML
{
	protected function generatePDFFromHTML($html = "", $filename = null, $preDefinedHTML = false) {
		ini_set('memory_limit', '1024M');

		require_once Yang::getBasePath() . "/extensions/dompdf/dompdf_config.inc.php";
		
		if ($preDefinedHTML) {
			$html = "
<html>
<head>
	<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />
	<style type=\"text/css\">
		div.page-break	{ display: block; page-break-before: always; }
		body {
			font-family: 'arial';
		}

		td.holiday {
			background: #efefef;
		}

		td.centered {
			text-align: center;
		}
	</style>
</head>
<body>
$html
</body>
</html>
			";
		}
		
		if (empty($html)) {
			$html = "<html></html>";
		}
		
		if (empty($filename)) {
			$filename = $this->getExportFileName();
		}
		
		$filename .= date("_Y.m.d_H.i.s");
		$filename .= ".pdf";
		
		$out = Yang::getBasePath().'/../webroot/reports/'.$filename;
		$url = baseURL().'/reports/'.$filename;
		
//		$file = fopen("$out.html","w");
//		fwrite($file, $html);
//		fclose($file);
		
		$dompdf = new DOMPDF();     //if you use namespaces you may use new \DOMPDF()
		$dompdf->load_html($html);
		$dompdf->render();
//		$dompdf->stream($filename, array("Attachment"=>0));
		$output = $dompdf->output();
		file_put_contents($out, $output);
		
		return $url;
	}
}

?>
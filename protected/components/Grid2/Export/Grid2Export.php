<?php #yii2: done

'yii2-only`;

	namespace app\components\Grid2\Export;
	use Yang;

`/yii2-only';


trait Grid2Export
{
	private $exportFilename = "grid";
	
	/**
	 * Export
	 */
	protected function setExportFileName($exportFilename = "grid") {
		$this->exportFilename = $exportFilename;
	}

	protected function getExportFileName() {
		return $this->exportFilename;
	}

	protected function setExportFSFileID($fileID = null) {
		$userId = userID();
		$controllerId = $this->getControllerID();

		$_SESSION["tiptime"][$userId][$controllerId]["lastReportFileID"] = $fileID;
	}

	protected function getExportFSFileID() {
		$userId = userID();
		$controllerId = $this->getControllerID();

		if (!isset($_SESSION["tiptime"][$userId][$controllerId]["lastReportFileID"])) {
			return false;
		}

		$fileID = $_SESSION["tiptime"][$userId][$controllerId]["lastReportFileID"];

		if (empty($fileID)) {
			return false;
		}

		return $fileID;
	}

	public function actionGetExportFSFileID() {
		$this->layout = 'ajax';

		$fileID = $this->getExportFSFileID();

		$resp = [
			"fileID" => empty($fileID)?"":$fileID,
		];

		echo json_encode($resp);
	}

	public function actionRunBeforeJReport() {
		$resp = [
			"status" => "success",
		];

		echo json_encode($resp);
	}

	protected function num2alpha($n) {
		for ($r = ""; $n >= 0; $n = intval($n / 26) - 1) {
			$r = chr($n%26 + 0x41) . $r;
		}
		return $r;
	}
}

?>
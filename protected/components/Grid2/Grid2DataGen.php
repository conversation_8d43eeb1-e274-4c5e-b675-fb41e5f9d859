<?php

trait Grid2DataGen
{
	/**
	 * Összeszedi az alap generáláshoz szükséges adatokat. A tömbbe gyűjtött adatok ${index} változókkal is elérhetők.
	 */
	protected function initFetchParams($filter) {

		'Calculate pages';{

			$borderSize = 1;
			$lineHeight = 30;

			$gridParams			= requestParam('gridParams');
			$containerHeight	= isset($gridParams["objboxHeight"]) ?  (int)$gridParams["objboxHeight"] : 0;
			$currentPage		= (int)requestParam('currentPage');
			$linesPerPage		= $containerHeight / ($lineHeight + $borderSize);
			$linesPerPage		= (int)floor( $linesPerPage );
			$linesPerPage		= max(1,$linesPerPage); // valójában azt jelenti, hogy "legyen minimum 1"
			$pageStart			= $currentPage * $linesPerPage;

		}
		'Check session for cached data';{

			$userID = userID();
			$gridID = requestParam('gridID');
			$ctrlID = $this->getControllerID();
			$cached = isset($_SESSION["tiptime"][$userID][$ctrlID]["gridDataArr"][$gridID]);

		}
		'Load values to $this->fetchParams';{

			$fp = &$this->fetchParams;
			$fp["user_id"		] = $userID;
			$fp["gridID"		] = $gridID;
			$fp["controllerId"	] = $ctrlID;
			$fp["headFilters"	] = requestParam("{$gridID}HeadFilters");
			$fp["export"		] = (int)requestParam('export');
			$fp["regenerate"	] = (int)requestParam('regenerate') || (!$cached);
			$fp["gridParams"	] = $gridParams;
			$fp["objboxHeight"	] = $containerHeight;
			$fp["lineHeight"	] = $lineHeight;
			$fp["linesPerPage"	] = $linesPerPage;
			$fp["currentPage"	] = $currentPage;
			$fp["pageStart"		] = $pageStart;
			$fp["totalRowCount"	] = 0;
			$fp["pageCount"		] = 0;

		}

	}

	/**
	 * A lapozóból jövő paraméterek alapján szészedi a tömböt úgy, hogy csak a megjelenítendő elemek legyenek benne.
	 */
	protected function getPageResults($totalResults, $pageStart = 0, $linesPerPage = 0, &$results = []) {
		$no = 0;
		$cnt = 0;

		foreach ($totalResults as $res) {
			if ($pageStart <= $no) {
				if ($cnt < $linesPerPage) {
					$results[] = $res;
				}

				$cnt++;
			}

			$no++;
		}
	}

	/**
	 * Újrageneráláskor SESSION-be tárolja az adatokat, különben a változóba kerül a SESSION értéke.
	 */
	protected function getSessionData(&$totalResults = []) {

		#reasonUnclear // az egész függvény nagyon furcsa, nem igazán logikus, http://innote-dk.local/n-71b6tcg3

		$uid = $this->fetchParams["user_id"];
		$cid = $this->fetchParams["controllerId"];
		$gid = $this->fetchParams["gridID"];
		$grp = "gridDataArr";

		$freshlyGenerated = ($this->fetchParams["regenerate"]);
		if( $freshlyGenerated) $_SESSION["tiptime"][$uid][$cid][$grp][$gid] = $totalResults;
		if(!$freshlyGenerated) $totalResults = $_SESSION["tiptime"][$uid][$cid][$grp][$gid];

	}

	/**
	 * Visszaadja a fejlécben megszűrt adatokat
	 */
	protected function filterResults($headFilters = [], &$totalResults = []) {

		#refactorThis // nem nagyon csúnya, de azér érdemes lenne

		if (is_array($headFilters) && count($headFilters)) {
			foreach ($headFilters as $filterColumn => $filterValue) {
				if ($filterValue === '') {
					unset($headFilters[$filterColumn]);
				}
			}

            $columns = $this->columns();
			$totalResults = array_filter($totalResults, function($var) use($headFilters, $columns) {
				$ret = true;

				foreach ($headFilters as $filterColumn => $filterValue) {
					$haystack = isset($var[$filterColumn]) ? mb_strtolower($var[$filterColumn], 'UTF-8') : '';
					$needle = mb_strtolower($filterValue, 'UTF-8');

                    //combo case
                    $columnDetails = $columns[$filterColumn] ?? [];
                    if ($haystack !== '' && $columnDetails['col_type'] === 'combo' && !empty($columnDetails['options']['array'])) {
                        $fieldOptionData = array_filter($columnDetails['options']['array'], function ($element) use ($haystack) {
                            return isset($element['id']) && $element['id'] === $haystack;
                        });

                        $haystack = reset($fieldOptionData)['value'] ?? $haystack;
                    }

					$ret = $ret && is_array($var) && isset($var[$filterColumn]) && (stripos($haystack, $needle) !== false);
				}

				return $ret;
			});
		}
	}

	/**
	 * Visszaadja az aktuális oszlopokat, multigridnél is.
	 */
	protected function getActualColumns($gridID = null) {
		//mDoing("we're in ".get_class($this));
		$columns = $this->columns();

        if ($this->hasMultiGridMode() && !empty($gridID))
        {
            if (isset($columns[$gridID])) {
                $columns = $columns[$gridID];
            } else {
                if ($gridID == "dhtmlxGridHistory" && isset($columns["dhtmlxGrid"])) {
                    $columns = $columns["dhtmlxGrid"];
                } else {
                    $columns = array();
                }
            }
        }

    	return $columns;
	}

	/**
	 * Legenerálja a $retArr tömb adattartalmát. Gyakorlatilag itt jön létre a grid-ben megjelenő tartalom.
	 */
	protected function getRetArrData(	$laGrid = false,
										$gridID = null,
										$multiHeaderWidths = [],
										$lineHeight = 0,
										$results = [],
										&$retArr = [],
										$excelExport = false,
										$csvString = false,
										$pdfExport = false
									)
	{
		$pk = $this->LAGridDB->getPrimaryKey($gridID);

		$compressRows = useExperimental("use compressed grid data array") ?1:0;

		if (count($results)) {
			$columns = $this->getActualColumns($gridID);
			$columns; // http://innote-dk.local/n-71g4s41c
			$rNo = 0;

			foreach ($results as $res) {
				$row = [];
				$this->getGridRecords($laGrid, $gridID, $columns, $multiHeaderWidths, $res, $rNo, $pk, $lineHeight, $row, $excelExport, $csvString, $pdfExport);
				if ($csvString) {
					(isset($retArr['data'])) ? $retArr['data'] .= $row . "\n" : $retArr['data'] = $row . "\n";
				} else {
					if($compressRows==1) $retArr['data'][] = $this->encodeRowData($row);
					if($compressRows==0) $retArr['data'][] = $row;
				}
				$rNo++;
			}
		}

	}

	/**
	 * Visszaadja a megadott paraméterek alapján legenerált grid rekord adatot.
	 */
	protected function getGridRecords($laGrid = false, $gridID = null, $columns = [], $multiHeaderWidths = [], $res = null, $rNo = 0, $pk = "", $lineHeight = 0, &$row = [], $excelExport = false, $csvString = false, $pdfExport = false) {

		#refactorThis	//	Úgy néz ki, hogy csak ez az egy hívás van rá, tehát nem lesz nagy magic
						//	Viszont ha refaktolod, akkor módosítsd a két static html-t is, ahol van
						//	róla leírás. Mer van róla.

		if ($csvString) {
			$row = "";
		} else {
			$pkValue = "";

			if ($this->LAGridDB->getMode($gridID) === Grid2Controller::G2BC_QUERY_MODE_MODEL) {
				if ($laGrid) {
					if (isset($res[$pk]) && !empty($res[$pk])) {
						$row["pk"] = $res[$pk];
						$pkValue = $res[$pk];
					}
				} else {
					$row["pk"] = $res->$pk;
					$pkValue = $res->$pk;
				}
			} else {
				if (isset($res[$pk]) && !empty($res[$pk])) {
					$row["pk"] = $res[$pk];
					$pkValue = $res[$pk];
				}
			}

			if ($laGrid) {
				$cssClass = "";

				if ($rNo % 2 === 0) {
					$cssClass .= " odd"; //  páratlan
				} else {
					$cssClass .= " even"; // páros
				}

				$cssClass .= " $pkValue";

				$row["cssClass"] = $cssClass;
			}
		}

		$cNo = 0;
		$colChanged = "";

		foreach ($columns as $column => $attrs) {
			if (!$this->isColumnVisibleInGrid($attrs) && !$excelExport && !$pdfExport) {
				continue;
			}

			if ($csvString) {
				$cellContent = '';
				if (isset($res[$column])) {
					$cellContent = $res[$column];
				}

				if (isset($attrs['showInHis']) && $attrs['showInHis'] == true) {
					$cellContent = $this->Grid2SecToHis($cellContent);
				}

				if (isset($attrs['showInDecimal']) && $attrs['showInDecimal'] == true) {
					$cellContent = $this->Grid2SecToDecimal($cellContent);
				}

				if ($attrs['col_type'] == "combo" && $attrs['options']['mode'] == Grid2Controller::G2BC_QUERY_MODE_ARRAY) {
					foreach ($attrs['options']['array'] as $combo) {
						if ($combo['id'] == $res[$column]) {
							$cellContent = $combo['value'];
						}
					}
				} else if ($attrs['col_type'] == "combo" && $attrs['options']['mode'] == Grid2Controller::G2BC_QUERY_MODE_SQL) {
					$sqlRes = dbFetchAll($attrs['options']['sql']);
					foreach ($sqlRes as $combo) {
						if ($combo['id'] == $res[$column]) {
							$cellContent = $combo['value'];
						}
					}
				}

				if ($column != $colChanged && $colChanged != "") {
					$row .= ";" . $cellContent;
				} else {
					$row .= $cellContent;
				}

				$colChanged = $column;
			} else {
				if (isset($multiHeaderWidths[$cNo]) && !isset($attrs["width"])) {
					$width = $multiHeaderWidths[$cNo];
				} else if (isset($attrs["width"])) {
					$width = $attrs["width"] == 'null' ? '0px; display:none' : $attrs["width"];
				} else {
					$width = null;
				}

                if (isset($attrs["align"])) {
					$align = $attrs["align"];
				} else {
					$align = "left";
				}

				$cellContent = "";

				if (isset($res[$column])) {
					$cellContent = $res[$column];
				}

				if (isset($attrs['showInHis']) && $attrs['showInHis'] == true) {
					$cellContent = $this->Grid2SecToHis($cellContent);
				}

				if (isset($attrs['showInDecimal']) && $attrs['showInDecimal'] == true) {
					$cellContent = $this->Grid2SecToDecimal($cellContent);
				}

				$row["columns"][$column]["data"] = $cellContent;
				$row["columns"][$column]["width"] = $width;
				$row["columns"][$column]["lineHeight"] = $lineHeight;
				$row["columns"][$column]["align"] = $align;

				if (isset($attrs["cssClass"])) {
					$row["columns"][$column]["cssClass"] = $attrs["cssClass"];
				}

				if (isset($attrs["title"])) {
					$row["columns"][$column]["title"] = $attrs["title"];
				}
			}

			$cNo++;
		}
	}

	/**
	 * Másodperceket alakítja át óra:perc formátumra
	 *
	 * @param int $seconds
	 * @return string
	 */
	protected function Grid2SecToHis($seconds = 0)
	{
		$sec = $seconds < 0 ? (-1) * $seconds : $seconds;

		$s = $sec % 60;
		$m = (($sec - $s) / 60) % 60;
		$h = floor($sec / 3600);

		$hLen = strlen((int)$h);
		$hLen = $hLen < 2 ? 2 : $hLen;
		$ret = substr("0" . $h, -$hLen) . ":" . substr("0" . $m, -2);

		$ret = $seconds < 0 ? "-" . $ret : $ret;

		return $ret;
	}

	/**
	 * Másodperceket alakítja át óra,pp formátumra
	 *
	 * @param int $seconds
	 * @return string
	 */
	protected function Grid2SecToDecimal($seconds = 0)
	{
		$ret = str_replace(".", ",", (string)rtrim(rtrim(sprintf ("%.2f", (int)$seconds/60/60), '0'), '.'));
		return $ret;
	}
}
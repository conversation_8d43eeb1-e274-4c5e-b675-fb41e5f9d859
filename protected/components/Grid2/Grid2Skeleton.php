<?php #yii2: done

trait Grid2Skeleton
{
	private $columnProperties = [];

	private $reportOrientation = 'portrait';

	private $reportHeaderEnabled = false;
	private $reportHeaderLeft = "";
	private $reportHeaderRight = "";
	private $reportHeaderLineEnabled = false;

	private $reportFooterEnabled = false;
	private $reportFooterLeft = "";
	private $reportFooterRight = "";
	private $reportFooterLineEnabled = false;

	private $dialogMaxWidth = 0;

	private $reportMode = false;

	private $gridPropertyTypes = array(
		"gridClass",
		"splitColumnEnabled",
		"splitColumn",
	);

	private $gridProperties = array();

	/**
	 * Grid
	 */
	protected function columns() {
		return [];
	}

	/**
	 * Headers (in this case we dont use columns for header generation)
	 */
	protected function headers() {
		return [];
	}

	/**
	 * Footers
	 */
	protected function footers() {
		return [];
	}

	/**
	 * Wiazrds
	 */
	protected function wizards() {
		return [];
	}

	/**
	 * Attributeslabels for SQL mode
	 */
	public function attributeLabels() {
		return array();
	}

	/**
	 * G2B Searchbar
	 */
	protected function search() {
		return array();
	}

	public static function isColumnVisibleInGrid($attrs) {
		return LAGridColumnProperties::isColumnVisibleInGrid($attrs);
	}

	public static function isColumnVisibleInWindow($attrs) {
		return LAGridColumnProperties::isColumnVisibleInWindow($attrs);
	}

	public static function isColumnVisibleInAddWindow($attrs) {
		return LAGridColumnProperties::isColumnVisibleInAddWindow($attrs);
	}

	public static function isColumnVisibleInEditWindow($attrs) {
		return LAGridColumnProperties::isColumnVisibleInEditWindow($attrs);
	}

	public static function isColumnVisibleInExport($attrs) {
		return LAGridColumnProperties::isColumnVisibleInExport($attrs);
	}

	protected function setColumnProperties($gridID = null, $delimiter = ";") {
		$attributeLabels = array();

		if (method_exists($this, "attributeLabels")) {
			$attributeLabels = $this->attributeLabels();

			if ($this->hasMultiGridMode() && !empty($gridID)) {
				if (isset($attributeLabels[$gridID])) {
					$attributeLabels = $attributeLabels[$gridID];
				} else {
					$attributeLabels = array();
				}
			}
		}

		$columns = $this->columns();

		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($columns[$gridID])) {
				$columns = $columns[$gridID];
			} else {
				$columns = array();
			}
		}

		$headers = $this->headers();

		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($headers[$gridID])) {
				$headers = $headers[$gridID];
			} else {
				$headers = array();
			}
		}

		$g2cp = new LAGridColumnProperties($columns, $headers, $this->LAGridDB->getModelName($gridID), $attributeLabels, $delimiter);
		$this->columnProperties = $g2cp->getGridColumns();
	}

	protected function getColumnProperties() {
		return $this->columnProperties;
	}

	protected function getGridLabels($gridID = null) {
		$this->G2BInit();

		$attributeLabels = array();

		if (method_exists($this, "attributeLabels")) {
			$attributeLabels = $this->attributeLabels();

			if ($this->hasMultiGridMode() && !empty($gridID)) {
				if (isset($attributeLabels[$gridID])) {
					$attributeLabels = $attributeLabels[$gridID];
				} else {
					$attributeLabels = array();
				}
			}
		}

		$columns = $this->columns();

		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($columns[$gridID])) {
				$columns = $columns[$gridID];
			} else {
				$columns = array();
			}
		}

		$g2cp = new LAGridColumnProperties($columns, [], $this->LAGridDB->getModelName($gridID), $attributeLabels);
		return $g2cp->getGridLabels();
	}

	protected function getGridLabelsSerialized($gridID = null) {
		$this->G2BInit();

		$attributeLabels = array();

		if (method_exists($this, "attributeLabels")) {
			$attributeLabels = $this->attributeLabels();

			if ($this->hasMultiGridMode() && !empty($gridID)) {
				if (isset($attributeLabels[$gridID])) {
					$attributeLabels = $attributeLabels[$gridID];
				} else {
					$attributeLabels = array();
				}
			}
		}

		$columns = $this->columns();

		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($columns[$gridID])) {
				$columns = $columns[$gridID];
			} else {
				$columns = array();
			}
		}

		$g2cp = new LAGridColumnProperties($columns, $this->LAGridDB->getModelName($gridID), $attributeLabels);
		return $g2cp->getGridLabelsSerialized();
	}

	protected function getLAColumnWidths($gridID = null) {
		$columnWidths = [];

		$columns = $this->columns();

		if ($this->hasMultiGridMode() && !empty($gridID)) {
//			$width = isset($columns[$columnName][$gridID]["width"])?$columns[$columnName][$gridID]["width"]:0;
			if (isset($columns[$gridID])) {
				$columns = $columns[$gridID];
			} else {
				$columns = array();
			}
		}

		foreach ($columns as $columnName => $attrs) {
			$width = $attrs["width"];

			$widthNumber = 0;

			if ($width !== "*" && is_numeric($width)) {
				$widthNumber = (int)$width;
			}

			$columnWidths[$columnName] = $widthNumber;
		}

		return $columnWidths;
	}

	protected function getColumnDropdowns($gridID = null) {
		$columns = $this->columns();
		if (!is_null($gridID) && is_array($columns) && array_key_exists($gridID, $columns)) {
			$columns = $columns[$gridID];
		}

		$g2cp = new LAGridColumnProperties($columns);
		return $g2cp->getGridDropdowns();
	}

	/**
	 * Additional Grid settings
	 */
	protected function setGridProperty($name, $value, $gridID = null) {
		if (in_array($name, $this->gridPropertyTypes)) {
			if ($this->hasMultiGridMode()) {
				if (!empty($gridID)) {
					$this->gridProperties[$gridID][$name] = $value;
				}
			} else {
				$this->gridProperties[$name] = $value;
			}

			return true;
		}

		return false;
	}

	protected function getGridProperties($gridID = null) {
		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($this->gridProperties[$gridID])) {
				return $this->gridProperties[$gridID];
			} else {
				return array();
			}
		}

		return $this->gridProperties;
	}

	protected function getGridProperty($name, $gridID = null) {
		if ($this->hasMultiGridMode() && !empty($gridID)) {
			if (isset($this->gridProperties[$gridID][$name])) {
				return $this->gridProperties[$gridID][$name];
			} else {
				return false;
			}
		}

		if (isset($this->gridProperties[$name])) {
			return $this->gridProperties[$name];
		}

		return false;
	}

	protected function setReportOrientation($reportOrientation) {
		if (in_array($reportOrientation, ["portrait", "landscape"])) {
			$this->reportOrientation = $reportOrientation;
		}
	}

	protected function getReportOrientation() {
		return $this->reportOrientation;
	}

	protected function setReportHeader($left = "", $right = "", $line = false) {
		$this->reportHeaderEnabled = true;
		$this->reportHeaderLeft = $left;
		$this->reportHeaderRight = $right;
		$this->reportHeaderLineEnabled = $line;
	}

	protected function setReportFooter($left = "", $right = "", $line = false) {
		$this->reportFooterEnabled = true;
		$this->reportFooterLeft = $left;
		$this->reportFooterRight = $right;
		$this->reportFooterLineEnabled = $line;
	}

	protected function isReportHeaderEnabled() {
		return $this->reportHeaderEnabled;
	}

	protected function isReportFooterEnabled() {
		return $this->reportFooterEnabled;
	}

	protected function getReportHeaderLeftContent() {
		return $this->reportHeaderLeft;
	}

	protected function getReportFooterLeftContent() {
		return $this->reportFooterLeft;
	}

	protected function getReportHeaderRightContent() {
		return $this->reportHeaderRight;
	}

	protected function getReportFooterRightContent() {
		return $this->reportFooterRight;
	}

	protected function isReportHeaderLineEnabled() {
		return $this->reportHeaderLineEnabled;
	}

	protected function isReportFooterLineEnabled() {
		return $this->reportFooterLineEnabled;
	}

	protected function setDialogMaxWidth($dialogMaxColumnCount = 4) {
		$dialogMaxWidth = (300 * $dialogMaxColumnCount) + 20;

		$this->dialogMaxWidth = $dialogMaxWidth;
	}

	protected function getDialogMaxWidth() {
		if (!$this->dialogMaxWidth) {
			$this->setDialogMaxWidth();
		}

		return $this->dialogMaxWidth;
	}

	/**
	 * Additional JS
	 */
	protected function getJS() {
		return '';
	}

	/**
	 * Additional JS files
	 */
	protected function getJSFiles() {
		return [];
	}

	/**
	 * Additional HTML
	 */
	protected function getHTML() {
		return '';
	}

	/**
	 * Additional HTML files
	 */
	protected function getREQFiles() {
		return [];
	}

	/**
	 * Report mode
	 */
	protected function enableReportMode() {
		$this->reportMode = true;
	}

	protected function isReportMode() {
		return $this->reportMode;
	}

	/**
	 * Visszaad egy tömböt, amiben oszlopszinten szabályozva van, hogy az adott oszlop (input)
	 * megjelenjen-e a form-on. A megjelenési adatokat a big_form_layout táblából kérjük le.
	 *
	 * @param $generateFrom - generálás forrása
	 * @param $model - CActiveRecord
	 *
	 * @return array
	 */
	public function getBigFormLayout($generateFrom = null, $model = null, $contentId = null) {
		$columns = $this->columns();
		if (empty($generateFrom) || !isset($columns[$generateFrom])) return [];

		$uploadDialogColumns = $columns[$generateFrom];

		$SQL = "
			SELECT
				`column`,
				`enabled`,
				`mandatory`
			FROM
				`big_form_layout`
			WHERE
				`controller_id` = '".$this->getControllerID()."'
		";

		if (!empty($generateFrom)) {
			$SQL .= "
				AND `generate_from` = '$generateFrom'
			";
		}

		if (!empty($model)) {
			$SQL .= "
				AND `model` = '$model'
			";
		}

		if (!empty($contentId)) {
			$SQL .= "
				AND `column` LIKE '$contentId%'
			";
		}

		$results = dbFetchAll($SQL);

		$dbColumns = [];
		foreach ($results as $res) {
			$dbColumns[$res["column"]]["enabled"] = (int)$res["enabled"];
			$dbColumns[$res["column"]]["mandatory"] = (int)$res["mandatory"];
		}

		$ret = [];
		foreach ($uploadDialogColumns as $column => $props) {
			$enabled = false;
			if (isset($dbColumns[$column]["enabled"]) && $dbColumns[$column]["enabled"]) {
				$enabled = true;
			} else if (!isset($dbColumns[$column]["enabled"])) {
				$enabled = true;
			}

			$mandatory = false;
			if ($enabled && isset($dbColumns[$column]["mandatory"]) && $dbColumns[$column]["mandatory"]) {
				$mandatory = true;
			}

			$ret[$column] = [
				"enabled" => $enabled,
				"mandatory" => $mandatory,
				"label_text" => isset($props['label_text']) ? $props['label_text'] : '',
			];
		}

		//Yang::log($contentId . '__' . CJSON::encode($uploadDialogColumns), 'log', 'sys.s');

		return $ret;
	}

	protected function getLaGridRowStart($id = '', $cssClass = '', $dataKey = '') {  #refactorThis // https://innote.login.hu/n-71pxkyj3
		$rowStart = '';

		$idHTML = "";

		if (!empty($id)) {
			$idHTML = 'id="'.$id.'"';
		}

		$rowStart .= '<tr '.$idHTML.' class="gridRow '.$cssClass.'" data-key="'.$dataKey.'">';

		return $rowStart;
	}

	protected function getLaGridRowEnd() {
		$rowEnd = '';

		$rowEnd .= '</tr>';

		return $rowEnd;
	}

	protected function getLAGridCell($width = 0, $height = 0, $id = '', $columnName = '', $cssClass = '', $content = '', $tabIndex = 1, $style = '', $pkValue = '') {

		//	Prefixek:
		//		$v*  = value of property
		//		$pr* = html property
		//		$ht* = html source
		//		$st* = css style

		static $sqlCache = [];
		static $cols; if(!$cols) $cols=$this->columns();	#refactorThis // ez a columns() ez nekem még gyanús

		$columnInfo = $cols;
		$dhtmlxInfo = $cols["dhtmlxGrid"] ?? null;
		$thisColumn = $columnInfo[$columnName] ?? [];
		$thisColDhx = $dhtmlxInfo[$columnName] ?? [];
		$colOptions = $thisColumn["options"] ?? [];

		if($width===""			) $width  = "0px";				// ha üreset kap, az 0px-et jelent
		if(is_numeric($width )	) $width  = "{$width}px";		//	pl ha auto, akkor nem px
		if(is_numeric($height)	) $height = "{$height}px";

		$prDataCol		= formatIfNotEmpty($columnName,' data-column="___"');
		$prTabIndex		= formatIfNotEmpty($tabIndex,' tabindex="___"');
		$prElementId	= formatIfNotEmpty($id,' id="___"');
		$stWidth		= formatIfNotEmpty($width,'width:___');
		$stHeight		= formatIfNotEmpty($height,'height:___');
		$vCustom		= rtrim($style,'; ');
		$vStyle			= trim("$stWidth;$stHeight;$vCustom","; ");
		$vClass			= trim("gridCell $columnName $cssClass");

		$cellType = ""
			?: ($thisColumn["col_type"] ?? "")
			?: ($thisColDhx["col_type"] ?? "")
			?: "normal" // ilyet egyelőre nem láttunk, viszont tud lenni pl "ed" --> ezek most a defaultra mennek
		;;

		switch($cellType) { #testThis // https://innote.login.hu/n-71p8vnax

			case "cb": // checkbox

//				xdebug_break();
				$isChecked	= ($content=='1');
				$prChecked	= ($isChecked) ? " checked":"";
				$prStyle	= formatIfNotEmpty($vStyle,' style="___"');
				$prClass	= formatIfNotEmpty($vClass,' class="___"');

				$htCell = <<<TD_CELL

						<td{$prElementId}{$prDataCol}{$prStyle}{$prClass}{$prTabIndex}>
							<div class="saveRolesInput">
								<input type="checkbox" class="saveAllRoles" name="{$columnName}[]" value="{$pkValue}" id="cb_{$pkValue}"{$prChecked}>
							</div>
						</td>
TD_CELL;

				break;

			case "combo": // #clarifyThis // Mit jelent ez pontosan?

				$vClass		= "hideInGrid $vClass";
				$prStyle	= formatIfNotEmpty($vStyle,' style="___"');
				$prClass	= formatIfNotEmpty($vClass,' class="___"');

				'Look up value by $content';{
					$valuesFrom = $colOptions['mode'] ?? null;
					if($valuesFrom === Grid2Controller::G2BC_QUERY_MODE_ARRAY) $valuesFrom = 'array';
					if($valuesFrom === Grid2Controller::G2BC_QUERY_MODE_SQL  ) $valuesFrom = 'sql';
					switch($valuesFrom) {
						case "array": $values = $colOptions['array'];break;
						case "sql":
							$sql	= $colOptions['sql'];
							$values = $sqlCache[$sql] ?? null;
								if(!$values) $sqlCache[$sql] = dbFetchAll($sql);
							$values = $sqlCache[$sql];
							break;
						;;
					}
					$lookup		= isset($values) ? array_combine( array_column($values,"id"), array_column($values,"value") ) : null;
					$meaning	= $lookup[$content] ?? null;
					if(!is_null($meaning)) $content = $meaning;
				}

				$htCell = "<td$prElementId$prDataCol$prStyle$prClass$prTabIndex>".$content."</td>";
				break;

			default: // pl "ed", "normal", stb

				$prStyle	= formatIfNotEmpty($vStyle,' style="___"');
				$prClass	= formatIfNotEmpty($vClass,' class="___"');
				$htCell		= "<td$prElementId$prDataCol$prStyle$prClass$prTabIndex>".$content."</td>";
				break;

			;

		}

		return $htCell;

	}

}

?>
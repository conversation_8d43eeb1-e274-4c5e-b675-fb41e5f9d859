<?php
use Components\Core\Enum\ApproverRelatedGroupEnum;
use Components\Core\Enum\AppSettingsIdEnum;
use Components\Grid2Core\Builder\Grid2CompanyAndPayrollSearchBuilder;
use Components\Grid2Core\Enum\Grid2ColumnFieldEnum;
use Components\Grid2Core\Enum\Grid2ColumnColTypeEnum;
use Components\Grid2Core\Enum\SearchFilterFieldEnum;
use Components\Grid2Core\Enum\SearchFilterGroupNameEnum;
use Components\Grid2Core\Guesser\GroupNameFilteredByCompanyPayrollRightGuesser;
use Components\Grid2Core\Guesser\GroupNameFromSearchFilterGuesser;
use Components\Grid2Core\Provider\DateSearchFilterProvider;
use Components\Grid2Core\Provider\GroupSearchFilterProvider;
use Components\Grid2Core\Provider\SearchFilterProvider;

Yang::loadComponentNamespaces('Core');
Yang::loadComponentNamespaces('Grid2Core');

trait Grid2PreDefinedBars
{
    private array $groupSearchFromDB;
    private array $dateSearchFromDB;

	public $preDefinedDateSearch;
	public $preDefinedGroupSearch;

	public array $processID;
	private bool $disableApprovalChecks = false;
	private int $publishedStatus = Status::PUBLISHED;


	protected function getPreDefinedSearch(
        $preDefinedDateSearch,
        $preDefinedGroupSearch = Grid2Controller::G2BC_SEARCH_WITH_DEFAULT_GROUP_FILTER,
        $processID = 'workForce',
        $disableApprovalChecks = false): array
    {

		$controllerID = $this->getControllerID();

		$this->preDefinedDateSearch		= $this->getPreDefinedDbDate($controllerID, $preDefinedDateSearch);
		$this->preDefinedGroupSearch	= $this->getPreDefinedDbGroup($controllerID, $preDefinedGroupSearch);
        $this->groupSearchFromDB = (new GroupNameFromSearchFilterGuesser())->guess($controllerID);
		$this->processID				= is_array($processID) ? $processID : [$processID];
		$this->disableApprovalChecks	= $disableApprovalChecks;

		$search = [];
		switch ($this->preDefinedDateSearch)
		{
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_FROM_TO:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_DATE:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_DATETIME:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_YEARMONTH:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_YEAR:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_Evaluation_Period:
				$search = $this->getEmployeeWithInterval();
				break;
			case Grid2Controller::G2BC_SEARCH_COG2_WITH_HIDDENYEARMONTH:
				$search = $this->getCogWithInterval(2);
				break;
		}

		if ($this->preDefinedDateSearch == Grid2Controller::G2BC_SEARCH_WITH_FLEX_GROUPS
				|| $this->preDefinedGroupSearch == Grid2Controller::G2BC_SEARCH_WITH_FLEX_GROUPS)
		{
			unset($search['submit']);
			$search = Yang::arrayMerge($search, $this->getFlexGroups($processID, $disableApprovalChecks));
		}
		return $search;
	}

	protected function getPreDefinedSearchFromDb(
        $processID = ApproverRelatedGroupEnum::NO_VISIBILITY_PERMISSION,
        bool $disableApprovalChecks = false
    ): array {
		$controllerID = $this->getControllerID();
		$this->preDefinedDateSearch		= $this->getPreDefinedDbDate($controllerID);
		$this->preDefinedGroupSearch	= $this->getPreDefinedDbGroup($controllerID);
        $this->groupSearchFromDB = (new GroupNameFromSearchFilterGuesser())->guess($controllerID);
		$this->processID				= is_array($processID) ? $processID : [$processID];;
		$this->disableApprovalChecks	= $disableApprovalChecks;

		$search = [];
		switch ($this->preDefinedDateSearch)
		{
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_FROM_TO:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_DATE:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_DATETIME:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_YEARMONTH:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_YEAR:
			case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_Evaluation_Period:
				$search = $this->getEmployeeWithInterval();
				break;
			case Grid2Controller::G2BC_SEARCH_COG2_WITH_HIDDENYEARMONTH:
				$search = $this->getCogWithInterval(2);
				break;
		}

		if ($this->preDefinedDateSearch == Grid2Controller::G2BC_SEARCH_WITH_FLEX_GROUPS
				|| $this->preDefinedGroupSearch == Grid2Controller::G2BC_SEARCH_WITH_FLEX_GROUPS)
		{
			unset($search['submit']);
			$search = Yang::arrayMerge($search, $this->getFlexGroups($processID, $disableApprovalChecks, $controllerID));

			$lockedItems = [];
			$lockedItems['wfm/standbyReport'] = 1;
			$lockedItems['wfm/missingWorktime'] = 1;

            if (isset($lockedItems[$controllerID])) {
                $locked = [
                    ['id' => '0', 'value' => Dict::getValue('notcloseddays')],
                    [
                        'id' => '1',
                        'value' => Dict::getValue('from_only_locked_data')
                    ],
                    [
                        'id' => 'ALL',
                        'value' => Dict::getValue('all')
                    ]
                ];
                $lockedExtra = [
                    'locked' => [
                        'col_type' => 'combo',
                        'label_text' => Dict::getValue('locked'),
                        'options' => [
                            'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
                            'array' => $locked,
                        ],
                        'default_value' => '0',
                    ]
                ];
                $submit = $search['submit'];
                unset($search['submit']);
                $search = Yang::arrayMerge($search, $lockedExtra);
                $search['submit'] = $submit;
            }
		}

		return $search;
	}


	protected function getEmployeeWithInterval(): array
    {
		list($ret, $intervalFilter) = $this->getDateSearch();
        $dateSearchNames = reset($intervalFilter);
        $dateSearchName = reset($dateSearchNames);
        $updateFields = (new GroupNameFilteredByCompanyPayrollRightGuesser())->guess($this->getControllerID());
        $companyPayrollSearchFilter = (new Grid2CompanyAndPayrollSearchBuilder())
            ->reset()
            ->setApproverProcessIds($this->processID)
            ->setDateSearchName($dateSearchName)
            ->setCompanySearchName(SearchFilterGroupNameEnum::COMPANY)
            ->setPayrollSearchName(SearchFilterGroupNameEnum::PAYROLL)
            ->setFieldsToUpdate($updateFields)
            ->build();
        $ret = empty($companyPayrollSearchFilter) ?
             Yang::arrayMerge(
                 $ret,
                 $this->getCompanySearch($intervalFilter),
                 $this->getPayrollSearch($intervalFilter)
             ) :
            Yang::arrayMerge($ret,$companyPayrollSearchFilter);

		$ret = Yang::arrayMerge($ret,$this->getWorkGroupSearch($intervalFilter));
		$ret = Yang::arrayMerge($ret,$this->getUnitSearch($intervalFilter));

		for($i=1;$i<=CompanyOrgGroup::PIECES;++$i)
		{
			$ret = Yang::arrayMerge($ret,$this->getCompanyOrgGroupSearch($i,$intervalFilter));
		}

		$ret = Yang::arrayMerge($ret,$this->getCostSearch($intervalFilter));
		$ret = Yang::arrayMerge($ret,$this->getCostCenterSearch($intervalFilter));

		for($i=1;$i<=EmployeeExt::OPTION_PIECES;++$i)
		{
			$ret = Yang::arrayMerge($ret,$this->getEmployeeExtOptionSearch($i,$intervalFilter));
		}

		for($i=1;$i<=EmployeeExt2::OPTION_PIECES;++$i)
		{
			$ret = Yang::arrayMerge($ret,$this->getEmployeeExt2OptionSearch($i,$intervalFilter));
		}

		$ret = Yang::arrayMerge($ret,$this->getEmployeeContractSearch($intervalFilter));

		$ret = Yang::arrayMerge(
			$ret,
			['submit' =>['col_type'=>'searchBarReloadGrid', 'width'=>'*', 'label_text'=>'']]
		);
		return $ret;
	}

	protected function getCogWithInterval($cog = 1): array
    {
		$defaultDateTimeStamp = strtotime(date('Y-m-d H:i:s'));

		$filter_defaultDateTime = App::getSetting('filter_defaultDateTime');
		$notEmpty_filter_defaultDateTime = !empty($filter_defaultDateTime);

		if ($notEmpty_filter_defaultDateTime) {
			$defaultDateTimeStamp = strtotime(App::getSetting('filter_defaultDateTime'));
		}

		switch ($this->preDefinedDateSearch) {
			case Grid2Controller::G2BC_SEARCH_COG2_WITH_HIDDENYEARMONTH:
			default:
				$intervalFilter = ['interval' => ['valid_month' => '{valid_month}',]];
				$interval = ['valid_month' => [
                    'col_type' => 'hidden',
                    'width' => '*',
                    'label_text' => Dict::getValue('valid_from'),
                    'default_value' => date('Y-m', $defaultDateTimeStamp),
                    'onchange' => ['company','payroll','workgroup','unit',]],];
				break;
		}

        $gacog = new GetActiveCompanyOrgGroup(
            $intervalFilter,
            $this->processID,
            $this->getControllerID(),
            $this->disableApprovalChecks,
            $cog
        );
		$cogSQL = $gacog->getSQL();

		$ret = Yang::arrayMerge(
			$interval,
			[
                'company_org_group' . $cog => [
                    'col_type' => 'auto',
                    'options' => [
                        'mode' => Grid2Controller::G2BC_QUERY_MODE_SQL,
                        'sql' => $cogSQL,
                    ],
                    'label_text' => Dict::getValue('company_org_group' . $cog),
                    'default_value' => '',
                ],
                'submit' => ['col_type' => 'searchBarReloadGrid', 'width' => '*', 'label_text' => ''],
			]
		);

		return $ret;
	}

	/**
	 * Flex csoport szűrők
	 * @param mixed $processID
	 * @param bool $disableApprovalChecks
	 * @param string $controllerId
	 * @return array
	 */
	protected function getFlexGroups(
        $processID = ApproverRelatedGroupEnum::NO_VISIBILITY_PERMISSION,
        $disableApprovalChecks = false,
        $controllerId = ''
    ): array {
		$pub			= Status::PUBLISHED;
		$end			= App::getSetting('defaultEnd');
		if (!$disableApprovalChecks) {
			$art		= new ApproverRelatedGroup();
			$compGarg	= $art->getApproverReleatedGroupSQL(
                'Company', $processID, userID(),
                '',
                'AND',
                'allDate', $controllerId);
			$payrollGarg= $art->getApproverReleatedGroupSQL(
                'Payroll', $processID, userID(),
                '',
                'AND',
                'allDate', $controllerId);
			$unitGarg	= $art->getApproverReleatedGroupSQL(
                'Unit', $processID, userID(),
                '',
                'AND',
                'allDate', $controllerId);
			$cog1Garg	= $art->getApproverReleatedGroupSQL(
                'CompanyOrgGroup1', $processID, userID(),
                '',
                'AND',
                'allDate', $controllerId);
			$cog2Garg	= $art->getApproverReleatedGroupSQL(
                'CompanyOrgGroup2', $processID, userID(),
                '',
                'AND',
                'allDate', $controllerId);
			$compGarg	= ['where' => $compGarg['where'] ?? ''];
			$payrollGarg= ['where' => $payrollGarg['where'] ?? ''];
			$unitGarg	= ['where' => $unitGarg['where'] ?? ''];
			$cog1Garg	= ['where' => $cog1Garg['where'] ?? ''];
			$cog2Garg	= ['where' => $cog2Garg['where'] ?? ''];
		} else {
			$compGarg	= ['where' => ''];
			$payrollGarg= ['where' => ''];
			$unitGarg	= ['where' => ''];
			$cog1Garg	= ['where' => ''];
			$cog2Garg	= ['where' => ''];
		}
		$allArray		= [['id' => 'ALL', 'value' => Dict::getValue('all')]];

		$mepsSQL = "
			SELECT
				`company`.`company_id` AS id,
				`company`.`company_name` AS value
			FROM `company`
			WHERE
					`company`.`status` = {$pub}
				AND	(IF('{valid_from}' = '', `company`.`valid_from`, '{valid_from}') <= IFNULL(`company`.`valid_to`, '{$end}'))
				AND (IF('{valid_to}' = '', IFNULL(`company`.`valid_to`, '{$end}'), '{valid_to}') >= `company`.`valid_from`)
				AND	(IF('{valid_year}' = '', `company`.`valid_from`, '{valid_year}-01-01') <= IFNULL(`company`.`valid_to`, '{$end}'))
				AND (IF('{valid_year}' = '', IFNULL(`company`.`valid_to`, '{$end}'), '{valid_year}-12-31') >= `company`.`valid_from`)
				AND	(IF('{valid_month}' = '', `company`.`valid_from`, '{valid_month}-01') <= IFNULL(`company`.`valid_to`, '{$end}'))
				AND (IF('{valid_month}' = '', IFNULL(`company`.`valid_to`, '{$end}'), LAST_DAY('{valid_month}-01')) >= `company`.`valid_from`)
				AND (IF('{valid_date}' = '', `company`.`valid_from`, '{valid_date}') BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}'))
				AND (IF('{valid_date}' = '' AND '{valid_month}' = '' AND '{valid_year}' = '' AND '{valid_to}' = '' AND '{valid_from}' = '', CURDATE(), `company`.`valid_from`) BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}'))
				AND `company`.`company_name` IS NOT NULL
				AND `company`.`company_name` <> ''
				AND `company`.`company_name` <> ' '
				{$compGarg['where']}
			GROUP BY id
			ORDER BY value
		";

		$locationsSQL = "
			SELECT
				`company_org_group_id` AS id,
				`company_org_group_name` AS value
			FROM `company_org_group1`
			LEFT JOIN `company` ON
					(`company`.`company_id` = `company_org_group1`.`company_id` OR `company_org_group1`.`company_id` = 'ALL')
				AND `company`.`status` = {$pub}
				AND `company_org_group1`.`valid_from` <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= IFNULL(`company_org_group1`.`valid_to`, '{$end}')
			LEFT JOIN `payroll` ON
					(`payroll`.`payroll_id` = `company_org_group1`.`payroll_id` OR `company_org_group1`.`payroll_id` = 'ALL')
				AND `payroll`.`status` = {$pub}
				AND `company_org_group1`.`valid_from` <= IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`valid_from` <= IFNULL(`company_org_group1`.`valid_to`, '{$end}')
			WHERE
					`company_org_group1`.`status` = {$pub}
				AND (`company_org_group1`.`company_id` IN ('{mep}') OR 'ALL' IN ('{mep}') OR `company_org_group1`.`company_id` = 'ALL')
				AND	(IF('{valid_from}' = '', `company_org_group1`.`valid_from`, '{valid_from}') <= IFNULL(`company_org_group1`.`valid_to`, '{$end}'))
				AND (IF('{valid_to}' = '', IFNULL(`company_org_group1`.`valid_to`, '{$end}'), '{valid_to}') >= `company_org_group1`.`valid_from`)
				AND	(IF('{valid_year}' = '', `company_org_group1`.`valid_from`, '{valid_year}-01-01') <= IFNULL(`company_org_group1`.`valid_to`, '{$end}'))
				AND (IF('{valid_year}' = '', IFNULL(`company_org_group1`.`valid_to`, '{$end}'), '{valid_year}-12-31') >= `company_org_group1`.`valid_from`)
				AND	(IF('{valid_month}' = '', `company_org_group1`.`valid_from`, '{valid_month}-01') <= IFNULL(`company_org_group1`.`valid_to`, '{$end}'))
				AND (IF('{valid_month}' = '', IFNULL(`company_org_group1`.`valid_to`, '{$end}'), LAST_DAY('{valid_month}-01')) >= `company_org_group1`.`valid_from`)
				AND (IF('{valid_date}' = '', `company_org_group1`.`valid_from`, '{valid_date}') BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '{$end}'))
				AND (IF('{valid_date}' = '' AND '{valid_month}' = '' AND '{valid_year}' = '' AND '{valid_to}' = '' AND '{valid_from}' = '', CURDATE(), `company_org_group1`.`valid_from`) BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '{$end}'))
				AND `company_org_group1`.`company_org_group_name` IS NOT NULL
				AND `company_org_group1`.`company_org_group_name` <> ''
				AND `company_org_group1`.`company_org_group_name` <> ' '
				{$compGarg['where']}
				{$payrollGarg['where']}
				{$cog1Garg['where']}
			GROUP BY id
			ORDER BY value
		";

		$unitsSQL = "
			SELECT
				`unit_id` AS id,
				`unit_name` AS value
			FROM `unit`
			LEFT JOIN `company` ON
					(`company`.`company_id` = `unit`.`company_id` OR `unit`.`company_id` = 'ALL')
				AND `company`.`status` = {$pub}
				AND `unit`.`valid_from` <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= IFNULL(`unit`.`valid_to`, '{$end}')
			LEFT JOIN `payroll` ON
					(`payroll`.`payroll_id` = `unit`.`payroll_id` OR `unit`.`payroll_id` = 'ALL')
				AND `payroll`.`status` = {$pub}
				AND `unit`.`valid_from` <= IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`valid_from` <= IFNULL(`unit`.`valid_to`, '{$end}')
			WHERE
					`unit`.`status` = {$pub}
				AND (`unit`.`company_id` IN ('{mep}') OR 'ALL' IN ('{mep}') OR `unit`.`company_id` = 'ALL')
				AND	(IF('{valid_from}' = '', `unit`.`valid_from`, '{valid_from}') <= IFNULL(`unit`.`valid_to`, '{$end}'))
				AND (IF('{valid_to}' = '', IFNULL(`unit`.`valid_to`, '{$end}'), '{valid_to}') >= `unit`.`valid_from`)
				AND	(IF('{valid_year}' = '', `unit`.`valid_from`, '{valid_year}-01-01') <= IFNULL(`unit`.`valid_to`, '{$end}'))
				AND (IF('{valid_year}' = '', IFNULL(`unit`.`valid_to`, '{$end}'), '{valid_year}-12-31') >= `unit`.`valid_from`)
				AND	(IF('{valid_month}' = '', `unit`.`valid_from`, '{valid_month}-01') <= IFNULL(`unit`.`valid_to`, '{$end}'))
				AND (IF('{valid_month}' = '', IFNULL(`unit`.`valid_to`, '{$end}'), LAST_DAY('{valid_month}-01')) >= `unit`.`valid_from`)
				AND (IF('{valid_date}' = '', `unit`.`valid_from`, '{valid_date}') BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '{$end}'))
				AND (IF('{valid_date}' = '' AND '{valid_month}' = '' AND '{valid_year}' = '' AND '{valid_to}' = '' AND '{valid_from}' = '', CURDATE(), `unit`.`valid_from`) BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '{$end}'))
				AND `unit`.`unit_name` IS NOT NULL
				AND `unit`.`unit_name` <> ''
				AND `unit`.`unit_name` <> ' '
				{$compGarg['where']}
				{$payrollGarg['where']}
				{$unitGarg['where']}
			GROUP BY id
			ORDER BY value
		";

		$ccsSQL = "
			SELECT
				`cost_center_id` AS id,
				`cost_center_name` AS value
			FROM `cost_center`
			LEFT JOIN `company` ON
					(`company`.`company_id` = `cost_center`.`company_id` OR `cost_center`.`company_id` = 'ALL')
				AND `company`.`status` = {$pub}
				AND `cost_center`.`valid_from` <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= IFNULL(`cost_center`.`valid_to`, '{$end}')
			LEFT JOIN `payroll` ON
					(`payroll`.`payroll_id` = `cost_center`.`payroll_id` OR `cost_center`.`payroll_id` = 'ALL')
				AND `payroll`.`status` = {$pub}
				AND `cost_center`.`valid_from` <= IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`valid_from` <= IFNULL(`cost_center`.`valid_to`, '{$end}')
			WHERE
					`cost_center`.`status` = {$pub}
				AND (`cost_center`.`company_id` IN ('{mep}') OR 'ALL' IN ('{mep}') OR `cost_center`.`company_id` = 'ALL')
				AND	(IF('{valid_from}' = '', `cost_center`.`valid_from`, '{valid_from}') <= IFNULL(`cost_center`.`valid_to`, '{$end}'))
				AND (IF('{valid_to}' = '', IFNULL(`cost_center`.`valid_to`, '{$end}'), '{valid_to}') >= `cost_center`.`valid_from`)
				AND	(IF('{valid_year}' = '', `cost_center`.`valid_from`, '{valid_year}-01-01') <= IFNULL(`cost_center`.`valid_to`, '{$end}'))
				AND (IF('{valid_year}' = '', IFNULL(`cost_center`.`valid_to`, '{$end}'), '{valid_year}-12-31') >= `cost_center`.`valid_from`)
				AND	(IF('{valid_month}' = '', `cost_center`.`valid_from`, '{valid_month}-01') <= IFNULL(`cost_center`.`valid_to`, '{$end}'))
				AND (IF('{valid_month}' = '', IFNULL(`cost_center`.`valid_to`, '{$end}'), LAST_DAY('{valid_month}-01')) >= `cost_center`.`valid_from`)
				AND (IF('{valid_date}' = '', `cost_center`.`valid_from`, '{valid_date}') BETWEEN `cost_center`.`valid_from` AND IFNULL(`cost_center`.`valid_to`, '{$end}'))
				AND (IF('{valid_date}' = '' AND '{valid_month}' = '' AND '{valid_year}' = '' AND '{valid_to}' = '' AND '{valid_from}' = '', CURDATE(), `cost_center`.`valid_from`) BETWEEN `cost_center`.`valid_from` AND IFNULL(`cost_center`.`valid_to`, '{$end}'))
				AND `cost_center`.`cost_center_name` IS NOT NULL
				AND `cost_center`.`cost_center_name` <> ''
				AND `cost_center`.`cost_center_name` <> ' '
				{$compGarg['where']}
				{$payrollGarg['where']}
			GROUP BY id
			ORDER BY value
		";

		$cog2sSQL = "
			SELECT
				`company_org_group_id` AS id,
				`company_org_group_name` AS value
			FROM `company_org_group2`
			LEFT JOIN `company` ON
					(`company`.`company_id` = `company_org_group2`.`company_id` OR `company_org_group2`.`company_id` = 'ALL')
				AND `company`.`status` = {$pub}
				AND `company_org_group2`.`valid_from` <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= IFNULL(`company_org_group2`.`valid_to`, '{$end}')
			LEFT JOIN `payroll` ON
					(`payroll`.`payroll_id` = `company_org_group2`.`payroll_id` OR `company_org_group2`.`payroll_id` = 'ALL')
				AND `payroll`.`status` = {$pub}
				AND `company_org_group2`.`valid_from` <= IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`valid_from` <= IFNULL(`company_org_group2`.`valid_to`, '{$end}')
			WHERE
					`company_org_group2`.`status` = {$pub}
				AND (`company_org_group2`.`company_id` IN ('{mep}') OR 'ALL' IN ('{mep}') OR `company_org_group2`.`company_id` = 'ALL')
				AND	(IF('{valid_from}' = '', `company_org_group2`.`valid_from`, '{valid_from}') <= IFNULL(`company_org_group2`.`valid_to`, '{$end}'))
				AND (IF('{valid_to}' = '', IFNULL(`company_org_group2`.`valid_to`, '{$end}'), '{valid_to}') >= `company_org_group2`.`valid_from`)
				AND	(IF('{valid_year}' = '', `company_org_group2`.`valid_from`, '{valid_year}-01-01') <= IFNULL(`company_org_group2`.`valid_to`, '{$end}'))
				AND (IF('{valid_year}' = '', IFNULL(`company_org_group2`.`valid_to`, '{$end}'), '{valid_year}-12-31') >= `company_org_group2`.`valid_from`)
				AND	(IF('{valid_month}' = '', `company_org_group2`.`valid_from`, '{valid_month}-01') <= IFNULL(`company_org_group2`.`valid_to`, '{$end}'))
				AND (IF('{valid_month}' = '', IFNULL(`company_org_group2`.`valid_to`, '{$end}'), LAST_DAY('{valid_month}-01')) >= `company_org_group2`.`valid_from`)
				AND (IF('{valid_date}' = '', `company_org_group2`.`valid_from`, '{valid_date}') BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '{$end}'))
				AND (IF('{valid_date}' = '' AND '{valid_month}' = '' AND '{valid_year}' = '' AND '{valid_to}' = '' AND '{valid_from}' = '', CURDATE(), `company_org_group2`.`valid_from`) BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '{$end}'))
				AND `company_org_group2`.`company_org_group_name` IS NOT NULL
				AND `company_org_group2`.`company_org_group_name` <> ''
				AND `company_org_group2`.`company_org_group_name` <> ' '
				{$compGarg['where']}
				{$payrollGarg['where']}
				{$cog2Garg['where']}
			GROUP BY id
			ORDER BY value
		";

		$search =
		[
			'mep' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('ext2_option17'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $mepsSQL,
						'array' => $allArray
				],
				'default_value'	=> 'ALL',
				'onchange'		=> ['company_org_group1_id', 'unit_id', 'cost_center_id', 'company_org_group2_id']
			],
			'company_org_group1_id' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('company_org_group1'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $locationsSQL,
						'array' => $allArray
				],
				'default_value'	=> 'ALL'
			],
			'unit_id' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('unit_id'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $unitsSQL,
						'array'	=> $allArray
					],
				'default_value'	=> 'ALL'
			],
			'cost_center_id' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('cost_center_id'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $ccsSQL,
						'array'	=> $allArray
					],
				'default_value'	=> 'ALL'
			],
			'company_org_group2_id' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('company') . ' ' . Dict::getValue('name'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $cog2sSQL,
						'array'	=> $allArray
					],
				'default_value'	=> 'ALL'
			],
			'submit' => ['col_type' => 'searchBarReinitGrid', 'width' => '*', 'label_text' => '']
		];

		return $search;
	}

	private function getDateSearch(): array
    {
		$intervalFilter = [];
		$interval = [];

		if($this->preDefinedDateSearch!==NULL)
		{
			$defaultDateTimeStamp = strtotime(date('Y-m-d H:i:s'));

			$filter_defaultDateTime = App::getSetting('filter_defaultDateTime');
			$notEmpty_filter_defaultDateTime = !empty($filter_defaultDateTime);

			if ($notEmpty_filter_defaultDateTime) {
				$defaultDateTimeStamp = strtotime(App::getSetting('filter_defaultDateTime'));
			}

			switch ($this->preDefinedDateSearch) {
				case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_DATE:
					$intervalFilter =	[
                        'interval' => [
                            'valid_date' => '{valid_date}',
                        ]
                    ];
					$interval = [
                        'valid_date'	=> ['col_type'=>'ed', 'dPicker'=>true, 'width'=>'*', 'label_text'=>Dict::getValue(
                            'date'
                        ), 'default_value'=>date('Y-m-d', $defaultDateTimeStamp), 'onchange' => ['company', 'mep', 'company_org_group1_id', 'payroll', 'workgroup', 'unit', 'unit_id', 'cost_center_id', 'company_org_group1', 'company_org_group2', 'company_org_group2_id', 'company_org_group3', 'employee_contract']],
                    ];
					break;
				case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_DATETIME:
					$intervalFilter =	[
                        'interval' => [
                            'valid_date' => '{valid_date}',
                        ]
                    ];
					$interval = [
                        'valid_date'	=> ['col_type'=>'ed', 'dtPicker'=>true, 'width'=>'*', 'label_text'=>Dict::getValue(
                            'date'
                        ), 'default_value'=>date('Y-m-d H:i:s', $defaultDateTimeStamp), 'onchange' => ['company', 'mep', 'company_org_group1_id', 'payroll', 'workgroup', 'unit', 'unit_id', 'cost_center_id', 'company_org_group1', 'company_org_group2', 'company_org_group2_id', 'company_org_group3', 'employee_contract']],
                    ];
					break;
				case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_YEARMONTH:
					$intervalFilter =	[
                        'interval' => [
                            'valid_month' => '{valid_month}',
                        ]
                    ];
					$interval = [
                        'valid_month'	=> ['col_type'=>'ed', 'mPicker'=>true, 'width'=>'*', 'label_text'=>Dict::getValue(
                            'valid_month'
                        ), 'default_value'=>date('Y-m', $defaultDateTimeStamp), 'onchange' => ['company', 'mep', 'company_org_group1_id', 'payroll', 'workgroup', 'unit', 'unit_id', 'cost_center_id', 'company_org_group1', 'company_org_group2', 'company_org_group2_id', 'company_org_group3', 'employee_contract']],
                    ];
					break;
				case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_YEAR:
					$intervalFilter =	[
                        'interval' => [
                            'valid_year' => '{valid_year}',
                        ]
                    ];
					$interval = [
                        'valid_year'	=> ['col_type'=>'ed', 'yPicker'=>true, 'width'=>'*', 'label_text'=>Dict::getValue(
                            'valid_from'
                        ), 'default_value'=>date('Y', $defaultDateTimeStamp), 'onchange' => ['company', 'mep', 'company_org_group1_id', 'payroll', 'workgroup', 'unit', 'unit_id', 'cost_center_id', 'company_org_group1', 'company_org_group2', 'company_org_group2_id', 'company_org_group3', 'employee_contract']],
                    ];
					break;
				case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_Evaluation_Period:
						$evaluationPeriod = new EvaluationPeriod();
						$evaluationCriteria = new CDbCriteria();
						$evaluationCriteria->condition = '`status`=' . $this->publishedStatus;
						$evaluationCriteria->order = 'valid_from DESC';
						$intervalFilter =	[
                            'interval' => [
                                'valid_from' => '1960-01-01',
																		'valid_to' => App::getSetting(
                                                                            'defaultEnd'
                                                                        )
                            ],
													'evaluation_period' => ['evaluation_period_id' => '{evaluation_period}']
                        ];
						$interval = [
                            'evaluation_period'	=> [
											'col_type'			=>'combo',
											'options'			=>	[
																		'mode'				=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
																		'modelSelectionModel'		=> $evaluationPeriod,
																		'modelSelectionCriteria'	=> $evaluationCriteria,
																		'comboId'				=> 'evaluation_period_id',
																		'comboValue'			=> 'evaluation_period_name',
                                            ],
											'label_text'=>Dict::getValue('evaluation_name'),
                            ],
                        ];
						break;
				case Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_FROM_TO:
					$intervalFilter =	[
                        'interval' => [
                            'valid_from' => '{valid_from}',
																		'valid_to' => '{valid_to}'
                        ]
                    ];
					$interval = [
                        'valid_from'	=> ['col_type'=>'ed', 'dPicker'=>true, 'width'=>'*', 'label_text'=>Dict::getValue(
                            'valid_from'
                        ), 'default_value'=>date('Y-m-01', $defaultDateTimeStamp), 'onchange' => ['company', 'mep', 'company_org_group1_id', 'payroll', 'workgroup', 'unit', 'unit_id', 'cost_center_id', 'company_org_group1', 'company_org_group2', 'company_org_group2_id', 'company_org_group3', 'employee_contract']],
										'valid_to'		=> ['col_type'=>'ed', 'dPicker'=>true, 'width'=>'*', 'label_text'=>Dict::getValue(
                                            'valid_to'
                                        ), 'default_value'=>date('Y-m-t', $defaultDateTimeStamp), 'onchange' => ['company', 'mep', 'company_org_group1_id', 'payroll', 'workgroup', 'unit', 'unit_id', 'cost_center_id', 'company_org_group1', 'company_org_group2', 'company_org_group2_id', 'company_org_group3', 'employee_contract']],
                    ];
					break;
			}
		}
		else
		{
			$searchBar = requestParam('searchInput');
            $validFrom = $searchBar['valid_from'] ?? $searchBar['date'] ?? date('Y-m-d');
            $validTo = $searchBar['valid_to'] ?? $searchBar['date'] ?? date('Y-m-d');
			$intervalFilter = ['interval' => [  'valid_from' => $validFrom,
                                                'valid_to' => $validTo]
            ];
		}
		return [$interval, $intervalFilter];
	}

	private function getCompanySearch($intervalFilter): array
    {
        if (!in_array(SearchFilterGroupNameEnum::COMPANY, $this->groupSearchFromDB)) {
            return [];
        }
        $search_type = $this->getSearchType(['COMPANY','DEFAULT_GROUP_FILTER']);
        $gac = new GetActiveCompany(
            $intervalFilter,
            $this->processID,
            $this->getControllerID(),
            $this->disableApprovalChecks,
            $search_type[Grid2ColumnFieldEnum::COL_TYPE]
        );
        $companySQL = $gac->getSQL();
        $clasParameter = $this->getClassName($search_type[Grid2ColumnFieldEnum::MULTIPLE]);
        return [
            SearchFilterGroupNameEnum::COMPANY => [
                Grid2ColumnFieldEnum::COL_TYPE => $search_type[Grid2ColumnFieldEnum::COL_TYPE],
                Grid2ColumnFieldEnum::MULTIPLE => $search_type[Grid2ColumnFieldEnum::MULTIPLE],
                Grid2ColumnFieldEnum::CLASS_PARAMETERS => $clasParameter,
                Grid2ColumnFieldEnum::OPTIONS => [
                    Grid2ColumnFieldEnum::OPTION_MODE => Grid2Controller::G2BC_QUERY_MODE_SQL,
                    Grid2ColumnFieldEnum::OPTION_SQL => $companySQL,
                    Grid2ColumnFieldEnum::OPTION_ARRAY =>
                        (App::getSetting(AppSettingsIdEnum::USE_COMPANY_AND_PAYROLL_RIGHTS)) ?
                            [] : [$search_type['all']],
                ],
                Grid2ColumnFieldEnum::LABEL_TEXT => Dict::getValue('company_id'),
                Grid2ColumnFieldEnum::ON_CHANGE => ['payroll', 'workgroup', 'unit', 'employee_contract'],
                Grid2ColumnFieldEnum::DEFAULT_VALUE => $search_type[Grid2ColumnFieldEnum::DEFAULT_VALUE]
            ],
        ];
	}

	private function getPayrollSearch($intervalFilter): array
    {
		$ret = [];
		if($this->preDefinedGroupSearch & Grid2Controller::G2BC_SEARCH_WITH_PAYROLL)
		{
			$payrollFilter = Yang::arrayMerge($intervalFilter,
				[
					'payroll' =>	[
						'company_id' => '{company}',
                    ]
                ]
			);
			$search_type=$this->getSearchType(['PAYROLL','DEFAULT_GROUP_FILTER']);
			$gap = new GetActivePayroll($payrollFilter, $this->processID, $this->getControllerID(), $this->disableApprovalChecks,$search_type['col_type']);
			$payrollSQL = $gap->getSQL();

			$ret = [
				'payroll'		=> [
										'col_type'			=> $search_type['col_type'],
										'multiple'			=> $search_type['multiple'],
										'class'				=> $this->getClassName($search_type['multiple']),
										'options'			=>	[
																	'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'	=> $payrollSQL,
																	'array'	=> [$search_type['all']],
                                        ],
										'label_text'		=> Dict::getValue('payroll_id'),
										'onchange'			=> ['workgroup','unit','employee_contract'],
										'default_value'		=> $search_type['default_value']
                ],
            ];
		}
		return $ret;
	}

	private function getWorkGroupSearch($intervalFilter): array
    {
		$ret = [];
		if($this->preDefinedGroupSearch & Grid2Controller::G2BC_SEARCH_WITH_WORKGROUP){
			$workgroupFilter = Yang::arrayMerge($intervalFilter,
									[
										'workgroup' =>	[
																'company_id' => '{company}',
																'payroll_id' => '{payroll}'
                                        ]
                                    ]
								);

			$search_type=$this->getSearchType(['WORKGROUP','DEFAULT_GROUP_FILTER']);
			$gawg = new GetActiveWorkgroup($workgroupFilter, $this->processID, $this->getControllerID(), $this->disableApprovalChecks,$search_type['col_type']);
			$workgroupSQL = $gawg->getSQL();

			$ret = [
				'workgroup'		=> [
										'col_type'			=> $search_type['col_type'],
										'multiple'			=> $search_type['multiple'],
										'class'				=> $this->getClassName($search_type['multiple']),
										'options'			=>	[
																	'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'	=> $workgroupSQL,
																	'array'	=> [$search_type['all']],
                                        ],
										'label_text'		=> Dict::getModuleValue('ttwa-base','workgroup'),
										'onchange'			=> ['employee_contract'],
										'default_value'		=> $search_type['default_value']
                ],
            ];
		}
		return $ret;
	}

	private function getUnitSearch($intervalFilter): array
    {
		$ret = [];
		if($this->preDefinedGroupSearch & Grid2Controller::G2BC_SEARCH_WITH_UNIT){
			$unitFilter = Yang::arrayMerge($intervalFilter,
									[
										'unit' =>	[
																'company_id' => '{company}',
																'payroll_id' => '{payroll}'
                                        ]
                                    ]
								);

			$search_type=$this->getSearchType(['UNIT','DEFAULT_GROUP_FILTER']);
			$gau = new GetActiveUnit($unitFilter, $this->processID, $this->getControllerID(), $this->disableApprovalChecks,$search_type['col_type']);
			$unitSQL = $gau->getSQL();

			$ret = [
				'unit'			=> [
										'col_type'			=> $search_type['col_type'],
										'multiple'			=> $search_type['multiple'],
										'class'				=> $this->getClassName($search_type['multiple']),
										'options'			=>	[
																	'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'	=> $unitSQL,
																	'array'	=> [$search_type['all']],
                                        ],
										'label_text'		=> Dict::getValue('unit_id'),
										'onchange'			=> ['employee_contract'],
										'default_value'		=> $search_type['default_value']
                ],
            ];
		}
		return $ret;
	}

	private function getCompanyOrgGroupSearch($type,$intervalFilter): array
    {
		$ret = [];

		$cog=0;
		$code="Grid2Controller::G2BC_SEARCH_WITH_COMPANY_ORG_GROUP$type";
		$cog = defined("$code") ? constant($code) : 0;

		if($this->preDefinedGroupSearch & $cog)
		{
			$search_type=$this->getSearchType(["COMPANY_ORG_GROUP$type"]);
			$gacog = new GetActiveCompanyOrgGroup($intervalFilter, $this->processID, $this->getControllerID(), $this->disableApprovalChecks, $type,$search_type['col_type']);
			$cogSQL = $gacog->getSQL();

			$ret = [
				"company_org_group$type"			=> [
										'col_type'			=> $search_type['col_type'],
										'multiple'			=> $search_type['multiple'],
										'class'				=> $this->getClassName($search_type['multiple']),
										'options'			=>	[
																	'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'	=> $cogSQL,
																	'array'	=> [$search_type['all']],
                                        ],
										'label_text'		=> Dict::getValue("company_org_group$type"),
										'onchange'			=> ['employee_contract'],
										'default_value'		=> $search_type['default_value']
                ],
            ];
		}
		return $ret;
	}

	private function getCostSearch($intervalFilter): array
    {
		$ret = [];
		if($this->preDefinedGroupSearch & Grid2Controller::G2BC_SEARCH_WITH_COST)
		{
			$filter = Yang::arrayMerge($intervalFilter,
									[
										'cost' =>	[
																'company_id' => '{company}',
																'payroll_id' => '{payroll}'
                                        ]
                                    ]
								);
			$search_type=$this->getSearchType(['COST']);
			$gac = new GetActiveCost($filter, $this->processID, $this->getControllerID(), $this->disableApprovalChecks,$search_type['col_type']);
			$SQL = $gac->getSQL();

			$ret = [
				'cost'			=> [
										'col_type'			=> $search_type['col_type'],
										'multiple'			=> $search_type['multiple'],
										'class'				=> $this->getClassName($search_type['multiple']),
										'options'			=>	[
																	'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'	=> $SQL,
																	'array'	=> [$search_type['all']],
                                        ],
										'label_text'		=> Dict::getValue('cost'),
										'onchange'			=> ['employee_contract'],
										'default_value'		=> $search_type['default_value']
                ],
            ];
		}
		return $ret;
	}

	private function getCostCenterSearch($intervalFilter): array
    {
		$ret = [];
		if($this->preDefinedGroupSearch & Grid2Controller::G2BC_SEARCH_WITH_COSTCENTER){
			$filter = Yang::arrayMerge($intervalFilter,
									[
										'cost_center' =>	[
																'company_id' => '{company}',
																'payroll_id' => '{payroll}'
                                        ]
                                    ]
								);
			$search_type=$this->getSearchType(['COSTCENTER']);
			$gacc = new GetActiveCostCenter($filter, $this->processID, $this->getControllerID(), $this->disableApprovalChecks,$search_type['col_type']);
			$SQL = $gacc->getSQL();

			$ret = [
				'cost_center'			=> [
										'col_type'			=> $search_type['col_type'],
										'multiple'			=> $search_type['multiple'],
										'class'				=> $this->getClassName($search_type['multiple']),
										'options'			=>	[
																	'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'	=> $SQL,
																	'array'	=> [$search_type['all']],
                                        ],
										'label_text'		=> Dict::getValue('costcenter'),
										'onchange'			=> ['employee_contract'],
										'default_value'		=> $search_type['default_value']
                ],
            ];
		}
		return $ret;
	}

	private function getEmployeeExtOptionSearch($type,$intervalFilter): array
    {
		$ret = [];

		$option = 0;
		$code = "Grid2Controller::G2BC_SEARCH_WITH_EMPLOYEE_EXT_OPTION$type";
		$option = defined("$code") ? constant($code) : 0;

		if($this->preDefinedGroupSearch & $option)
		{
			$search_type=$this->getSearchType(["EMPLOYEE_EXT_OPTION$type"]);
			$SQL= '';
            $optionType = OptionConfig::getType('option'.$type);
			if($optionType === OptionConfig::TYPE_ED || $optionType === OptionConfig::TYPE_DPICKER)
			{
				$SQL="
					SELECT DISTINCT
						option$type as `id`,
						option$type as `value`
					FROM `employee_ext`
					WHERE
							`status`=" . $this->publishedStatus . "
						AND option$type IS NOT NULL
						AND option$type!=''
						AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."')
					";
				if($search_type['col_type']=== 'auto')
				{
					$SQL.= "AND `option$type` like '%{search}%'
					";
				}
				$SQL.= 'GROUP BY  `id`
					ORDER BY  `value`
				';
			}
			else
			{
				$SQL= '
					SELECT
						option_value as `id`,
						option_value as `value`
					FROM `option`
					WHERE
							`status`=' . $this->publishedStatus . "
						AND option_id=$type
				";
				if($search_type['col_type']=== 'auto')
				{
					$SQL.= "AND `option_value` like '%{search}%'
					";
				}
				$SQL.= 'GROUP BY  `id`
					ORDER BY  `value`
				';
			}

			$ret = [
				"option$type"			=> [
										'col_type'			=> $search_type['col_type'],
										'multiple'			=> $search_type['multiple'],
										'class'				=> $this->getClassName($search_type['multiple']),
										'options'			=>	[
																	'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'	=> $SQL,
																	'array'	=> [$search_type['all']],
                                        ],
										'label_text'		=> Dict::getValue("option$type"),
										'onchange'			=> ['employee_contract'],
										'default_value'		=> $search_type['default_value']
                ],
            ];
		}

		return $ret;
	}

	private function getEmployeeExt2OptionSearch($type,$intervalFilter): array
    {
		$ret = [];

		$option = 0;
		$code = "Grid2Controller::G2BC_SEARCH_WITH_EMPLOYEE_EXT2_EXT2_OPTION$type";
		$option = defined("$code") ? constant($code) : 0;

		if($this->preDefinedGroupSearch & $option)
		{
			$search_type=$this->getSearchType(["EMPLOYEE_EXT2_EXT2_OPTION$type"]);
			$SQL= '';

			if(OptionConfig::getType($type)===OptionConfig::TYPE_ED || OptionConfig::getType($type)===OptionConfig::TYPE_DPICKER)
			{
				$SQL="
					SELECT DISTINCT
						ext2_option$type as `id`,
						ext2_option$type as `value`
					FROM `employee_ext2`
					WHERE
							`status`=" . $this->publishedStatus . "
						AND ext2_option$type IS NOT NULL
						AND ext2_option$type!=''
						AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."')
					";
				if($search_type['col_type']=== 'auto')
				{
					$SQL.= "AND `ext2_option$type` like '%{search}%'
					";
				}
				$SQL.= 'GROUP BY  `id`
					ORDER BY  `value`
				';
			}
			else
			{
				$SQL= '
					SELECT
						option_value as `id`,
						option_value as `value`
					FROM `option`
					WHERE
							`status`=' . $this->publishedStatus . "
						AND option_id=$type
				";
				if($search_type['col_type']=== 'auto')
				{
					$SQL.= "AND `option_value` like '%{search}%'
					";
				}
				$SQL.= 'GROUP BY  `id`
					ORDER BY  `value`
				';
			}

			$ret = [
				"ext2_option$type"			=> [
										'col_type'			=> $search_type['col_type'],
										'multiple'			=> $search_type['multiple'],
										'class'				=> $this->getClassName($search_type['multiple']),
										'options'			=>	[
																	'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'	=> $SQL,
																	'array'	=> [$search_type['all']],
                                        ],
										'label_text'		=> Dict::getValue("ext2_option$type"),
										'onchange'			=> ['employee_contract'],
										'default_value'		=> $search_type['default_value']
                ],
            ];
		}

		return $ret;
	}

	private function getEmployeeContractSearch($intervalFilter): array
	{
		$ret = [];
		if ($this->preDefinedGroupSearch & Grid2Controller::G2BC_SEARCH_WITH_EMPLOYEECONTRACT)
		{
			$searchBar	= requestParam('searchInput');
			$edFilter	= [
				'controllerId'	=> $this->getControllerID(),
				'search'		=> $searchBar,
				'nameFilter'	=> true
			];

			$tables = ['employee','employee_contract','company_org_group1','company_org_group2'];

			$approverParams = ['processId' => $this->processID];

			$GetActiveEmployeeData = new GetActiveEmployeeData_WithoutCalendar(
				$edFilter,
				$approverParams,
				$tables,
                'employee_contract.`employee_contract_id`',
				$this->disableApprovalChecks,
				$this->getControllerID()
			);
			$SQL = $GetActiveEmployeeData->getSQL();
			$SQL = '
				SELECT
					`employee_contract`.`employee_contract_id` AS `id`,
					' . Employee::getParam('fullname_with_emp_id_ec_id', ['employee', 'employee_contract']) . ' AS `value`
				' . substr($SQL,strpos($SQL, 'FROM')). '
				ORDER BY `value`
			';

			$searchType = $this->getSearchType(['EMPLOYEECONTRACT'], 'auto');

			$ret =
			[
				'employee_contract'	=>
				[
					'col_type'		=> $searchType['col_type'],
					'multiple'		=> $searchType['multiple'],
					'class'			=> $this->getClassName($searchType['multiple']),
					'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $SQL,
						'array'	=> [$searchType['all']]
					],
					'label_text'	=> Dict::getValue('name'),
					'default_value'	=> $searchType['default_value']
				]
			];
		}
		return $ret;
	}

	public static function getPreDefinedDbDate_before_20200813 ($controllerId,$preDefinedDateSearch=NULL): string
	{
		$searchFromFilter = "
				SELECT
					filter_id
				FROM search_filter
				WHERE
						`filter_type` = 'date'
					AND `controller_id` = '$controllerId'
					AND `status` = " . Status::PUBLISHED;
		$result = dbFetchRow($searchFromFilter);

		if (!empty($result)){
			$code = 'Grid2Controller::G2BC_SEARCH_' .$result['filter_id'];
			$dateSearch = defined("$code") ? constant($code) : $preDefinedDateSearch;
		} else {
			$dateSearch = $preDefinedDateSearch;
		}

		return $dateSearch;
	}

	public static function getPreDefinedDbDate($controllerId, $preDefinedDateSearch = NULL): ?int
	{
        if (empty($controllerId)) {
            return null;
        }
        if(!useExperimental('use new getPreDefinedDbDate method')) { #removeLater
			return self::getPreDefinedDbDate_before_20200813($controllerId,$preDefinedDateSearch);
		}

        $filterId = (new DateSearchFilterProvider())->provide($controllerId);

        $outValue = $preDefinedDateSearch;
        $g2bcCode = "Grid2Controller::G2BC_SEARCH_{$filterId}";
        if (defined($g2bcCode)) {
            $outValue = constant($g2bcCode);
        }

		return $outValue;
	}

	public static function getPreDefinedDbGroup_before_20200813 ($controllerId,$preDefinedGroupSearch=NULL): string
	{

		$searchFromFilter = "
				SELECT DISTINCT
					filter_id
				FROM search_filter
				WHERE
						`filter_type` = 'group'
					AND `controller_id` = '$controllerId'
					AND `status` = " . Status::PUBLISHED;
		$result = dbFetchAll($searchFromFilter);

		$groupSearch = 0;
		if (!empty($result)){

			foreach($result as $code) {

				$code = 'Grid2Controller::G2BC_SEARCH_WITH_' .$code['filter_id'];
				if (defined("$code")) {
					$groupSearch = constant($code);
				}
			}
		}

		if ($groupSearch === 0){
			$groupSearch = $preDefinedGroupSearch;

		}

		return $groupSearch;
	}

	public static function getPreDefinedDbGroup ($controllerId, $preDefinedGroupSearch = NULL): ?string
	{
        if (empty($controllerId)) {
            return null;
        }
		if(!useExperimental('use new getPreDefinedDbGroup method')) { #removeLater
			$outValue = self::getPreDefinedDbGroup_before_20200813($controllerId, $preDefinedGroupSearch);
			return $outValue;
		}
        $groupResults = (new GroupSearchFilterProvider())->provide($controllerId);

        $outValue = 0;
        foreach($groupResults as $filterID) {
            $g2bcCode = "Grid2Controller::G2BC_SEARCH_WITH_{$filterID}";
            if(defined($g2bcCode)) $outValue += constant($g2bcCode);
        }
        if(!$outValue) $outValue = $preDefinedGroupSearch;

		return $outValue;
	}

	private function getSearchType($filter_ids, $default_value = Grid2ColumnColTypeEnum::COMBO): array
	{
        $controllerID	= $this->getControllerID();
        $type = (new SearchFilterProvider())->provide($controllerID);
        $search_type = [];
        foreach($filter_ids as $fid) {
            if (isset($type[$controllerID][$fid])) {
                foreach($type[$controllerID][$fid] as $k => $v) {
                    if($k == SearchFilterFieldEnum::FILTER_ID) continue; // skip filter_id
                    $search_type[$k] = $v;
                }
            }
        }
        if(!$search_type) $search_type = null; // compatibility with original

		$ret = [];
        $ret[Grid2ColumnFieldEnum::COL_TYPE] = $search_type[SearchFilterFieldEnum::SEARCH_TYPE] ?? $default_value;
        $ret[Grid2ColumnFieldEnum::MULTIPLE] = $search_type[SearchFilterFieldEnum::MULTI_SELECT] ?? 0;
        $ret['all'] = $ret[Grid2ColumnFieldEnum::COL_TYPE] === Grid2ColumnColTypeEnum::COMBO ?
            [
                Grid2ColumnFieldEnum::OPTION_ARRAY_ID => 'ALL',
                Grid2ColumnFieldEnum::OPTION_ARRAY_VALUE => Dict::getValue('all')
            ] :
            [];
        $ret[Grid2ColumnFieldEnum::DEFAULT_VALUE] =
            $ret[Grid2ColumnFieldEnum::COL_TYPE] === Grid2ColumnColTypeEnum::COMBO  ? 'ALL' : '';

		return $ret;
	}

	public function columnRights($columnArray, $multiGrid = false): array
	{
		$retArr = [];

		$queryColumnRights = "
			SELECT
				column_id
			FROM
				`column_rights`
			LEFT JOIN `auth_rolegroup` ON
					`column_rights`.rolegroup_id = `auth_rolegroup`.rolegroup_id
			LEFT JOIN `user` ON
					`auth_rolegroup`.rolegroup_id = `user`.rolegroup_id
				AND CURDATE() BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`,'".App::getSetting('defaultEnd')."')
				AND	`user`.`status`=" . $this->publishedStatus . '
			WHERE
					`column_rights`.`status`=' . $this->publishedStatus . "
				AND (user_id ='".userID()."' OR `column_rights`.rolegroup_id='ALL')
				AND controller_id='".$this->getControllerID()."'
		";

		$result = dbFetchAll($queryColumnRights);

		$columns= [];

		for($i = 0; $i < count($result); ++$i) {
			$columns[] = $result[$i]['column_id'];
		}

		if(sizeof($columns)==1 && $columns[0] == 'ALL') {
			$retArr = $columnArray;
		}

		if ($multiGrid) {
			foreach ($columnArray as $grid => $cA) {
				foreach ($cA as $key => $value) {
					if(in_array($key, $columns)) {
						$retArr[$grid][$key] = $value;
					}
				}
			}
		} else {
			foreach ($columnArray as $key => $value) {
				if(in_array($key, $columns)) {
					$retArr[$key] = $value;
				}
			}
		}

		return $retArr;
	}

    private function getClassName($multiple = 0): string
    {
        return ($multiple) ? 'customSelect2Class' : '';
    }
}

<?php

trait Grid2StatusButtons
{

/**
	 * Status buttons
	 */
	protected function getStatusButtons($gridID = null) {
		if (!isset($gridID)) {
			$gridID = 'dhtmlxGrid';
		} else if (empty($gridID)) {
			$gridID = 'dhtmlxGrid';
		}
		
		$buttons = array();

		if ($this->hasRight("search", $gridID)) {
			$buttons["searchInGrid"] = array(
							"type" => "button",
							"id" => "searchInGrid",
							"class" => "searchInGrid",
							"name" => "searchInGrid",
							"img" => "/images/status_icons/st_search_opened.png",
							"label" => Dict::getValue("search")." (Ctrl+Shift+f)",
							"onclick" => "",
						);

		}

		if ($this->hasRight("search", $gridID)) {
			$buttons["searchInGridSelector"] = array(
							"type" => "selector",
						);
		}

		if ($this->hasRight("add", $gridID)) {
			$buttons["openAddDialog"] = array(
							"type" => "button",
							"id" => "openAddDialog",
							"class" => "openAddDialog",
							"name" => "openAddDialog",
							"img" => "/images/status_icons/st_add.png",
							"label" => Dict::getValue("add")/*."(Ctrl+Shift+a)"*/,
							"onclick" => "G2BDialog('dhtmlxGrid',0,'./dialog','./save','./gridData',null,'".$this->getControllerPageTitle()." - ".Dict::getValue("add_new_item")."','".Dict::getValue("please_select_line")."');",
						);
		}

		if ($this->hasRight("modify", $gridID)) {
			$buttons["openModDialog"] = array(
							"type" => "button",
							"id" => "openModDialog",
							"class" => "openModDialog",
							"name" => "openModDialog",
							"img" => "/images/status_icons/st_mod.png",
							"label" => Dict::getValue("modify")/*."(Ctrl+Shift+m)"*/,
							"onclick" => "G2BDialog('dhtmlxGrid',1,'./dialog','./save','./gridData',null,'".$this->getControllerPageTitle()." - ".Dict::getValue("modify")."','".Dict::getValue("please_select_line")."');",
						);
		}

		if ($this->hasRight("delete", $gridID)) {
			$gridID = 'dhtmlxGrid';
			
			$buttons["openDelDialog"] = array(
							"type" => "button",
							"id" => "openDelDialog",
							"class" => "openDelDialog",
							"name" => "openDelDialog",
							"img" => "/images/status_icons/st_del.png",
							"label" => Dict::getValue("delete")/*."(Ctrl+Shift+d)"*/,
							"onclick" => "G2BDelDialog('dhtmlxGrid','./gridData',null,$('.offCanvasSearchSection .to-serialize').serialize()+'&mainGridID=$gridID',controllerRights['$gridID'],'./delete','".Dict::getValue("are_yout_sure_to_delete")."','".Dict::getValue("please_select_line")."');",
						);
		}

		if ($this->hasRight("add", $gridID) || $this->hasRight("modify", $gridID) || $this->hasRight("delete", $gridID)) {
			$buttons[] = array(
							"type" => "selector",
						);
		}

		if ($this->hasRight("details", $gridID)) {
			$buttons["openDetailsDialog"] = array(
							"type" => "button",
							"id" => "openDetailsDialog",
							"class" => "openDetailsDialog",
							"name" => "openDetailsDialog",
							"img" => "/images/status_icons/st_details.png",
							"label" => Dict::getValue("details")/*."(Ctrl+Shift+t)"*/,
							"onclick" => "G2BDialog('dhtmlxGrid',2,'./dialog','./save','./gridData',null,'".Dict::getValue("details")."','".Dict::getValue("please_select_line")."');",
						);
		}

		if ($this->hasRight("details", $gridID)) {
			$buttons[] = array(
							"type" => "selector",
						);
		}

		if ($this->hasRight("reload", $gridID)) {
			if (!$this->isReportMode()) {
				$gridID = 'dhtmlxGrid';

				if ($this->hasLAGrid()) {
					$buttons["reloadGrid"] = array(
									"type" => "button",
									"id" => "reloadGrid",
									"class" => "reloadGrid",
									"name" => "reloadGrid",
									"img" => "/images/status_icons/st_rel.png",
									"label" => Dict::getValue("reload")/*." (Ctrl+Shift+r)"*/,
									"onclick" => "reloadAnimation();laGrid_$gridID.enableGridForceLoad('');laGrid_$gridID.loadGridData(null, true);",
								);
				} else {
					$buttons["reloadGrid"] = array(
									"type" => "button",
									"id" => "reloadGrid",
									"class" => "reloadGrid",
									"name" => "reloadGrid",
									"img" => "/images/status_icons/st_rel.png",
									"label" => Dict::getValue("reload")/*." (Ctrl+Shift+r)"*/,
									"onclick" => "reloadAnimation();loadGrid(/*gridID*/ 'dhtmlxGrid', /*gridDataUrl*/ './gridData', /*gridDataParams*/ $('.offCanvasSearchSection .to-serialize').serialize(), /*gridDropdownUrl*/ './dropdown', /*gridDropdowns*/ null, /*controllerRights*/ controllerRights, /*columnOrder*/ null);",
								);
				}
			} else {
				$buttons["reloadGrid"] = array(
								"type" => "button",
								"id" => "reloadGrid",
								"class" => "reloadGrid",
								"name" => "reloadGrid",
								"img" => "/images/status_icons/st_rel.png",
								"label" => Dict::getValue("reload")/*." (Ctrl+Shift+r)"*/,
								"onclick" => "reloadAnimation();loadReportGrid(/*gridID*/ 'reportGrid', /*gridDataParams*/ 'forceLoad=1&'+$('.offCanvasSearchSection .to-serialize').serialize(), /*controllerRights*/ controllerRights);",
							);
			}
		}
		
		if ($this->hasRight("export_xls", $gridID) || $this->hasRight("export_xlsx", $gridID) || $this->hasRight("export_pdf_node", $gridID)) {
			$buttons[] = array(
							"type" => "selector",
						);
		}

		if ($this->hasRight("export_xls", $gridID)) {
			$gridID = 'dhtmlxGrid';
			
			$buttons["exportXLS"] = array(
							"type" => "button",
							"id" => "exportXLS",
							"class" => "exportXLS",
							"name" => "exportXLS",
							"img" => "/images/status_icons/st_xls.png",
							"label" => Dict::getValue("export"),
							"onclick" => "exportDhtmlxGridXLS('XLS', $('.offCanvasSearchSection .to-serialize').serialize()+'&gridID=".$gridID."');",
						);
		}

		if ($this->hasRight("export_xlsx", $gridID)) {
			$gridID = 'dhtmlxGrid';
			
			$buttons["exportXLSX"] = array(
							"type" => "button",
							"id" => "exportXLSX",
							"class" => "exportXLSX",
							"name" => "exportXLSX",
							"img" => "/images/status_icons/st_xlsx.png",
							"label" => Dict::getValue("export"),
							"onclick" => "exportDhtmlxGridXLS('XLSX', $('.offCanvasSearchSection .to-serialize').serialize()+'&gridID=".$gridID."');",
						);
		}
		
		if ($this->hasRight("export_csv")) {	
			$buttons["exportCSV"] = array(
							"type" => "button",
							"id" => "exportCSV",
							"class" => "exportCSV",
							"name" => "exportCSV",
							"img" => "/images/status_icons/st_csv.png",
							"label" => Dict::getValue("export"),
							"onclick" => "exportDhtmlxGridCSV('', $('.offCanvasSearchSection .to-serialize').serialize()+'&gridID=".$gridID."');",
						);
		}

		if ($this->hasRight("export_pdf_node", $gridID)) {
			$buttons["exportPDFNode"] = array(
							"type" => "button",
							"id" => "exportPDF",
							"class" => "exportPDF",
							"name" => "exportPDF",
							"img" => "/images/status_icons/st_pdf.png",
							"label" => Dict::getValue("export"),
							"onclick" => "$('#reloadGrid').trigger('click');JReport(/*gridID*/ 'dhtmlxGrid', '".Yang::getParam('nodeHost')."htmlToPDF/', '".$this->getExportFileName()."', 'pdf', 'NODE', $('.offCanvasSearchSection .to-serialize').serialize());",
						);
		}
                if ($this->hasRight("save_state", $gridID)) {
			$buttons["saveState"] = array(
							"type" => "button",
							"id" => "openAddDialog",
							"class" => "openAddDialog",
							"name" => "openAddDialog",
							"img" => "/images/status_icons/st_multicheck.png",
							"label" => Dict::getValue("save"),
							"onclick" => "G2BSaveAllDialog('dhtmlxGrid','./gridData',null,$('.offCanvasSearchSection .to-serialize').serialize()+'&mainGridID=$gridID',controllerRights['$gridID'],'./saveAll','".Dict::getValue("are_you_sure_save_all")."');",
						);

		}

		if ($this->hasRight("regenerate_table", $gridID)) {
			$buttons["regenerateTable"] = array(
							"type" 		=> "datepickerCombo",
							"id" 		=> "datepickerCombo",
							"class" 	=> "exportCSV",
							"name" 		=> "datepicker",
							"img" 		=> "/images/buttons/refresh.png",
							"label" 	=> Dict::getValue("HRKPIupdate"),
							"onclick" 	=> "HRKPIdatesetter();",
						);

		}

		if ($this->hasRight("downloadCSV", $gridID)) {
			$buttons["csvDownload"] = array(
							"type" 		=> "button_1",
							"id" 		=> "csvDownload",
							"class" 	=> "exportCSV",
							"name" 		=> "csvDownload",
							"img" 		=> "/images/buttons/button_csv_download.png",
							"label" 	=> Dict::getValue("HRKPIcsvDownload"),
							"onclick" 	=> "HRKPIdataToCSV();",
						);

		}

		if ($this->hasRight("print", $gridID)) {
			$buttons["printing"] = [
				"type" => "button",
				"id" => "printing",
				"class" => "printing",
				"name" => "printing",
				"img" => "/images/status_icons/st_print.png",
				"label" => Dict::getModuleValue("ttwa-base","printing"),
				"onclick" => "printBodyContainerOfPage();"
			];
		}

		return $buttons;
	}

	public function actionGetStatusButtons() {
		$this->layout = "//layouts/ajax";
		$this->G2BInit();

		$gridID = requestParam('gridID');
		$gridID = empty($gridID) ? "dhtmlxGrid" : $gridID;

        foreach ($this->getStatusButtons($gridID) as $options) {
            if (is_null($options)) { continue; }
			if ($options["type"] === "button") {
				echo '<div style="width:30px;height:30px;display:inline-block;margin-left:5px;"><a href="#" onclick="'.$options["onclick"].'" id="'.$options["id"].'" class="'.$options["class"].'" name="'.$options["name"].'"><img id="'.$options["id"].'Icon" class="'.$options["class"].'Icon" src="'.$options["img"].'" alt="'.$options["label"].'" title="'.$options["label"].'" style="margin:0px;" /></a></div>';
			} else if ($options["type"] === "button_1") {
				echo '<div style="width:70px;height:30px;display:inline-block;margin-left:5px;"><input type="image" src="'.$options["img"].'" onclick="'.$options["onclick"].'" value="'.$options["label"].'" id="'.$options["id"].'" class="switchButton '.$options["class"].'" name="'.$options["name"].'" title="'.$options["label"].'" style="width:70px" /></div>';
			} else if ($options["type"] === "selector") {
				echo '<div style="width:5px;height:30px;display:inline-block;"></div>';
			} else if ($options["type"] === "text") {
				echo '<div style="height:30px;line-height:30px;display:inline-block;vertical-align: top;">'.$options["label"].'</div>';
			} else if ($options["type"] === "switch") {
				echo '
					<div style="width:80px;height:30px;display:inline-block;margin-left:5px;box-sizing: border-box;padding:1px 0px;vertical-align: top;">
						<input type="button" onclick="'.$options["onclick"].'" id="'.$options["id"].'" class="switchButton '.$options["class"].'" name="'.$options["name"].'" title="'.$options["label"].'" style="" />
					</div>
				';
			} else if ($options["type"] === "datepickerCombo") {
				echo '
					<div id="'.$options["id"].'" style="width:460px;height:30px;display:inline-block;margin-left:5px;box-sizing: border-box;padding:1px 0px;vertical-align: top;">
						<input data-error="" class="to-serialize" type="text" value="2020-10-15" name="hrkpi_validFrom" id="hrkpi_validFrom" style="position:relative;top:-10px;">
						<input data-error="" class="to-serialize" type="text" value="2020-10-15" name="hrkpi_validTo" id="hrkpi_validTo" style="position:relative;top:-10px;">
						<input type="image" src="'.$options["img"].'" onclick="'.$options["onclick"].'" value="'.$options["label"].'" id="" class="switchButton '.$options["class"].'" name="'.$options["name"].'" title="'.$options["label"].'" style="width:70px" />
						<script type="text/javascript">
							$(function() {
								$( "#hrkpi_validFrom" ).datepicker({dateFormat: "yy-mm-dd"});
								$( "#hrkpi_validTo" ).datepicker({dateFormat: "yy-mm-dd"});
							});
						</script>
					</div>
				';
			}
		}
	}
}

?>
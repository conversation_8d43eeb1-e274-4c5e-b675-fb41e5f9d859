<?php

trait Grid2
{
    private string $controllerID = '';

    private array $controllerRights = [];

    private string $controllerPageTitle = '';
    private string $controllerPageTitleId = 'undefined';

    private bool $multiGridMode = false;

    private bool $LAGrid = false;

    private bool $LAGridLight = false;

    protected int $maxDays = 31;

    /**
     * Controller management
     */
    protected function setControllerID(string $controllerID)
    {
        $this->controllerID = $controllerID;
    }

    public function getControllerID(): string
    {
        return $this->controllerID;
    }

    protected function G2BInit()
    {
        $this->LAGridRights->overrideInitRights('reload', true);
        $this->LAGridRights->controllerInitRights();
    }

    /**
     * Right management fallback
     */
    protected function hasRight($right, $gridID = null)
    {
        return $this->LAGridRights->hasRight($right, $gridID);
    }

    protected function getControllerRights($gridID = null)
    {
        return $this->LAGridRights->getControllerRights($gridID);
    }

    /**
     * Layout
     */
    protected function setControllerPageTitle($controllerPageTitle)
    {
        $this->controllerPageTitle = $controllerPageTitle;
    }

    public function getControllerPageTitle(): string
    {
        return $this->controllerPageTitle;
    }

    public function setControllerPageTitleId($controllerPageTitleId, $module = null, $params = [], $workEnd = '')
    {
        $this->controllerPageTitleId = $controllerPageTitleId;
        $this->controllerPageTitle = Dict::getValue($controllerPageTitleId, $params, $module) . $workEnd;
    }

    public function getControllerPageTitleId(): string
    {
        return $this->controllerPageTitleId;
    }

    public function getPageTitle(): string
    {
        return Dict::getValue(Yang::appName()) . ' - ' . $this->getControllerPageTitle();
    }

    /**
     * Get stored site preferences from DB
     */
    protected function getSitePreferences(): array
    {
        $user_id = userID();
        $controller_id = $this->getControllerID();

        $sql = "
			SELECT
				*
			FROM
				`g2b_site_preference`
			WHERE
				`user_id` = '$user_id' AND `controller_id` = '$controller_id' AND `status` = 2
		";

        $res_gsp = dbFetchAll($sql);

        $preferences['column_order'] = null;
        $preferences['column_visibility'] = null;

        if (count($res_gsp)) {
            $preferences['column_order'] = $res_gsp[0]['column_order'];
            $preferences['column_visibility'] = $res_gsp[0]['column_visibility'];
        }

        return $preferences;
    }

    /**
     * MultiGrid mode
     */
    protected function enableMultiGridMode(): void
    {
        $this->multiGridMode = true;
    }

    public function hasMultiGridMode(): bool
    {
        return $this->multiGridMode;
    }

    /**
     * LA Grid
     */
    protected function enableLAGrid(): void
    {
        $this->LAGrid = true;
    }

    protected function hasLAGrid(): bool
    {
        return $this->LAGrid;
    }

    /**
     * LA Grid Light
     */
    protected function enableLAGridLight(): void
    {
        $this->LAGridLight = true;
    }

    protected function hasLAGridLight(): bool
    {
        return $this->LAGridLight;
    }
}
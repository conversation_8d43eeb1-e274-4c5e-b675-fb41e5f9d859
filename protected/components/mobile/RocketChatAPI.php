<?php

/**
 * RocketChat API
 * https://developer.rocket.chat/reference/api
 * Helpful: https://mohammedlakkadshaw.com/blog/embedding-rocket-chat-using-iframe-auth.html/
 */
class RocketChatAPI
{
    /**
     * API url
     */
    const URL = "/api/v1/";

    /**
     * Init curl connection
     * @param string $url
     * @param string $header
     * @param json $request
     * @param integer $returnTransfer
     * @return mixed
     */
    private static function createCurlConnection($url, $header, $request, $returnTransfer = 0, $requestType = 'POST')
	{
		$curl = curl_init();

		curl_setopt($curl, CURLOPT_HEADER , 0);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, $returnTransfer);
		curl_setopt($curl, CURLOPT_URL, $url);

        if ($requestType === 'POST') {
            curl_setopt($curl, CURLOPT_POST, 0);
		    curl_setopt($curl, CURLOPT_POSTFIELDS, $request);
        }

		$resultOfCurl = self::executeCurl($curl,$returnTransfer);
		curl_close($curl);

		return $resultOfCurl;
	}

    /**
     * Execute curl
     * @param [type] $curl
     * @param int $returnTransfer
     * @return mixed
     */
	private static function executeCurl($curl, $returnTransfer)
	{
        $data = curl_exec($curl);
		if ($returnTransfer && $data !== false) {
			return $data;
		} else if (!$returnTransfer && $data !== false) {
			return true;
		} else {
			return false;
		}
	}

    /**
     * Remove auth tokens
     * @param string $apiEndpoint
     * @return void
     */
    public static function removeTokens($apiEndpoint = "users.removeOtherTokens")
    {
        if (Yang::session("rocketAdminAuthToken", "") != "" && Yang::session("rocketAdminUserId", "") != "")
        {
            $removeEndpoint = Yang::getParam("rocketChatURL") . self::URL . $apiEndpoint;
            $removeHeader   = ['POST', 'HTTP/1.1', 'X-Auth-Token: ' . Yang::session("rocketAdminAuthToken"), 'X-User-Id: ' . Yang::session("rocketAdminUserId"), 'Content-Type: application/json', 'charset=UTF-8'];
            $removeRequest  = json_encode([]);
            $apiResponse    = json_decode(self::createCurlConnection($removeEndpoint, $removeHeader, $removeRequest, 1), true);

            if (isset($apiResponse["success"]) && $apiResponse["success"] == "true") {
                Yang::log("Rocket API - removeTokens() - Success", "log", "system.RocketChat");
            } else {
                Yang::log("Rocket API - removeTokens() - Error", "log", "system.RocketChat");
            }
        } else {
            Yang::log("Rocket API - removeTokens() - Admin token / Admin UserId not set", "log", "system.RocketChat");
        }
    }

    /**
     * Retreive rocket admin auth token and user
     * @param string $apiEndpoint
     * @return array
     */
    public static function retreiveAdminTokenAndUser($apiEndpoint = "login")
    {
        $adminLogin = self::rocketLogin($apiEndpoint, "", Yang::getParam("rocketChatAdminUser"), Yang::getParam("rocketChatAdminPass"));
        return ["adminToken" => $adminLogin["userToken"], "adminUser" => $adminLogin["userUser"]];
    }

    /**
     * Generate user rocket auth token and user
     * @param string $apiEndpoint
     * @param string $authToken
     * @return array
     */
    public static function generateAuthTokenForUser($username, $apiEndpoint = "users.createToken")
    {
        if ($username != "" && Yang::session("rocketAdminAuthToken", "") != "" && Yang::session("rocketAdminUserId", "") != "")
        {
            $generateEndpoint  = Yang::getParam("rocketChatURL") . self::URL . $apiEndpoint;
            $generateHeader    = ['POST', 'HTTP/1.1', 'X-Auth-Token: ' . Yang::session("rocketAdminAuthToken"), 'X-User-Id: ' . Yang::session("rocketAdminUserId"), 'Content-Type: application/json', 'charset=UTF-8'];
            $generateRequest   = json_encode(["username" => $username]);
            $generateResponse  = json_decode(self::createCurlConnection($generateEndpoint, $generateHeader, $generateRequest, 1), true);
            if (isset($generateResponse["success"]) && $generateResponse["success"] == "true") {
                Yang::log("Rocket API - generateAuthTokenForUser() - Generating new token successfull", "log", "system.RocketChat");
                return ["token" => $generateResponse["data"]["authToken"]];
            } else {
                Yang::log("Rocket API - generateAuthTokenForUser() - Generating new token unsuccessfull", "log", "system.RocketChat");
                return ["token" => ""];
            }
        } else {
            Yang::log("Rocket API - generateAuthTokenForUser() - Rocket user / Admin token / Admin User Id missing", "log", "system.RocketChat");
            return ["token" => ""];
        }
    }

    /**
     * Return user rocket auth token and user
     * @param string $apiEndpoint
     * @param string $authToken
     * @return array
     */
    public static function rocketLogin($apiEndpoint = "login", $authToken = "", $user = "", $password = "")
    {
        if ($authToken == "" && ($user == "" || $password == ""))
        {
            Yang::log("Rocket API - rocketLogin() - Auth Token / User / Password is not set", "log", "system.RocketChat");
            return ["userToken" => "", "userUser" => ""];
        } else {
            $loginEndpoint      = Yang::getParam("rocketChatURL") . self::URL . $apiEndpoint;
            $loginHeader        = ['POST', 'HTTP/1.1', 'Content-Type: application/json', 'charset=UTF-8'];
            if ($authToken != "") {
                $loginRequest   = json_encode(["resume" => $authToken]);
            } else {
                $loginRequest   = json_encode(["username" => $user, "password" => $password]);
            }
            $loginResponse      = json_decode(self::createCurlConnection($loginEndpoint, $loginHeader, $loginRequest, 1), true);

            if (isset($loginResponse["status"]) && $loginResponse["status"] == "success") {
                Yang::log("Rocket API - rocketLogin() - Success", "log", "system.RocketChat");
                return ["userToken" => $loginResponse["data"]["authToken"], "userUser" => $loginResponse["data"]["userId"]];
            } else {
                Yang::log("Rocket API - rocketLogin() - Rocket auth error", "log", "system.RocketChat");
                return ["userToken" => "", "userUser" => ""];
            }
        }
    }

    /**
     * Send push notification token
     * @param string $apiEndpoint
     * @param string $token
     * @param string $type
     * @return array
     */
    public static function sendNotificationToken($token, $type, $apiEndpoint = "push.token")
    {
        $userToken  = Yang::session("rocketUserToken", "");
        $userId     = Yang::session("rocketUserId", "");
        if (empty($userToken) || empty($userId)) {
            Yang::log("Rocket API - sendNotificationToken() - user token / user id is missing", "log", "system.RocketChat");
            return ["success" => false];
        }
        $generateEndpoint   = Yang::getParam("rocketChatURL") . self::URL . $apiEndpoint;
        $generateHeader     =
        [
            'POST',
            'HTTP/1.1',
            'X-Auth-Token: ' . $userToken,
            'X-User-Id: ' . $userId,
            'Content-Type: application/json',
            'charset=UTF-8',
        ];
        $appName = Yang::getParam("rocketChatAppName");
        $generateRequest    = json_encode(["type" => $type, "value" => $token, "appName" => $appName]);
        $rawResponse        = self::createCurlConnection(
            $generateEndpoint,
            $generateHeader,
            $generateRequest,
            1
        );
        $generateResponse   = json_decode($rawResponse,true);
        if (isset($generateResponse["success"]) && $generateResponse["success"] == "true") {
            Yang::log("Rocket API - sendNotificationToken({$userId}, {$type}, {$appName}) - Notification push token successfull", "log", "system.RocketChat");
            return $generateResponse;
        } else {
            Yang::log("Rocket API - sendNotificationToken({$userId}, {$type}, {$appName}) - Notification push token unsuccessfull", "log", "system.RocketChat");
            return $generateResponse;
        }
    }

    /**
     * create Rocket User
     * @param string $email
     * @param string $name
     * @param string $username
     * @param string $roles
     * @param string $verified
     * @param string $password
     * @param string $apiEndpoint
     * @return array
     */
    public static function createUser($email, $name, $username, $roles = ['user'],  $verified = true, $password = "UxsZpKdH", $apiEndpoint = "users.create")
    {
        $generateRequest   = json_encode([
            "email" => $email,
            "name"  => $name,
            "username" => $username,
            "roles"    => $roles,
            "verified" => $verified,
            "password" => $password
        ]);
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint),
            self::generateAPIHeader(),
            $generateRequest,
            1
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $username);
    }

    /**
     * Get Rocket direct messages list
     * @param string $apiEndpoint
     * @return array
     */
    public static function getDirectMessagesList(string $apiEndpoint = "im.list") : array
    {
        $urlParams   = "";
        $rawResponse = self::createCurlConnection(
         self::generateAPIUrl($apiEndpoint, $urlParams),
         self::generateAPIHeader('GET'),
         NULL,
         1,
         'GET'
        );

        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "");
    }

    /**
     * Get Rocket Channels info
     * @param string $apiEndpoint
     * @return array
     */
    public static function getChannelsInfo(string $apiEndpoint = "channels.list") : array
    {
        $urlParams   = "";
        $rawResponse = self::createCurlConnection(
         self::generateAPIUrl($apiEndpoint, $urlParams),
         self::generateAPIHeader('GET'),
         NULL,
         1,
         'GET'
        );

        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "");
    }

    /**
     * Get Rocket Groups info
     * @param string $apiEndpoint
     * @return array
     */
    public static function getGroupsInfo(string $apiEndpoint = "groups.list") : array
    {
        $urlParams   = "";
        $rawResponse = self::createCurlConnection(
         self::generateAPIUrl($apiEndpoint, $urlParams),
         self::generateAPIHeader('GET'),
         NULL,
         1,
         'GET'
        );

        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "");
    }

    /**
     * Get Rocket Direct messages info
     * @param string $apiEndpoint
     * @return array
     */
    public static function getDirectMessagesInfo(string $apiEndpoint = "im.list") : array
    {
        $urlParams   = "";
        $rawResponse = self::createCurlConnection(
         self::generateAPIUrl($apiEndpoint, $urlParams),
         self::generateAPIHeader('GET'),
         NULL,
         1,
         'GET'
        );

        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "");
    }

    /**
     * Get Rocket Rooms info
     * @param string $apiEndpoint
     * @return array
     */
    public static function getRoomsInfo(string $apiEndpoint = "rooms.get") : array
    {
        $urlParams   = "";
        $rawResponse = self::createCurlConnection(
         self::generateAPIUrl($apiEndpoint, $urlParams),
         self::generateAPIHeader('GET'),
         NULL,
         1,
         'GET'
        );

        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "");
    }

    /**
     * Get Rocket Room info
     * @param string $roomId
     * @param string $apiEndpoint
     * @return array
     */
    public static function getRoomInfo(string $roomId, string $apiEndpoint = "rooms.info") : array
    {
        $urlParams   = "?roomId=" . $roomId;
        $rawResponse = self::createCurlConnection(
         self::generateAPIUrl($apiEndpoint, $urlParams),
         self::generateAPIHeader('GET'),
         NULL,
         1,
         'GET'
        );

        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $roomId);
    }

    /**
     * Get Rocket User channels message counter
     *
     * @param string $userId
     * @param string $userId
     * @param string $apiEndpoint
     * @return array
     */
    public static function getUserChannelMessageCounters(string $roomId = "", string $userId = "", string $userToken = "", string $apiEndpoint = "channels.counters") : array
    {
        $urlParams   = "?roomId=" . $roomId . "&userId=" . $userId;
        $rawResponse = self::createCurlConnection(
         self::generateAPIUrl($apiEndpoint, $urlParams),
         self::generateAPIHeader('GET', $userId, $userToken),
         NULL,
         1,
         'GET'
        );

        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $userId);
    }

    /**
     * Get Rocket User direct message counter
     *
     * @param string $userId
     * @param string $userId
     * @param string $apiEndpoint
     * @return array
     */
    public static function getUserDirectMessageCounters(string $roomId = "", string $userId = "", string $userToken = "", string $apiEndpoint = "im.counters") : array
    {
        $urlParams   = "?roomId=" . $roomId . "&userId=" . $userId;
        $rawResponse = self::createCurlConnection(
         self::generateAPIUrl($apiEndpoint, $urlParams),
         self::generateAPIHeader('GET', $userId, $userToken),
         NULL,
         1,
         'GET'
        );

        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $userId);
    }

    /**
     * Get Rocket User group message counter
     *
     * @param string $userId
     * @param string $userId
     * @param string $apiEndpoint
     * @return array
     */
    public static function getUserGroupMessageCounters(string $roomId = "", string $userId = "", string $userToken = "", string $apiEndpoint = "groups.counters") : array
    {
        $urlParams   = "?roomId=" . $roomId . "&userId=" . $userId;
        $rawResponse = self::createCurlConnection(
         self::generateAPIUrl($apiEndpoint, $urlParams),
         self::generateAPIHeader('GET', $userId, $userToken),
         NULL,
         1,
         'GET'
        );

        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $userId);
    }

    /**
     * get Rocket UserInfo
     * @param string $username
     * @param string $apiEndpoint
     * @return array
     */
    public static function getUserInfo($username, $apiEndpoint = "users.info")
    {
        $urlParams = "?username=" . $username;
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint, $urlParams),
            self::generateAPIHeader('GET'),
            NULL,
            1,
            'GET'
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $username);
    }

    /**
     * get Rocket UserList
     * @param string $apiEndpoint
     * @return array
     */
    public static function getUserList($apiEndpoint = "users.list")
    {
        $urlParams ="?count=0";
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint, $urlParams),
            self::generateAPIHeader('GET'),
            NULL,
            1,
            'GET'
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "");
    }

    /**
     * update Rocket User
     * @param string $userId
     * @param string $email
     * @param string $name
     * @param string $roles
     * @param string $apiEndpoint
     * @return array
     */
    public static function updateUser($userId, $email, $name, $roles, $apiEndpoint = "users.update")
    {
        $generateRequest   = json_encode([
            "userId" => $userId,
            "data" => [
                "email" => $email,
                "name"  => $name,
                "roles" => $roles
            ]
        ]);
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint),
            self::generateAPIHeader(),
            $generateRequest,
            1
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "email: $email name: $name roles: " . json_encode($roles));
    }

    /**
     * delete Rocket User
     * @param string $username
     * @param string $confirmRelinquish
     * @param string $apiEndpoint
     * @return array
     */
    public static function deleteUser($username, $confirmRelinquish = true, $apiEndpoint = "users.delete")
    {
        $generateRequest   = json_encode([
            "username" => $username,
            "confirmRelinquish"  => $confirmRelinquish
        ]);

        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint),
            self::generateAPIHeader(),
            $generateRequest,
            1
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $username);
    }

    /**
     * create Rocket Team
     * @param string $name
     * @param string $type
     * @param string $apiEndpoint
     * @return array
     */
    public static function createTeam($name, $type = 1, $apiEndpoint = "teams.create")
    {
        $generateRequest   = json_encode([
            "name" => $name,
            "type"  => $type
        ]);
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint),
            self::generateAPIHeader(),
            $generateRequest,
            1
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $name);
    }

    /**
     * get Rocket TeamList
     * @param string $apiEndpoint
     * @return array
     */
    public static function getTeamList($apiEndpoint = "teams.listAll")
    {
        $urlParams ="?count=0";
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint, $urlParams),
            self::generateAPIHeader('GET'),
            NULL,
            1,
            'GET'
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "");
    }

     /**
      * get Rocket TeamMembers
      * @param string $teamName
      * @param string $apiEndpoint
      * @return array
      */
     public static function getTeamMembers($teamName, $apiEndpoint = "teams.members")
    {
        $urlParams ="?teamName=" . $teamName;
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint, $urlParams),
            self::generateAPIHeader('GET'),
            NULL,
            1,
            'GET'
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "", false);
    }

    /**
     * delete Rocket Team
     * @param string $teamName
     * @param string $apiEndpoint
     * @return array
     */
    public static function deleteTeam($teamName, $apiEndpoint = "teams.delete")
    {
        $generateRequest   = json_encode([
            "teamName" => $teamName
        ]);
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint),
            self::generateAPIHeader(),
            $generateRequest,
            1
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $teamName);
    }

    /**
     * add Rocket TeamMembers
     * @param string $teamName
     * @param string $members
     * @param string $apiEndpoint
     * @return array
     */
    public static function addTeamMembers($teamName, $members, $apiEndpoint = "teams.addMembers")
    {
        $generateRequest   = json_encode([
            "teamName" => $teamName,
            "members"  => [$members]
        ]);
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint),
            self::generateAPIHeader(),
            $generateRequest,
            1
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, "teamName: $teamName members: $members");
    }

    /**
     * remove Rocket TeamMember
     * @param string $teamName
     * @param string $userId
     * @param string $apiEndpoint
     * @return array
     */
    public static function removeTeamMember($teamName, $userId, $apiEndpoint = "teams.removeMember")
    {
        $generateRequest   = json_encode([
            "teamName" => $teamName,
            "userId"  => $userId
        ]);
        $rawResponse        = self::createCurlConnection(
            self::generateAPIUrl($apiEndpoint),
            self::generateAPIHeader(),
            $generateRequest,
            1
        );
        return self::evaluateAPIResponse($rawResponse, __FUNCTION__, $teamName);
    }

    /**
     * Get Rocket Chat user ID
     *
     * @return mixed|null
     */
    public static function getUserID()
    {
        return Yang::session("rocketUserId");
    }

    /**
     * Get Rocket Chat user token
     *
     * @return mixed|null
     */
    public static function getUserToken()
    {
        return Yang::session("rocketUserToken");
    }

    /**
     * generate API Url
     * @param string $apiEndpoint
     * @param string $urlParams
     * @return string
     */
    public static function generateAPIUrl($apiEndpoint, $urlParams = '')
    {
        return Yang::getParam("rocketChatURL") . self::URL . $apiEndpoint . $urlParams;
    }

    /**
     * generate APIHeader
     * @param string $requestType
     * @return array
     */
    public static function generateAPIHeader($requestType = 'POST', $userId = "", $userToken = "")
    {
        $userId    = ($userId != "" ? $userId : Yang::session("rocketUserId"));
        $userToken = ($userToken != "" ? $userToken : Yang::session("rocketUserToken"));

        return [
            $requestType,
            'HTTP/1.1',
            'X-Auth-Token: ' . $userToken,
            'X-User-Id: ' . $userId,
            'Content-Type: application/json',
            'charset=UTF-8'
        ];
    }

    /**
     * evaluate APIResponse
     * @param string $rawResponse
     * @param string $functionName
     * @param string $logParams
     * @param string $isLogResult
     * @return array
     */
    public static function evaluateAPIResponse($rawResponse, $functionName, $logParams, $isLogResult = true)
    {
        $generateResponse  = json_decode($rawResponse, true);
        if ($isLogResult) {
            if (isset($generateResponse["success"]) && $generateResponse["success"] == "true") {
                Yang::log("Rocket API - {$functionName}: {$logParams} - successfull", "log", "system.RocketChat");
            } else {
                Yang::log(json_encode($generateResponse), "log", "system.RocketChat");
                Yang::log("Rocket API - {$functionName}: {$logParams} - unsuccessfull", "log", "system.RocketChat");
            }
        }
        return $generateResponse;
    }
}

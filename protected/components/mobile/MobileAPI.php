<?php

/**
 * Mobile layout API
 */
class MobileAPI
{
	/**
	 * Mobilon elérhető menü elemek
	 * <PERSON>s<PERSON> kulcs: controller id
	 * Első érték: dict_id
	 * @var array
	 */
	private static $menuListItems =
	[
		"ahp/absenceplanner"	 => "absenceManagement",
		"ahp/absenceapprove"	 => "menu_item_absence_approval",
		"wfm/viewschedule"		 => "workScheduleView",
		"documentViewer"		 =>	"menu_item_document_viewer",
        "wfm/worktimeManagement" => "worktimeManagement",
		"chat"					 => "page_title_message"
	];

	/**
	 * Search filter tábla megfeleltetések
	 * Első kulcs: táblában lévő azonosító
	 * Második kulcs: a filter neve (ua. mint normál layouton)
	 * Második kulcs érték: amennyiben van jogosultság ellenőrzés a "csoporthoz" akkor a Garghoz azonosító
	 * @var array
	 */
	private static $matchingSearchFilterIds =
	[
		"EMPLOYEE_WITH_FROM_TO"		=> ["valid_from", "valid_to"],
		"EMPLOYEE_WITH_DATE"		=> ["valid_date"],
		"EMPLOYEE_WITH_YEARMONTH"	=> ["valid_month"],
		"EMPLOYEE_WITH_YEAR"		=> ["valid_year"],
		"DEFAULT_GROUP_FILTER"		=> ["company" => "Company", "payroll" => "Payroll", "workgroup" => "Workgroup", "unit" => "Unit", "employee_contract" => "Employee"],
		"COMPANY"					=> ["company" => "Company"],
		"PAYROLL"					=> ["payroll" => "Payroll"],
		"COMPANY_ORG_GROUP1"		=> ["company_org_group1" => "CompanyOrgGroup1"],
		"COMPANY_ORG_GROUP2"		=> ["company_org_group2" => "CompanyOrgGroup2"],
		"COMPANY_ORG_GROUP3"		=> ["company_org_group3" => "CompanyOrgGroup3"],
		"WORKGROUP"					=> ["workgroup" => "Workgroup"],
		"UNIT"						=> ["unit" => "Unit"],
		"COSTCENTER"				=> ["cost_center"],
		"COST"						=> ["cost"],
		"EMPLOYEEPOSITION"			=> ["employee_position"],
		"EMPLOYEECONTRACT"			=> ["employee_contract" => "Employee"]
	];

	/**
	 * Keresők egymásra hatása
	 * Első kulcs: dátumban történt változás vagy csoport azonosítóban
	 * Második kulcs: dátum esetén a csoportosítások kereső azonosítója amit újra le kell kérni, csoportosítás esetén a változtatott csoport kereső azonosítója
	 * Második érték: dátum esetén a garg azonosító, csoportosítás esetén azok az alárendelt csoportok amiket újra le kell kérni a garg azonosítóval
	 * @var array
	 */
	private static $searchDependencies =
	[
		"DATE_DEPENDENCIES"			=>
		[
			["company" => "Company"],
			["payroll" => "Payroll"],
			["company_org_group1" => "CompanyOrgGroup1"],
			["company_org_group2" => "CompanyOrgGroup2"],
			["company_org_group3" => "CompanyOrgGroup3"],
			["workgroup" => "Workgroup"],
			["unit" => "Unit"],
			["employee_position"],
			["cost"],
			["cost_center"],
			["employee_contract" => "Employee"]
		],
		"GROUP_DEPENDENCIES"		=>
		[
			"company" => [
				["payroll" => "Payroll"],
				["company_org_group1" => "CompanyOrgGroup1"],
				["company_org_group2" => "CompanyOrgGroup2"],
				["company_org_group3" => "CompanyOrgGroup3"],
				["workgroup" => "Workgroup"],
				["unit" => "Unit"],
				["cost"],
				["cost_center"],
				["employee_contract" => "Employee"]
			],
			"payroll" => [
				["company_org_group1" => "CompanyOrgGroup1"],
				["company_org_group2" => "CompanyOrgGroup2"],
				["company_org_group3" => "CompanyOrgGroup3"],
				["workgroup" => "Workgroup"],
				["unit" => "Unit"],
				["cost"],
				["cost_center"],
				["employee_contract" => "Employee"]
			],
			"company_org_group1"	=> [["employee_contract" => "Employee"]],
			"company_org_group2"	=> [["employee_contract" => "Employee"]],
			"company_org_group3"	=> [["employee_contract" => "Employee"]],
			"workgroup"				=> [["employee_contract" => "Employee"]],
			"unit"					=> [["employee_contract" => "Employee"]],
			"employee_position"		=> [["employee_contract" => "Employee"]],
			"cost"					=> [["employee_contract" => "Employee"]],
			"cost_center"			=> [["employee_contract" => "Employee"]]
		]
	];

	/**
	 * Világvége
	 * @var string
	 */
	private static $end;

	/**
	 * Aktív
	 * @var int
	 */
	private static $pub = Status::PUBLISHED;

	/**
	 * Visszaadja a mobil menük listáját
	 * @param string $userId
	 * @return array
	 */
	public static function getMenuItems($userId)
	{
		$ret = ["start" => ["hasRight" => 1, "dict" => Dict::getValue("home_page")]];
		foreach (self::$menuListItems as $controllerId => $menuLabel) {
			$ret[$controllerId] = ["hasRight" => 0, "dict" => Dict::getValue($menuLabel)];
			if (App::getRight($controllerId, "view", null, $userId)) {
				$ret[$controllerId]["hasRight"] = 1;
			}
		}
		return $ret;
	}

	/**
	 * Visszaadja a hónapokat a nevével
	 * @return array
	 */
	public static function getMonths()
	{
		$months		=
		[
			"01" => Dict::getValue("january"),
			"02" => Dict::getValue("february"),
			"03" => Dict::getValue("march"),
			"04" => Dict::getValue("april"),
			"05" => Dict::getValue("may"),
			"06" => Dict::getValue("june"),
			"07" => Dict::getValue("july"),
			"08" => Dict::getValue("august"),
			"09" => Dict::getValue("september"),
			"10" => Dict::getValue("october"),
			"11" => Dict::getValue("november"),
			"12" => Dict::getValue("december")
		];

		return $months;
	}

	/**
	 * Helper a hónap napjaihoz (vagy csak a hónapba tartozókat, vagy úgy hogy hétfőtől vasárnapig a heteket)
	 * @param string $month
	 * @param boolean $onlyActualMonth
	 * @return array
	 */
	public static function getMonthDays($month = null, $onlyActualMonth = false)
	{
		if (is_null($month)) { $month = date("Y-m"); }
		$ret	= [];
		$to		= date("Y-m-d");
		$fromDt	= new DateTime($month . "-01");
		$from	= (!$onlyActualMonth) ? $fromDt->modify("monday this week")->format("Y-m-d") : $fromDt->format("Y-m-d");
		$toDt	= new DateTime($month . "-01");
		$toDt->modify("last day of this month");
		$end	= $toDt->format("Y-m-d");
		if (!$onlyActualMonth)
		{
			while (true)
			{
				$fromDt->modify('next sunday');
				if ($fromDt >= $toDt) {
					$to = $fromDt->format("Y-m-d");
					break;
				}
				$fromDt->modify('next monday');
			}
		} else { $to = $end; $fromDt = $toDt; }

		$ret["from"]	= $from;
		$ret["to"]		= $to;
		$ret["end"]		= $end;
		$ret["fromDt"]	= $fromDt;

		return $ret;
	}

	/**
	 * Visszaadja a beosztást
	 * @param array $ecIds
	 * @param string $month
	 * @param boolean $onlyActualMonth
	 * @param string $controllerId
	 * @return array
	 */
	public static function getSchedule($ecIds = [], $month = null, $onlyActualMonth = false, $controllerId = "")
	{
		$ret	= [];

		// Dátum inicializálás
		if (is_null($month)) { $month = date("Y-m"); }
		$dates	= self::getMonthDays($month, $onlyActualMonth);

		// Beosztás
		$myself = false;
		if (in_array(Yang::getUserECID(), $ecIds)) {
			$gews				= new GetEmployeeWorkSchedule($dates["from"], $dates["to"], [Yang::getUserECID()], true, "workSchedule", "2", false, false, $controllerId);
			$workScheduleOwn	= $gews->get();
			if (($key = array_search(Yang::getUserECID(), $ecIds)) !== false) {
				unset($ecIds[$key]);
			}
			$myself = true;
		}

		$gews			= new GetEmployeeWorkSchedule($dates["from"], $dates["to"], $ecIds, false, "workSchedule", "2", false, false, $controllerId);
		$workSchedule	= $gews->get();
		if ($myself) {
			$ecIds[]	= Yang::getUserECID();
			$workSchedule[Yang::getUserECID()] = $workScheduleOwn[Yang::getUserECID()];
		}

		// Napok
		for($i = new DateTime($dates["from"]); $i <= $dates["fromDt"]; $i->modify('+1 day'))
		{
			$day = $i->format("Y-m-d");
			foreach ($ecIds as $ecId)
			{
				$days						= (is_array($workSchedule[$ecId])) ? array_keys($workSchedule[$ecId]) : [];
				$ret[$ecId][$day]["day"]	= $i->format("j");
				if (!isset($ret[$ecId]["month"])) {
                    $ret[$ecId]["month"]	= str_replace("-", ".", $month) . ".";
                }
				if (in_array($day, $days))
				{
					$ret[$ecId][$day]["classes"] = "day";
					if ($workSchedule[$ecId][$day]["used_type_of_daytype"] == "RESTDAY") {
						$ret[$ecId][$day]["restday"]	= Dict::getValue("absence_type_restday");
						$ret[$ecId][$day]["start"]		= "";
						$ret[$ecId][$day]["end"]		= "";
					} else {
						$ret[$ecId][$day]["restday"]	= "";
						$ret[$ecId][$day]["start"]		= $workSchedule[$ecId][$day]["used_work_start"];
						$ret[$ecId][$day]["end"]		= $workSchedule[$ecId][$day]["used_work_end"];
					}
				} else {
					$ret[$ecId][$day]["classes"]		= "day";
					$ret[$ecId][$day]["restday"]		= "";
					$ret[$ecId][$day]["start"]			= "";
					$ret[$ecId][$day]["end"]			= "";
				}

				if (strtotime($day) < strtotime($month . "-01"))	{ $ret[$ecId][$day]["classes"] .= " prev"; }
				if ($day == date("Y-m-d"))							{ $ret[$ecId][$day]["classes"] .= " today-red"; }
				if (strtotime($day) > strtotime($dates["end"]))		{ $ret[$ecId][$day]["classes"] .= " next"; }
				if (date('N', strtotime($day)) >= 6)				{ $ret[$ecId][$day]["classes"] .= " highlighted"; }
			}
		}

		return $ret;
	}

	/**
	 * Visszaadja a távolléteket
	 * @param array $ecIds
	 * @param string $month
	 * @param string $onlyActualMonth
	 * @param array $absences
	 * @param array $validities
	 * @return array
	 */
	public static function getAbsences($ecIds = [], $month = null, $onlyActualMonth = false, $absences = [], $validities = [])
	{
		$ret			= [];
		$clickableYear	= [];

		// Dátum inicializálás
		if (is_null($month)) { $month = date("Y-m"); }
		$dates	= self::getMonthDays($month, $onlyActualMonth);

		// Távollétek
		if (empty($absences)) {
			$gea	= new GetEmployeeAbsences();
			$abs	= $gea->get($dates["from"], $dates["to"], $ecIds);
		} else {
			$abs	= $absences;
		}

		// Ünnepek
		$ph			= new GetPublicHoliday($dates["from"], $dates["to"]);
		$pha		= $ph->get();
		$phs		= [];

		// Napok
		for($i = new DateTime($dates["from"]); $i <= $dates["fromDt"]; $i->modify('+1 day'))
		{
			$day = $i->format("Y-m-d");
			foreach ($ecIds as $ecId)
			{
				$days			= (is_array($abs[$ecId])) ? array_keys($abs[$ecId]) : [];
				if (!isset($phs[$ecId])) {
					$country	= GetPublicHoliday::getCountryByEcId($ecId);
					$phs[$ecId] = isset($pha[$country]) ? $pha[$country] : [];
				}
				$ret[$ecId][$day]["day"]		= $i->format("j");
				$ret[$ecId][$day]["dataTags"]	= [];
				if (in_array($day, $days) && $abs[$ecId][$day]["state_type_id"] != null)
				{
					$ret[$ecId][$day]["classes"]					= "day";
					$ret[$ecId][$day]["style"]						= 'style="background-color: ' . $abs[$ecId][$day]["bgcolor"] . ' !important; color: #fff;"';
					$ret[$ecId][$day]["svg"]						= "";
					$ret[$ecId][$day]["dataTags"]["abs-dict-id"]	= $abs[$ecId][$day]["name_dict_id"];
					$ret[$ecId][$day]["dataTags"]["note"]			= $abs[$ecId][$day]["note"];
					$ret[$ecId][$day]["dataTags"]["abs-status"]		= $abs[$ecId][$day]["status"];
					$ret[$ecId][$day]["dataTags"]["row-id"]			= $abs[$ecId][$day]["row_id"];
					if ($abs[$ecId][$day]["status"] == Status::DRAFT)								{ $ret[$ecId][$day]["svg"] = '<svg viewBox="0 0 16 16"><use xlink:href="#icon-question-circle"></use></svg>'; }
					if ($abs[$ecId][$day]["status"] == self::$pub)									{ $ret[$ecId][$day]["svg"] = '<svg viewBox="0 0 16 16"><use xlink:href="#icon-check-circle"></use></svg>'; }
					if ($abs[$ecId][$day]["status"] == Status::REJECTED)							{ $ret[$ecId][$day]["svg"] = '<svg viewBox="0 0 16 16"><use xlink:href="#icon-exclamation-circle"></use></svg>'; }
					if ($abs[$ecId][$day]["status"] == Status::DRAFT_DELETE)						{ $ret[$ecId][$day]["svg"] = '<svg viewBox="0 0 16 16"><use xlink:href="#icon-check-circle"></use></svg><svg viewBox="0 0 16 16"><use xlink:href="#icon-question-circle"></use></svg>'; }
				} else {
					$ret[$ecId][$day]["classes"]	= "day";
					$ret[$ecId][$day]["style"]		= "";
					$ret[$ecId][$day]["svg"]		= "";
					if (array_key_exists($day, $phs[$ecId]))										{ $ret[$ecId][$day]["classes"] .= " trholiday"; }
				}

				if (array_key_exists($day, $phs[$ecId]))											{ $ret[$ecId][$day]["dataTags"]["holiday-dict-id"]	= $phs[$ecId][$day]["name_dict_id"]; }
				if (array_key_exists($day, $phs[$ecId]) && !is_null($phs[$ecId][$day]["chdate"]))	{ $ret[$ecId][$day]["dataTags"]["holiday-chdate"]	= $phs[$ecId][$day]["chdate"]; }
				// TODO ? - SN: Dolgozó csoportja távolléteinél szerintem ez "lassabb", minden csoportba tartozó munkavállalónak lekéri minden napjára hogy "locked" nap-e
				$notLocked = self::checkAbsDayLocked($ecId, $i, $dates["from"], $dates["to"], $abs);
				$clickableYear[$ecId][$i->format("Y-m-d")] = $notLocked;
				if (!$notLocked)																	{ $ret[$ecId][$day]["svg"] .= '<svg viewBox="0 0 16 16"><use xlink:href="#icon-lock-circle"></use></svg>'; }
				if (!$notLocked)																	{ $ret[$ecId][$day]["dataTags"]["locked"] = "1"; } else { $ret[$ecId][$day]["dataTags"]["locked"] = "0"; }
				if (strtotime($day) < strtotime($month . "-01"))									{ $ret[$ecId][$day]["classes"] .= " prev"; }
				if ($day == date("Y-m-d"))															{ $ret[$ecId][$day]["classes"] .= " today"; }
				if (strtotime($day) > strtotime($dates["end"]))										{ $ret[$ecId][$day]["classes"] .= " next"; }
				if (date('N', strtotime($day)) >= 6 && !array_key_exists($day, $phs[$ecId]))		{ $ret[$ecId][$day]["classes"] .= " highlighted"; }

				// Több dolgozó esetén a dolgozó a napon érvényes-e éppen a csoportban
				$found = false;
				if (!empty($validities))
				{
					foreach ($validities[$ecId] as $k => $v)
					{
						$date		= date("Y-m-d", strtotime($day));
						$startDate	= date("Y-m-d", strtotime($v["valid_from"]));
						$endDate	= date("Y-m-d", strtotime($v["valid_to"]));
						if ($date >= $startDate && $date <= $endDate) { $found = true; }
					}
					if (!$found)
					{
						// Ürítés, nem érvényes a napon a csoportban
						$ret[$ecId][$day]				= [];
						$ret[$ecId][$day]["classes"]	= "day";
						$ret[$ecId][$day]["day"]		= "";
						$ret[$ecId][$day]["dataTags"]	= [];
						$ret[$ecId][$day]["style"]		= "";
						$ret[$ecId][$day]["svg"]		= "";
					}
				}
			}
		}

		Yang::setSessionValue('ahpplannerdata_clickableyear', $clickableYear);

		return $ret;
	}

	/**
	 * Lockos nap-e vagy sem --> Grid2AHPPlannerData base ($clickable)
	 * @param string $ecId
	 * @param DateTime $checkDate
	 * @param string $from
	 * @param string $to
	 * @param array $abs
	 * @return boolean
	 */
	public static function checkAbsDayLocked($ecId, $checkDate, $from, $to, $abs)
	{
		// Init
		$gews	= new GetEmployeeWorkSchedule($from, $to, [$ecId], true);
		$ws		= $gews->get();
		if (count($ws) > 0) {
			$nowDate = AHPSaveMethods::getNowDate(Yang::getUserRoleGroup(), $ecId, $ws[$ecId]);
		} else {
			$nowDate = AHPSaveMethods::getNowDate(Yang::getUserRoleGroup(), $ecId, null);
		}
		$allDaysEditable			= App::getRight("ahp/absenceplanneryearview", "all_days_editable");
		$allEmployeesEditable		= App::getRight("ahp/absenceplanneryearview", "all_employees_editable");
		$allDaysEditableHimself		= App::getRight("ahp/absenceplanneryearview", "all_days_editable_for_himself");
		$absPlannerYearCanEdit		= App::getRight("ahp/absenceplanneryearview", "absenceplanneryear_can_edit");
		self::$end					= App::getSetting("defaultEnd");
		$canEditLockedDays			= App::getRight("ahp/absenceplanneryearview", "can_edit_locked_days");
		$wfm						= weHaveModule('ttwa-wfm');
		$absenceOnLockDays			= (int)App::getSetting("absence_on_lock_days");
		$employeeValid				= GetEmployeeValids::GetContractValids($ecId, $checkDate->format("Y"));
		$approverValid				= GetEmployeeValids::GetApproverValids("absenceApprover", $ecId, $checkDate->format("Y"));
		$approvableEmployee			= AHPSaveMethods::checkEmployeeIsApprovable(["interval" => ["valid_from" => $checkDate->format("Y") . "-01-01", "valid_to" => $checkDate->format("Y") . "-12-31"], "employee_contract" => ["employee_contract_id" => $ecId]], "ahp/absenceplanneryearview");
		$ret						= false;

		if (!$allDaysEditable)
		{
			if ($checkDate <= $nowDate->min_date || $checkDate >= $nowDate->max_date) {
				$ret = false;
			} else {
				if (!$allEmployeesEditable) {
					if ($ecId === Yang::getUserECID()) {
						$ret = true;
					}
				} else {
					$ret = true;
				}
			}
		} else {
			if (!$allEmployeesEditable) {
				if ($ecId === Yang::getUserECID()) {
					$ret = true;
				}
			} else {
				if ($checkDate <= $nowDate->min_date && $ecId === Yang::getUserECID()) {
					if ($allDaysEditableHimself) {
						$ret = true;
					}
				} else {
					$ret = true;
				}
			}
		}

		$isExt4AHPLimitation = self::getExt4option11AHPLimitation($ecId);
		if ($isExt4AHPLimitation) {
			$ret = false;
		}

		if (!$canEditLockedDays && isset($abs[$ecId][$checkDate->format("Y-m-d")]['locked']) && (int)$abs[$ecId][$checkDate->format("Y-m-d")]['locked'] && $wfm) {
			$ret = false;
		}
		if($absenceOnLockDays === 0 && $wfm)
		{
			$lockSelect = "
				SELECT
					`day`
				FROM `employee_calc_used_daytype`
				WHERE
						`employee_contract_id`= '{$ecId}'
					AND `day` BETWEEN '{$from}' AND '{$to}'
					AND `status` = " . Status::LOCKED;

			$resClock = dbFetchAll($lockSelect);
			if (isset($resClock) AND is_array($resClock)) {
				foreach ($resClock as $lockRec) {
					$lockDays[$lockRec['day']] = true;
				}
			}
			if (isset($lockDays[$checkDate->format("Y-m-d")]) && $lockDays[$checkDate->format("Y-m-d")]) {
				$ret = false;
			}
		}

		$employeeActive = false;
		if (is_array($employeeValid)) {
			for($i = 0; $i < count($employeeValid); $i++) {
				$employeeActive = $employeeActive || ($employeeValid[$i]['valid_from'] <= $checkDate->format("Y-m-d") && $employeeValid[$i]['valid_to'] >= $checkDate->format("Y-m-d"));
			}
			$ret = $ret && ($employeeActive === false ? false : true);
		}
		$approverActive=false;
		if (is_array($approverValid)) {
			for($i = 0; $i < count($approverValid); $i++) {
				$approverActive = $approverActive || ($approverValid[$i]['valid_from'] <= $checkDate->format("Y-m-d") && $approverValid[$i]['valid_to'] >= $checkDate->format("Y-m-d"));
			}
		}
		if (!($approvableEmployee && $employeeActive && $approverActive) && $ecId !== Yang::getUserECID()) {
			$ret = false;
		}
		if ($absPlannerYearCanEdit && $employeeActive && !(isset($lockDays[$checkDate->format("Y-m-d")]) && $lockDays[$checkDate->format("Y-m-d")])) {
			$ret = true;
			if ($checkDate <= $nowDate->min_date || $checkDate >= $nowDate->max_date) {
				$ret = false;
			}
		}

		return $ret;
	}

	/**
	 * Clickable ext4 option disable check
	 * @param string $ecId
	 * @return bool
	 */
	public static function getExt4option11AHPLimitation(string $ecId = "") :bool
	{
		if (App::getSetting("ahpLimitationByExt4Option11") == "0") {
            return false;
        }
        $ext4option11AHPLimitation	= explode(";", App::getSetting("ahpLimitationByExt4Option11"));
		self::$end					= App::getSetting("defaultEnd");

        $ext4SQL = "
            SELECT `employee_ext4`.`ext4_option11`
            FROM `employee_ext4`
            JOIN `employee_contract` ON
                    `employee_contract`.`employee_id` = `employee_ext4`.`employee_id`
                AND	`employee_contract`.`status` = " . self::$pub . "
                AND CURDATE() BETWEEN `employee_contract`.`valid_from`
                AND IFNULL(`employee_contract`.`valid_to`, '" . self::$end . "')
            WHERE
                `employee_ext4`.`status` = " . self::$pub . "
                AND CURDATE() BETWEEN `employee_ext4`.`valid_from`
                AND IFNULL(`employee_ext4`.`valid_to`, '" . self::$end . "')
                AND employee_contract.employee_contract_id = '" . $ecId . "'
        ";

        $ext4					= dbFetchValue($ext4SQL);
        $isExt4AHPLimitation	= in_array($ext4, $ext4option11AHPLimitation);
        return $isExt4AHPLimitation;
	}

	/**
	 * Grid2AHPPlannerActions copy
	 * @param string $ecId
	 * @param string $year
	 * @return array
	 */
	public static function getSummaryData($ecId, $year)
	{
		$ret = [];

		$settingAbsenceFloorRounding				= App::getSetting("absence_floor_rounding");
		$settingAbsenceCalculationHour				= App::getSetting("absence_calculation_hour");
		$settingshowAbsenceInHour					= App::getSetting("showAbsencesInHours");
		$settingFooterDataPlusSummaryData			= App::getSetting("settingFooterDataPlusSummaryData");
		$stateTypeId								= App::getSetting('getBaseAbsencesByStateType');
		$absenceValuesAreTheNumberOfDecimalPlaces	= (int)App::getSetting('absenceValuesAreTheNumberOfDecimalPlaces');

		$baseAbsences	= AHPAbsenceFunctions::getBaseAbsencesByStateType($stateTypeId);
		$frame			= AHPEmployeeFunctions::getEmployeeAbsenceFrameByEcID($ecId, $year, $baseAbsences);
		$used			= AHPEmployeeFunctions::getEmployeeAbsenceUsedByEcID($ecId, $year, false, $baseAbsences);
		$dailyWorktime	= AHPEmployeeFunctions::getEmployeeDailyWorktime($ecId, true, $year);

		if ($settingAbsenceCalculationHour == '1' && $settingshowAbsenceInHour === '0') {
			if ((int)$dailyWorktime === 0) { $dailyWorktime = 8; }
			$frame	= round( ($frame / $dailyWorktime), $absenceValuesAreTheNumberOfDecimalPlaces);
		} else {
			$frame	= round($frame, $absenceValuesAreTheNumberOfDecimalPlaces);
			$used	= round($used, $absenceValuesAreTheNumberOfDecimalPlaces);
		}
		$available = round( ($frame-$used), $absenceValuesAreTheNumberOfDecimalPlaces);

		if ($settingAbsenceFloorRounding == '1')
		{
			$frame		= floor($frame);
			$used		= floor($used);
			$available	= floor($available);
		}

		if ($settingFooterDataPlusSummaryData)
		{
			$footerData = self::footerDataResult($ecId, $year);
			$frame		+= $footerData['frame'];
			$used		+= $footerData['used'];
			$available	+= $footerData['available'];
		}

		$ret["frame"]		= $frame;
		$ret["used"]		= $used;
		$ret["available"]	= $available;

		return $ret;
	}

	/**
	 * Grid2AHPPlannerActions copy
	 * @param string $ecId
	 * @param string $year
	 * @return array
	 */
	public static function footerDataResult($ecId, $year)
	{
		$result = [];

		$settingAbsenceFloorRounding				= App::getSetting("absence_floor_rounding");
		$settingAbsenceCalculationHour				= App::getSetting("absence_calculation_hour");
		$settingshowAbsenceInHour					= App::getSetting("showAbsencesInHours");
		$stateTypeId 								= App::getSetting('getBaseAbsencesByStateType');
		$absenceValuesAreTheNumberOfDecimalPlaces	= (int)App::getSetting('absenceValuesAreTheNumberOfDecimalPlaces');

		$frame			= AHPEmployeeFunctions::getEmployeeAbsenceFrameByEcID($ecId, $year);
		$used			= AHPEmployeeFunctions::getEmployeeAbsenceUsedByEcID($ecId, $year);
		$dailyWorktime	= AHPEmployeeFunctions::getEmployeeDailyWorktime($ecId, false, $year);
		if ($settingAbsenceCalculationHour == '1' && $settingshowAbsenceInHour === '0') {
			$frame		= round( ($frame / $dailyWorktime), $absenceValuesAreTheNumberOfDecimalPlaces);
		} else {
			$frame		= round($frame, $absenceValuesAreTheNumberOfDecimalPlaces);
			$used		= round($used, $absenceValuesAreTheNumberOfDecimalPlaces);
		}

		$available		= round( ($frame - $used), $absenceValuesAreTheNumberOfDecimalPlaces);
		$baseAbsences	= AHPAbsenceFunctions::getBaseAbsencesByStateType($stateTypeId);
		$absFrame		= AHPEmployeeFunctions::getEmployeeAbsenceFrameByEcID($ecId, $year, $baseAbsences);
		$absUsed		= AHPEmployeeFunctions::getEmployeeAbsenceUsedByEcID($ecId, $year, false, $baseAbsences);

		if ($settingAbsenceCalculationHour == '1' && $settingshowAbsenceInHour === '0') {
			$absFrame	= round( ($absFrame / $dailyWorktime), $absenceValuesAreTheNumberOfDecimalPlaces);
		} else {
			$absFrame	= round($absFrame, $absenceValuesAreTheNumberOfDecimalPlaces);
			$absUsed	= round($absUsed, $absenceValuesAreTheNumberOfDecimalPlaces);
		}
		$absAvailable	= round( ($absFrame - $absUsed), $absenceValuesAreTheNumberOfDecimalPlaces);

		$frame		-= $absFrame;
		$used		-= $absUsed;
		$available	-= $absAvailable;

		if ($settingAbsenceFloorRounding == '1') {
			$frame		= floor($frame);
			$used		= floor($used);
			$available	= floor($available);
		}

		$result['frame'] 		= $frame;
		$result['used'] 		= $used;
		$result['available']	= $available;

		return $result;
	}

	/**
	 * Visszaadja az eltérést az időarányos szabitól
	 * @param string $ecId
	 * @param string $year
	 * @return array
	 */
	public static function getProRata($ecId, $year)
	{
		$prorata				= [];
		$showExtraWords			= (int)App::getSetting('absenceplanneryearview_show_extra_words') === 1;
		$showAbsencesInHours	= (int)App::getSetting('showAbsencesInHours') === 1;
		$baseAbsences			= AHPAbsenceFunctions::getBaseAbsencesByStateType('a272f564576d443e7832587126b070aa');
		$prorata["value"]		= AHPEmployeeFunctions::getEmployeeProRataAbsenceByEcID($ecId, $year, $baseAbsences);
		$prorata["text"]		.= Dict::getModuleValue('ttwa-ahp', 'difference_from_pro-rata') . ": " . $prorata["value"];

		if ($showExtraWords) {
			$word = Dict::getModuleValue('ttwa-base', 'hours');
			if (!$showAbsencesInHours) {
				$word = Dict::getModuleValue('ttwa-base', 'days');
			}
			$prorata["text"]	.= " " . $word;
		}

		return $prorata;
	}

	/**
	 * Visszaadja a kereső mezőket
	 *
	 * A keresők sorrendjéhez a Class változóban felvett sorrendet használja
	 *
	 * Backend Know-How:
	 * 	Lehetőség van mobilon itt a multi_select 1 alkalmazására, így több csoportot is ki lehet választani a kereső modalban
	 * 	Lehetőség van a mobile.nav view-nak átadni az $initOpenSearch változóban true-t ez esetben alapértelmezetten megnyitja és felépíti a kereső modalt
	 * 	Keresőkből a Class tetején található változóban megadott opciók vehetők fel a search_filter táblába
	 * 	A search_filter táblába való rögzítés után 2 függvényre van szükség a kontrollerben (példa: AbsenceplannerController ):
	 * 		actionMobileSearch() -> ez hívja meg a MobileAPI::buildSearchFields($controllerId, $processId) függvényt amely felépíti a kereső modal tartalmat
	 * 		actionChangeDropdowns() -> ez hívja meg a MobileAPI::reloadDependencies($field, $value, $type, $dateInputs, $groupInputs, $processId, $controller) függvényt amely változás esetén leköveti az alárendelt szűrőket
	 * Frontend Know-How:
	 * 	Kereső gomb és searchModal létrehozása (példa: absenceplanner view)
	 * @param string $controllerId
	 * @return array
	 */
	public static function getSearchFilters($controllerId)
	{
		$ret				= [];
		$keys				= array_keys(self::$matchingSearchFilterIds);
		$searchFiltersSQL	= "
			SELECT
				sf.`filter_type`,
				sf.`filter_id`,
				sf.`multi_select`
			FROM (
				SELECT '1' AS id, -1 AS order_nr
		";
		foreach ($keys as $key => $id) {
			$searchFiltersSQL .= "
				UNION SELECT '{$id}' AS id, {$key} AS order_nr
			";
		}
		$searchFiltersSQL .= "
			) AS order_helper
			LEFT JOIN `search_filter` sf ON sf.`filter_id` = order_helper.id
			WHERE
					sf.`status` = " . Status::PUBLISHED . "
				AND sf.`controller_id` = '{$controllerId}'
			ORDER BY order_helper.order_nr
		";
		$res = dbFetchAll($searchFiltersSQL);
		foreach ($res as $r) { $ret[$r["filter_type"]][] = ["filter_id" => $r["filter_id"], "multi" => $r["multi_select"]]; }

		return $ret;
	}

	/**
	 * Visszaadja a Frontendnek a keresőket
	 * @param string $controllerId
	 * @param string $processId
	 * @return void
	 */
	public static function buildSearchFields($controllerId, $processId)
	{
		$ret = ["status" => "0", "html" => "", "employees" => []];

		// Alapok inicializálása
		self::$end		= App::getSetting("defaultEnd");
		$defaults		=
		[
			"valid_from"		=> ["default" => date("Y-m") . "-01",	"whereSQL" => " AND '" . date("Y-m") . "-01' <= IFNULL(tableName.`valid_to`, '" . self::$end . "')"],
			"valid_to"			=> ["default" => date("Y-m-t"),			"whereSQL" => " AND tableName.`valid_from` <= '" . date("Y-m-t") . "'"],
			"valid_date"		=> ["default" => date("Y-m-d"),			"whereSQL" => " AND CURDATE() BETWEEN tableName.`valid_from` AND IFNULL(tableName.`valid_to`, '" . self::$end . "')"],
			"valid_month"		=> ["default" => date("Y-m"),			"whereSQL" => " AND tableName.`valid_from` <= '" . date("Y-m-t") . "' AND '" . date("Y-m") . "-01' <= IFNULL(tableName.`valid_to`, '" . self::$end . "')"],
			"valid_year"		=> ["default" => date("Y"),				"whereSQL" => " AND tableName.`valid_from` <= '" . date("Y") . "-12-31' AND '" . date("Y") . "-01-01' <= IFNULL(tableName.`valid_to`, '" . self::$end . "')"],
			"employee_contract" => ["default" => ""]
		];
		$dateWhereSQL	= "";

		// Keresők lekérése
		$searchFilters	= self::getSearchFilters($controllerId);

		// Ha nincs dátum szűrő akkor jelenlegi dátum
		if (!array_key_exists("date", $searchFilters)) {
			$dateWhereSQL				.= $defaults["valid_date"]["whereSQL"];
			$defContract["valid_date"]	= $defaults["valid_date"]["default"];
		}
		foreach ($searchFilters as $type => $ids)
		{
			// Dátum
			if ($type === "date")
			{
				foreach (self::$matchingSearchFilterIds[$ids[0]["filter_id"]] as $searchFields)
				{
					// HTML felépítése
					$ret["html"] .= '
						<div class="mb-2">
							<label for="' . $searchFields . '" class="form-label">' . Dict::getValue($searchFields) . '</label>
							<input type="text" id="' . $searchFields . '" name="' . $searchFields . '" class="form-control date-picker serialize" value="' . $defaults[$searchFields]["default"] . '">
						</div>
					';
					// Dátum szűrés csoportokhoz
					$dateWhereSQL				.= $defaults[$searchFields]["whereSQL"];
					$defContract[$searchFields]	= $defaults[$searchFields]["default"];
				}
			} else {
			// Csoport
				foreach ($ids as $id)
				{
					if (array_key_exists($id["filter_id"], self::$matchingSearchFilterIds))
					{
						// Kell-e approver vizsgálat
						if (isset(self::$matchingSearchFilterIds[$id["filter_id"]][0])) { $checkApprover = false; } else { $checkApprover = true; }
						foreach (self::$matchingSearchFilterIds[$id["filter_id"]] as $key => $value)
						{
							if ($checkApprover) { $table = $key; } else { $table = $value; }
							if (strpos($table, 'company_org') !== false) { $tableId = mb_substr($table, 0, -1); } else { $tableId = $table; }

							// SQL felépítés
							if ($table != "employee_contract")
							{
								$SQL	= "";
								$multi	= " multiple";
								if ($id["multi"] != "1") {
									$multi = "";
								}
								$SQL .= "
									SELECT 'ALL' AS id, '" . Dict::getValue("all") . "' AS value, '1' AS def UNION
									SELECT
										`{$table}`.`{$tableId}_id` AS id,
										`{$table}`.`{$tableId}_name` AS value,
										'0' AS def
									FROM `{$table}`
									WHERE
											`{$table}`.`status` = " . self::$pub . "
										" . str_replace("tableName", $table, $dateWhereSQL) . "
								";
								// GARG
								if ($checkApprover) {
									$art		= new ApproverRelatedGroup;
									$gargSQL	= $art->getApproverReleatedGroupSQL($value, [$processId], Yang::getUserID(), "CURDATE()", "AND", "CurrentDate", $controllerId);
									$SQL		.= $gargSQL["where"];
								}
								$SQL .= "
									GROUP BY id
									ORDER BY value
								";
								$results = dbFetchAll($SQL);

								// HTML felépítése
								if ($table === "payroll") { $dict = Dict::getValue(ucfirst($table)); } else { $dict = Dict::getValue($table); }
								$ret["html"] .= '
									<div class="mb-2">
										<label for="' . $table . '" class="form-label">' . $dict . '</label>
										<select name="' . $table . '" id="' . $table . '" class="form-select serialize"' . $multi . '>
								';
								foreach ($results as $res)
								{
									$selected = ((int)$res["def"]) ? " selected" : "";
									$ret["html"] .= '
											<option value="' . $res["id"] . '"' . $selected . '>' . $res["value"] . '</option>
									';
								}
								$ret["html"] .= '
										</select>
									</div>
								';
							} else {
								$gae				= new GetActiveEmployees(["interval" => $defContract], $processId, $controllerId, userID(), false, false, true);
								$activeEmployees	= $gae->getEmployees();
								$ret["html"]		.= '
									<div class="mb-2">
										<label for="' . $table . '" class="form-label">' . Dict::getValue("employee") . '</label>
										<div class="form-group">
											<input type="hidden" id="' . $table . '" name="' . $table . '" class="form-control serialize">
											<input type="text" id="' . $table . '_visible" name="' . $table . '_visible" class="form-control">
											<button onclick="clearInputField(\'employee_contract\')" type="button" class="form-input-button input-clear">
												<svg viewBox="0 0 16 16"><use xlink:href="#icon-close-circle"></use></svg>
											</button>
										</div>
									</div>
								';
								foreach ($activeEmployees as $res) {
									$ret["employees"][] = ["label" => $res["value"], "value" => $res["employee_contract_id"]];
								}
							}
						}
					}
				}
			}
		}

		if ($ret["html"] != "") { $ret["status"] = "success"; }

		echo json_encode($ret);
	}


	/**
	 * Visszaadja az új paraméterek alapján (változtatott mezők) a többi mező lehetséges értékeit
	 * @param string $field
	 * @param string $value
	 * @param string $type
	 * @param array $dateInputs
	 * @param array $groupInputs
	 * @param string $processId
	 * @param string $controllerId
	 * @return void
	 */
	public static function reloadDependencies($field, $value, $type, $dateInputs, $groupInputs, $processId, $controllerId)
	{
		$ret			= ["status" => "success"];
		// Alapok inicializálása
		self::$end		= App::getSetting("defaultEnd");
		$dateWhereSQL	= "";
		$vFrom			= (!isset($dateInputs["valid_from"]))	? date("Y-m") . "-01"	: $dateInputs["valid_from"];
		$vTo			= (!isset($dateInputs["valid_to"]))		? date("Y-m-t")			: $dateInputs["valid_to"];
		$vDate			= (!isset($dateInputs["valid_date"]))	? date("Y-m-d")			: $dateInputs["valid_date"];
		$vMonT			= (!isset($dateInputs["valid_month"]))	? date("Y-m-t")			: date("Y-m-t", strtotime($dateInputs["valid_month"] . "-01"));
		$vMonF			= (!isset($dateInputs["valid_month"]))	? date("Y-m-01")		: $dateInputs["valid_month"] . "-01";
		$vYearT			= (!isset($dateInputs["valid_year"]))	? date("Y") . "-12-31"	: $dateInputs["valid_year"] . "-12-31";
		$vYearF			= (!isset($dateInputs["valid_year"]))	? date("Y") . "-01-01"	: $dateInputs["valid_year"] . "-01-01";
		$defaults		=
		[
			"valid_from"		=> " AND '" . $vFrom . "' <= IFNULL(tableName.`valid_to`, '" . self::$end . "')",
			"valid_to"			=> " AND tableName.`valid_from` <= '" . $vTo . "'",
			"valid_date"		=> " AND '" . $vDate . "' BETWEEN tableName.`valid_from` AND IFNULL(tableName.`valid_to`, '" . self::$end . "')",
			"valid_month"		=> " AND tableName.`valid_from` <= '" . $vMonT . "' AND '" . $vMonF . "' <= IFNULL(tableName.`valid_to`, '" . self::$end . "')",
			"valid_year"		=> " AND tableName.`valid_from` <= '" . $vYearT . "' AND '" . $vYearF . "' <= IFNULL(tableName.`valid_to`, '" . self::$end . "')"
		];
		foreach (array_keys($dateInputs) as $dates) {
			$dateWhereSQL .= $defaults[$dates];
		}
		if ($dateWhereSQL === "") { $dateWhereSQL .= $defaults["valid_date"]; }

		if ($type === "date")
		{
			foreach (self::$searchDependencies["DATE_DEPENDENCIES"] as $dep)
			{
				if (isset($dep[0])) { $checkApprover = false; $table = $dep[0]; } else { $checkApprover = true; $table = array_keys($dep)[0]; }
				if (strpos($table, 'company_org') !== false) { $tableId = mb_substr($table, 0, -1); } else { $tableId = $table; }

				// SQL felépítés
				if ($table != "employee_contract")
				{
					$SQL = "
						SELECT 'ALL' AS id, '" . Dict::getValue("all") . "' AS value UNION
						SELECT
							`{$table}`.`{$tableId}_id` AS id,
							`{$table}`.`{$tableId}_name` AS value
						FROM `{$table}`
						WHERE
								`{$table}`.`status` = " . self::$pub . "
							" . str_replace("tableName", $table, $dateWhereSQL) . "
					";
					// GARG
					if ($checkApprover) {
						$art		= new ApproverRelatedGroup;
						$gargSQL	= $art->getApproverReleatedGroupSQL($dep[array_keys($dep)[0]], [$processId], Yang::getUserID(), "CURDATE()", "AND", "CurrentDate", $controllerId);
						$SQL		.= $gargSQL["where"];
					}
					$SQL .= "
						GROUP BY id
						ORDER BY value
					";
					$results = dbFetchAll($SQL);

					foreach ($results as $res) {
						$ret[$table][] = ["id" => $res["id"], "label" => $res["value"]];
					}
				} else {
					$ret[$table][]		= ["label" => "", "value" => ""];
					$gae				= new GetActiveEmployees(["interval" => $dateInputs], $processId, $controllerId, userID(), false, false, true);
					$activeEmployees	= $gae->getEmployees();
					foreach ($activeEmployees as $res) {
						$ret[$table][] = ["label" => $res["value"], "value" => $res["employee_contract_id"]];
					}
				}
			}
		} else if ($type === "group") {
			if (empty($dateInputs)) { $dateInputs["valid_date"] = date("Y-m-d"); }
			foreach (self::$searchDependencies["GROUP_DEPENDENCIES"][$field] as $dep)
			{
				if (isset($dep[0])) { $checkApprover = false; $table = $dep[0]; } else { $checkApprover = true; $table = array_keys($dep)[0]; }
				if (strpos($table, 'company_org') !== false) { $tableId = mb_substr($table, 0, -1); } else { $tableId = $table; }

				// SQL felépítés
				if ($table != "employee_contract")
				{
					$SQL = "
						SELECT 'ALL' AS id, '" . Dict::getValue("all") . "' AS value UNION
						SELECT
							`{$table}`.`{$tableId}_id` AS id,
							`{$table}`.`{$tableId}_name` AS value
						FROM `{$table}`
						WHERE
								`{$table}`.`status` = " . self::$pub . "
							" . str_replace("tableName", $table, $dateWhereSQL) . "
					";
					// Dependency
					$SQL .= "
							AND (`{$table}`.`{$field}_id` IN ('ALL', '" . implode("', '", (array)$value) . "')
					";
					$SQL .= (in_array("ALL", (array)$value)) ? " OR 1=1 )" : " )";
					// GARG
					if ($checkApprover) {
						$art		= new ApproverRelatedGroup;
						$gargSQL	= $art->getApproverReleatedGroupSQL($dep[array_keys($dep)[0]], [$processId], Yang::getUserID(), "CURDATE()", "AND", "CurrentDate", $controllerId);
						$SQL		.= $gargSQL["where"];
					}
					$SQL .= "
						GROUP BY id
						ORDER BY value
					";
					$results = dbFetchAll($SQL);

					foreach ($results as $res) {
						$ret[$table][] = ["id" => $res["id"], "label" => $res["value"]];
					}
				} else {
					$ret[$table][]		= ["label" => "", "value" => ""];
					$gae				= new GetActiveEmployees(["interval" => $dateInputs], $processId, $controllerId, userID(), false, false, true);
					$activeEmployees	= $gae->getEmployees([], null, $groupInputs);
					foreach ($activeEmployees as $res) {
						$ret[$table][] = ["label" => $res["value"], "value" => $res["employee_contract_id"]];
					}
				}
			}
		}

		echo json_encode($ret);
	}
}
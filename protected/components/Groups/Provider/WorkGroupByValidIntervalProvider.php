<?php

namespace Components\Groups\Provider;

class WorkGroupByValidIntervalProvider
{
    public function __invoke(string $validFrom, string $validTo)
    {
        $endDate = \App::getSetting('defaultEnd');
        $criterias = new \CDbCriteria();
        $criterias->select = 't.workgroup_id, t.workgroup_name, t.work_order';
        $criterias->alias = 't';
        $criterias->condition =
            't.status = :published
						AND t.valid_from <= :validTo
						AND IFNULL(t.valid_to, :appEndDate) >= :validFrom';
        $criterias->params = [
            ':validFrom' => $validFrom,
            ':validTo' => $validTo,
            ':appEndDate' => $endDate,
            ':published' => \Status::PUBLISHED
        ];

        $result = \Workgroup::model()->findAll($criterias);
        if (!\is_array($result)) {
            $result = [];
        }

        $resultArray = [];
        foreach ($result as $object) {
            $resultArray[$object->workgroup_id] = [
                'workgroup_name' => $object->workgroup_name,
                'work_order' => $object->work_order
            ];
        }
        return $resultArray;
    }
}
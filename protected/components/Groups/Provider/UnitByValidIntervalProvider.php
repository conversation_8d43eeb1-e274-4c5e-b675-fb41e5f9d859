<?php

namespace Components\Groups\Provider;

class UnitByValidIntervalProvider
{
    public function __invoke(string $validFrom, string $validTo)
    {
        $endDate = \App::getSetting('defaultEnd');
        $criterias = new \CDbCriteria();
        $criterias->select = 't.unit_id, t.unit_name';
        $criterias->alias = 't';
        $criterias->condition =
            't.status = :published
						AND t.valid_from <= :validTo
						AND IFNULL(t.valid_to, :appEndDate) >= :validFrom';
        $criterias->params = [
            ':validFrom' => $validFrom,
            ':validTo' => $validTo,
            ':appEndDate' => $endDate,
            ':published' => \Status::PUBLISHED
        ];

        $result = \Unit::model()->findAll($criterias);
        if (!\is_array($result)) {
            $result = [];
        }

        $resultArray = [];
        foreach ($result as $object) {
            $resultArray[$object->unit_id] = $object->unit_name;
        }
        return $resultArray;
    }
}
<?php

declare(strict_types=1);

namespace Components\WTTCore\Handler;

use Components\Employee\Descriptor\EmployeeContractsAndIntervalDescriptor;
use Components\HTTPCore\Builder\JsonResponseBuilder;
use Psr\Http\Message\ResponseInterface;

final class WorkScheduleDayTypeNameResponseHandler
{
    private WorkScheduleDayTypeNameArrayHandler $arrayHandler;

    public function __construct()
    {
        $this->arrayHandler = new WorkScheduleDayTypeNameArrayHandler();
    }

    /**
     * @throws \Exception
     */
    public function handle(EmployeeContractsAndIntervalDescriptor $ecIdAndIntervalDescriptor): ResponseInterface
    {
        $data = $this->arrayHandler->handle($ecIdAndIntervalDescriptor);

        return (new JsonResponseBuilder())
            ->setData($data)
            ->setStatus(200)
            ->build();
    }
}
<?php

declare(strict_types=1);

namespace Components\WTTCore\Handler;

use Components\WorkScheduleCore\Builder\WorkScheduleDescriptorsBuilder;
use Components\Employee\Descriptor\EmployeeContractsAndIntervalDescriptor;
use Components\WorkScheduleCore\Descriptor\WorkScheduleUsedByEcIdDayDescriptor;
use Components\WorkScheduleCore\Transformer\StringTransformer;
use Components\WTTCore\Builder\WorkScheduleDayTypeNameArrayBuilder;

final class WorkScheduleDayTypeNameArrayHandler
{
    private StringTransformer $stringTransformer;

    public function __construct()
    {
        $this->stringTransformer = new StringTransformer();
    }

    /**
     * @throws \Exception
     */
    public function handle(EmployeeContractsAndIntervalDescriptor $ecIdAndIntervalDescriptor): array
    {
        $from = $this->stringTransformer->convertStringToDate($ecIdAndIntervalDescriptor->getFrom());
        $to = $this->stringTransformer->convertStringToDate($ecIdAndIntervalDescriptor->getTo());

        $workScheduleUsedByEcIdDayDescriptor =
            new WorkScheduleUsedByEcIdDayDescriptor(
                (new WorkScheduleDescriptorsBuilder())
                    ->setEcIdsAndIntervalDescriptor($ecIdAndIntervalDescriptor)
                    ->build()
            );

        return (new WorkScheduleDayTypeNameArrayBuilder())
            ->setWorkScheduleUsedByEcIdDayDescriptor($workScheduleUsedByEcIdDayDescriptor)
            ->setEmployeeContractIds($ecIdAndIntervalDescriptor->getEmployeeContracts())
            ->setFrom($from)
            ->setTo($to)
            ->build();
    }
}
<?php

declare(strict_types=1);

namespace Components\WTTCore\Guesser;

use Components\WTTCore\Descriptor\WTTModeDescriptor;
use Components\WTTCore\Enum\WTTModeEnum;
use Components\WTTCore\Interfaces\TimeDataRecordingDateGuesserInterface;


final class TimeDataRecordingDateDayMode<PERSON><PERSON>ser implements TimeDataRecordingDateGuesserInterface
{
    public function guess(WTTModeDescriptor $descriptor): string
    {
        $date = new \DateTime();
        return $date->format('Y-m-d');
    }

    public function isSupported(WTTModeDescriptor $descriptor): bool
    {
        return WTTModeEnum::DAY === $descriptor->getMode();
    }
}
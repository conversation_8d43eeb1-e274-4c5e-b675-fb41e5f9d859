<?php

declare(strict_types=1);

namespace Components\WTTCore\Guesser;

use Components\WTTCore\Descriptor\WTTModeDescriptor;
use Components\WTTCore\Enum\WTTModeEnum;
use Components\WTTCore\Interfaces\TimeDataRecordingDateGuesserInterface;

final class TimeDataRecordingDateNightMode<PERSON><PERSON>ser implements TimeDataRecordingDateGuesserInterface
{
    public function guess(WTTModeDescriptor $descriptor): string
    {
        $actualDate = new \DateTime();
        $day = $actualDate->format('Y-m-d');
        $dayStart = \DateTime::createFromFormat("Y-m-d His", $day . "000000");
        $dayStart->add($descriptor->getDayStartHour());
        if ($actualDate < $dayStart) {
            $actualDate->modify('-1 day');
        }
        return $actualDate->format('Y-m-d');
    }

    public function isSupported(WTTModeDescriptor $descriptor): bool
    {
        return WTTModeEnum::NIGHT === $descriptor->getMode();
    }
}
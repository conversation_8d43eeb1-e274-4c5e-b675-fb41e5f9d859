<?php

declare(strict_types=1);

namespace Components\WTTCore\Guesser;

use Components\WTTCore\Descriptor\WTTModeDescriptor;
use Components\WTTCore\Interfaces\TimeDataRecordingDateGuesserInterface;


final class TimeDataRecordingDateGuesser
{
    /**
     * @var TimeDataRecordingDateGuesserInterface[]
     */
    private array $timeDataRecordingDateGuessers;

    public function __construct()
    {
        $this->timeDataRecordingDateGuessers[] = new TimeDataRecordingDateDayModeGuesser();
        $this->timeDataRecordingDateGuessers[] = new TimeDataRecordingDateNightModeGuesser();
    }

    /**
     * @throws \Exception
     */
    public function guess(WTTModeDescriptor $modeDescriptor): string
    {
        foreach ($this->timeDataRecordingDateGuessers as $timeDataRecordingDateGuesser) {
            if ($timeDataRecordingDateGuesser->isSupported($modeDescriptor)) {
                return $timeDataRecordingDateGuesser->guess($modeDescriptor);
            }
        }
        throw new \Exception('wttMode appSetting invalid: ' . $modeDescriptor->getMode());
    }
}
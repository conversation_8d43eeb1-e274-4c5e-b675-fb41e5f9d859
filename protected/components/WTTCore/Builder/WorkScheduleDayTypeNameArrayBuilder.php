<?php

declare(strict_types=1);

namespace Components\WTTCore\Builder;

use Components\WorkScheduleCore\Descriptor\WorkScheduleUsedByEcIdDayDescriptor;
use Components\WorkScheduleCore\Transformer\StringTransformer;

final class WorkScheduleDayTypeNameArrayBuilder
{
    private StringTransformer $stringTransformer;
    private WorkScheduleUsedByEcIdDayDescriptor $workScheduleUsedByEcIdDayDescriptor;
    /**
     * @var string[]
     */
    private array $employeeContractIds;
    private \DateTime $from;
    private \DateTime $to;

    public function __construct()
    {
        $this->stringTransformer = new StringTransformer();
    }

    /**
     * @throws \Exception
     */
    public function build(): array
    {
        if ($this->isNotSetEmployeeContractsIds()) {
            throw new \Exception("WorkScheduleDayTypeNameArrayBuilder - employeeContractId empty!");
        }
        if ($this->isOneEmployeeAndOneDay()) {
            return $this->getDayTypeNameOneEmployeeOneDay();
        }
        if ($this->isOneEmployeeAndInterval()) {
            return $this->getDayTypeNameOneEmployeeAndInterval();
        }
        if ($this->isMoreEmployeeAndInterval()) {
            return $this->getDayTypeNameMoreEmployeeAndInterval();
        }
        throw new \Exception("WorkScheduleDayTypeNameArrayBuilder - not found build method");
    }

    private function isNotSetEmployeeContractsIds(): bool
    {
        return !isset($this->employeeContractIds) || empty($this->employeeContractIds);
    }

    private function isOneEmployeeAndOneDay(): bool
    {
        return (count($this->employeeContractIds) === 1 && $this->from == $this->to);
    }

    private function getDayTypeNameOneEmployeeOneDay(): array
    {
        $data = [];
        if ($this->workScheduleUsedByEcIdDayDescriptor->hasWorkScheduleByEcIdDay(
            reset($this->employeeContractIds),
            $this->stringTransformer->convertDateToString($this->from)
        )) {
             $data = [$this->workScheduleUsedByEcIdDayDescriptor->getWorkScheduleDescriptorByEcIdDay(
                 reset($this->employeeContractIds),
                 $this->stringTransformer->convertDateToString($this->from)
             )->getDayTypeName()];
         };
        return $data;
    }

    private function isOneEmployeeAndInterval(): bool
    {
        return (count($this->employeeContractIds) === 1 && $this->from < $this->to);
    }

    private function getDayTypeNameOneEmployeeAndInterval(): array
    {
        $ecId = reset($this->employeeContractIds);
        if (!$this->workScheduleUsedByEcIdDayDescriptor->hasWorkScheduleUsedDescriptorsByEcId($ecId)) {
            return [];
        }
        $workScheduleUsedByEcIdDescriptors = $this->workScheduleUsedByEcIdDayDescriptor->getWorkScheduleUsedDescriptorsByEcId(
            $ecId
        );
        $data = [];
        foreach ($workScheduleUsedByEcIdDescriptors as $day => $workScheduleUsedDescriptor) {
            $data[$day] = $workScheduleUsedDescriptor->hasDayType() ?
                $workScheduleUsedDescriptor->getDayTypeName() :
                '';
        }
        return $data;
    }

    private function isMoreEmployeeAndInterval(): bool
    {
        return (count($this->employeeContractIds) > 1 && $this->from < $this->to);
    }

    private function getDayTypeNameMoreEmployeeAndInterval(): array
    {
        $workScheduleUsedByEcIdAndDaysDescriptors =
            $this->workScheduleUsedByEcIdDayDescriptor->getWorkScheduleUsedDescriptors();
        $data = [];
        foreach ($workScheduleUsedByEcIdAndDaysDescriptors as $ecId => $workScheduleUsedByEcIdDescriptors) {
            foreach ($workScheduleUsedByEcIdDescriptors as $day => $workScheduleUsedDescriptor) {
                $data[$ecId][$day] = $workScheduleUsedDescriptor->hasDayType() ?
                    $workScheduleUsedDescriptor->getDayTypeName() :
                    '';
            }
        }
        return $data;
    }

    public function setWorkScheduleUsedByEcIdDayDescriptor(
        WorkScheduleUsedByEcIdDayDescriptor $workScheduleUsedByEcIdDayDescriptor
    ): WorkScheduleDayTypeNameArrayBuilder {
        $this->workScheduleUsedByEcIdDayDescriptor = $workScheduleUsedByEcIdDayDescriptor;
        return $this;
    }

    public function setEmployeeContractIds(array $employeeContractIds): self
    {
        $this->employeeContractIds = $employeeContractIds;
        return $this;
    }

    public function setFrom(\DateTime $from): self
    {
        $this->from = $from;
        return $this;
    }

    public function setTo(\DateTime $to): self
    {
        $this->to = $to;
        return $this;
    }
}
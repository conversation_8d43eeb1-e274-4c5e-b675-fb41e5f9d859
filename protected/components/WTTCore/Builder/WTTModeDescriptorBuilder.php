<?php

declare(strict_types=1);

namespace Components\WTTCore\Builder;

use Components\WorkScheduleCore\Transformer\StringTransformer;
use Components\WTTCore\Descriptor\WTTModeDescriptor;
use Components\WTTCore\Enum\WTTModeAppSettingsEnum;
use Components\WTTCore\Enum\WTTModeEnum;

final class WTTModeDescriptorBuilder
{
    private StringTransformer $stringTransformer;

    public function __construct()
    {
        $this->stringTransformer = new StringTransformer();
    }

    public function build(array $wttModeAppSetting): WTTModeDescriptor
    {
        $mode = (isset($wttModeAppSetting[WTTModeAppSettingsEnum::MODE]) &&
            $wttModeAppSetting[WTTModeAppSettingsEnum::MODE] === WTTModeEnum::NIGHT) ?
            WTTModeEnum::NIGHT :
            WTTModeEnum::DAY;
        $dayStartTime = new \DateInterval('PT0S');
        if (isset($wttModeAppSetting[WTTModeAppSettingsEnum::START_TIME])) {
            $dayStartTime = $this->stringTransformer->convertTimeStringToDateInterval(
                $wttModeAppSetting[WTTModeAppSettingsEnum::START_TIME],
            );
        }
        return new WTTModeDescriptor($mode, $dayStartTime);
    }
}
<?php

declare(strict_types=1);

namespace Components\WTTCore\Descriptor;

final class WTTModeDescriptor
{
    private string $mode;
    private \DateInterval $dayStartHour;

    /**
     * @param string $mode
     * @param \DateInterval $dayStartHour
     */
    public function __construct(string $mode, \DateInterval $dayStartHour)
    {
        $this->mode = $mode;
        $this->dayStartHour = $dayStartHour;
    }

    public function getMode(): string
    {
        return $this->mode;
    }

    public function getDayStartHour(): \DateInterval
    {
        return $this->dayStartHour;
    }
}
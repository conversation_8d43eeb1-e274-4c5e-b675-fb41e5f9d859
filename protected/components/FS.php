<?php

class FS
{
	private $file_id = null; // fájl egyedi azonosítója
    private $headerFooter = 'header';
	private $new_file = true; // létezik-e a fájl db-ben
	private $customFsDir = false;
	private $customFsUrl = false;
	private $fileAttributes = array();
	private $message = "";
	private $MySQLStore = true;
	private $fsDir = "";
	private $fsUrl = "";
	private $fileGroupID = "";

	public function __construct($file_id = null, $headerFooter = null) {
	    $date       = date("Y/m/d");
		$dateFolder = str_replace("/", DIRECTORY_SEPARATOR, $date);
		$this->fsDir = Yang::getBasePath().DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."webroot".DIRECTORY_SEPARATOR."file_storage".DIRECTORY_SEPARATOR.$dateFolder;
		$this->fsUrl = "file_storage"."/".$dateFolder;

		if (empty($file_id)) {
			$file_id = $this->generateFileID();
		} else {
			$this->new_file = false;
		}

		$this->setFileID($file_id);
        $this->setHeaderFooter($headerFooter);
	}

	private function setFileID($file_id) {
		$this->file_id = $file_id;
	}

	public function getFileID() {
		return $this->file_id;
	}

    public function getHeaderFooter() {
        return $this->headerFooter;
    }

    private function setHeaderFooter($headerFooter) {
        $this->headerFooter = $headerFooter;
    }

	public function getSmallFileID() {
		return substr($this->file_id, 0, 6);
	}

	public function setFileGroupID($fileGroupID) {
		$this->fileGroupID = $fileGroupID;
	}

	public function disableMySQLStore() {
		$this->MySQLStore = false;
	}

	private function isMySQLStore() {
		return $this->MySQLStore;
	}

	public function getFileAttributes() {
		return $this->fileAttributes;
	}

	public function getFsDir()
	{
		return $this->fsDir;
	}

	public function setCustomFsDir($fsDir)
	{
		$this->customFsDir = true;
		$this->fsDir = $fsDir;
	}

	public function hasCustomFsDir()
	{
		return $this->customFsDir;
	}

	public function getFsUrl()
	{
		return $this->fsUrl;
	}

	public function setCustomFsUrl($fsUrl)
	{
		$this->customFsUrl = true;
		$this->fsUrl = $fsUrl;
	}

	public function hasCustomFsUrl()
	{
		return $this->customFsUrl;
	}

	private function generateFileAttributes($file, $mimeType = null, $contentMode = false) {
		$this->fileAttributes["file_id"] = $this->getFileID();

		$this->checkMimeFunctionExists();

		if (!$contentMode) {
			$this->fileAttributes["file_name"] = basename($file);
			$this->fileAttributes["file_size"] = filesize($file); //bytes
			$this->fileAttributes["file_type"] = mime_content_type($file);
			if ($this->isImage($this->fileAttributes["file_type"])) {
				$imageSize = getimagesize($file);
				$this->fileAttributes["image_dimensions_w"] = $imageSize[0];
				$this->fileAttributes["image_dimensions_h"] = $imageSize[1];
			}
			$this->fileAttributes["file_md5"] = md5_file($file);
		} else {
			$this->fileAttributes["file_name"] = $file;
		}

		if (!empty($mimeType)) {
			$this->fileAttributes["file_type"] = $mimeType;
		}

		if (!empty($this->fileGroupID)) {
			$this->fileAttributes["file_group_id"] = $this->fileGroupID;
		}

		$this->fileAttributes["file_upload_date"] = date("Y-m-d H:i:s");
	}

	public function uploadTextFileFromContent($fileName, $content, $mimeType = null) {
		$fs = new FileStorage;

		$this->generateFileAttributes($fileName, $mimeType, true);

		$fs->attributes = $this->fileAttributes;
		if ($this->isMySQLStore()) {
			$fs->file_text = $content;
		} else {
			if (!file_exists($this->fsDir)) {
				mkdir($this->fsDir, 0775, true);
			}

			$fileName = $this->getSmallFileID() . "_" .$fileName;

			$file = fopen($this->fsDir . DIRECTORY_SEPARATOR . $fileName, "w") or die("Unable to open file!");
			fwrite($file, $content);
			fclose($file);

			$fs->file_url = $this->fsUrl . "/" . $fileName;
		}

		$valid = $fs->validate();
		if (!$valid) {
			$error = MyActiveForm::_validate($fs);
			$arr = (array) json_decode($error);

			$msg = "";

			foreach ($arr as $value)
			{
				foreach ($value as $val)
				{
					$msg .= $val."<br/>";
				}
			}

			$this->message = json_encode(array(
				'status' => "failed",
				'msg' => $msg
			));
		} else {
			$fs->save();

			$this->message = json_encode(array(
				'status' => "success",
				'file_id' => $this->fileAttributes["file_id"]
			));
		}
	}

    /**
     * Upload header or footer for PDF (wkhtmltopdf)
     *
     * @param $fileName
     * @param $content
     * @param $mimeType
     * @return void
     */
    public function uploadHeaderFooterTextFileFromContent($fileName, $content, $mimeType = null) {

        $this->generateFileAttributes($fileName, $mimeType, true);

        if (!$this->isMySQLStore()) {
            if (!file_exists($this->fsDir)) {
                mkdir($this->fsDir, 0775, true);
            }

            $fileName = $this->getSmallFileID() . "_" .$fileName;

            $file = fopen($this->fsDir . DIRECTORY_SEPARATOR . $fileName, "w") or die("Unable to open file!");
            fwrite($file, $content);
            fclose($file);
        }

        $this->message = json_encode(array('status' => "success",'file_id' => $this->fileAttributes["file_id"]));
    }

	public function uploadFileFromContent($fileName, $content, $mimeType = null) {
		$fs = new FileStorage;

		$this->generateFileAttributes($fileName, $mimeType, true);

		$fs->attributes = $this->fileAttributes;
		$fs->file_blob = $content;

		$valid = $fs->validate();
		if (!$valid) {
			$error = MyActiveForm::_validate($fs);
			$arr = (array) json_decode($error);

			$msg = "";

			foreach ($arr as $value)
			{
				foreach ($value as $val)
				{
					$msg .= $val."<br/>";
				}
			}

			$this->message = json_encode(array(
				'status' => "failed",
				'msg' => $msg
			));
		} else {
			$fs->save();

			$this->message = json_encode(array(
				'status' => "success",
				'file_id' => $this->fileAttributes["file_id"]
			));
		}
	}

	public function uploadFile($file, $empolyee_row_id = null) {
		$fileExists = false;
		$fileReadable = false;

		$msg = "success";

		if (file_exists($file)) {
			$fileExists = true;

			if (is_readable($file)) {
				$fileReadable = true;
			}
		}

		$fs = new FileStorage;

		if ($fileExists && $fileReadable)
		{
			$customer = Yang::getParam('customerDbPatchName');

			$path = $this->fsDir;
			if (!$this->hasCustomFsDir()) {
				$path = 'file_storage' . DIRECTORY_SEPARATOR . $customer . '_employee' . DIRECTORY_SEPARATOR;
			}
			
			$this->generateFileAttributes($file);

			$fs->attributes = $this->fileAttributes;

			if (!$this->hasCustomFsUrl()) {
				$fs->file_url = $path . $fs->file_name;
			} else {
				$fs->file_url = $this->fsUrl . DIRECTORY_SEPARATOR . $fs->file_name;
			}
			
		}

		if ($this->new_file) {
			if (!$fileExists) {
				$fs->addGenericError("file_not_exists");
			} else {
				if (!$fileReadable) {
					$fs->addGenericError("file_not_readable");
				}
			}

			$valid = $fs->validate();
			if (!$valid) {
				$error = MyActiveForm::_validate($fs);
				$arr = (array) json_decode($error);

				$msg = "";

				foreach ($arr as $value)
				{
					foreach ($value as $val)
					{
						$msg .= $val."<br/>";
					}
				}

				$this->message = json_encode(array(
					'status' => "failed",
					'msg' => $msg
				));
			} else {
				$fs->save();
                $insert_id = dbLastInsertID();

                // Attach image to Employee if $empolyee_row_id has set
                if($empolyee_row_id) {
                    $attached = Employee::attachImageToEmployee($empolyee_row_id, $fs->file_id);
                }
                else {
                    $attached = 1;
                }

				$this->message = json_encode(array(
					'status'    => "success",
					'file_id'   => $this->fileAttributes["file_id"],
					'insert_id' => $insert_id,
					'attached'  => $attached,
				));
			}
		}
	}

	public function deleteFile() {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->select = "`file_id`, `file_url`";
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$res_fs = $fs->find($crit);
		$fs->deleteAll($crit);

		if ($res_fs) {
			if (!empty($res_fs->file_url)) {
				$file = Yang::getBasePath().DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."webroot".DIRECTORY_SEPARATOR.$res_fs->file_url;

				if (file_exists($file)) {
					unlink($file);
				}
			}

			$this->message = json_encode(array(
				'status' => "success"
			));
		} else {
			$this->message = json_encode(array(
				'status' => "failed",
				'msg' => "file_not_exists"
			));
		}
	}

	public function deleteFilesByUserAndFileGroupID() {
		if (empty($this->fileGroupID)) return false;

		$user_id = userID();
		if (Yang::isGuest() || empty($user_id)) return false;

		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->select = "`file_id`, `file_url`";
		$crit->condition = "`file_group_id` = '".$this->fileGroupID."' AND `created_by` = '".$user_id."'";
		$res_fs_files = $fs->findAll($crit);
		$fs->deleteAll($crit);

		if ($res_fs_files) {
			foreach ($res_fs_files as $res_fs) {
				if (!empty($res_fs->file_url)) {
					$file = Yang::getBasePath().DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."webroot".DIRECTORY_SEPARATOR.$res_fs->file_url;

					if (file_exists($file)) {
						unlink($file);
					}
				}
			}

			$this->message = json_encode(array(
				'status' => "success"
			));
		} else {
			$this->message = json_encode(array(
				'status' => "failed",
				'msg' => "file_not_exists"
			));
		}
	}

	public function showImageFile($style = null, $echo = true, $css = false, $cssUrl = false) {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$fs_res = $fs->find($crit);

		$ret = '';

		if ($fs_res) {
			if ($this->isImage($fs_res->file_type)) {
				if (!is_null($fs_res->file_blob))
				{
					// width:'.$fs_res->image_dimensions_w.'px;height:'.$fs_res->image_dimensions_h.'px;
					if ($css) {
						$ret .= 'background-image: url(data:'.$fs_res->file_type.';base64,'.base64_encode( $fs_res->file_blob ).');';
					} else if ($cssUrl) {
						$ret .= 'url(data:'.$fs_res->file_type.';base64,'.base64_encode( $fs_res->file_blob ).')';
					} else { // alt="'.$fs_res->file_name.'" title="'.$fs_res->file_name.'"
						$ret .= '<img style="'.$style.'" src="data:'.$fs_res->file_type.';base64,'.base64_encode( $fs_res->file_blob ).'" />';
					}
				} else {
					if ($css) {
						$ret .= 'background-image: url(/' . $fs_res->file_url . ');';
					} else if ($cssUrl) {
						$ret .= 'url(/' .  $fs_res->file_url . ');';
					} else { // alt="'.$fs_res->file_name.'" title="'.$fs_res->file_name.'"
						$ret .= '<img style="'.$style.'" src="/' . $fs_res->file_url . '" />';
					}
				}

			} else {
			    // TODO Undefined variable $short_type used
				$short_type = substr($fs_res->file_type, 6, strlen($fs_res->file_type));
				$ret 	   .= 'File type not known as image ('.$short_type.')!';
			}
		} else {
			//$ret .= 'File not exists!';
		}

		if ($echo) {
			echo $ret;
		} else {
			return $ret;
		}
	}

	public function downloadFile($content = "attachment" /*inline*/) {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$fs_res = $fs->find($crit);

		if ($fs_res) {
			if (!empty($fs_res->file_text)) {
				header('Content-type: '.$fs_res->file_type);
				header('Content-disposition: '.$content.'; filename="'.$fs_res->file_name.'"');
				echo $fs_res->file_text;
			} else if (!empty($fs_res->file_url)) {
				if ($fs_res->file_type === "xlsx") {
					$fileType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
				} else {
					$fileType = $fs_res->file_type;
				}
				header('Content-type: '.$fileType);
				header('Content-disposition: '.$content.'; filename="'.$fs_res->file_name.'"');
				$file = Yang::getBasePath().DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."webroot".DIRECTORY_SEPARATOR.$fs_res->file_url;
				readfile($file);
			} else {
				header('Content-type: '.$fs_res->file_type);
				header('Content-disposition: '.$content.'; filename="'.$fs_res->file_name.'"');
				echo $fs_res->file_blob;
			}
		}
	}

	public function showTextFile() {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$fs_res = $fs->find($crit);

		if ($fs_res) {
			if (!empty($fs_res->file_text)) {
				header('Content-type: '.$fs_res->file_type);
				header('Content-disposition: inline; filename="'.$fs_res->file_name.'"');
				echo $fs_res->file_text;
			} else if (!empty($fs_res->file_url)) {
				header('Content-type: '.$fs_res->file_type);
				header('Content-disposition: inline; filename="'.$fs_res->file_name.'"');
				$file = Yang::getBasePath().DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."webroot".DIRECTORY_SEPARATOR.$fs_res->file_url;
				readfile($file);
			} else {
				header('Content-type: '.$fs_res->file_type);
				header('Content-disposition: inline; filename="'.$fs_res->file_name.'"');
				echo $fs_res->file_blob;
			}
		}
	}

    /**
     * Show header/footer text file for PDF
     *
     * @return void
     */
    public function showHeaderFooterTextFile() {
        $fs              = new FileStorage;
        $crit            = new CDbCriteria();
        $crit->condition = "`file_id` = '".$this->getFileID()."'";
        $fs_res          = $fs->find($crit);
        $file_url        = '';
        $headerFooter    = '_'.$this->headerFooter;

        if ($fs_res) {
            if (!empty($fs_res->file_url)) {
                header('Content-type: '.$fs_res->file_type);
                header('Content-disposition: inline; filename="'.$fs_res->file_name.'"');
                $file_url_array = explode('.',$fs_res->file_url);
                $extension      = end($file_url_array);

                if ($extension === 'html') {
                    array_pop($file_url_array);
                } else {
                    $extension = 'html';
                }
                $last_index = count($file_url_array)-1;
                $file_url_array[$last_index] .= $headerFooter;
                array_push($file_url_array,$extension);
                $file_url = implode('.',$file_url_array);

                $file = Yang::getBasePath().DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."webroot".DIRECTORY_SEPARATOR.$file_url;
                readfile($file);
                //echo $file;
            }
        }
    }

	public function getFileName() {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->select = "`file_name`";
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$fs_res = $fs->find($crit);

		if ($fs_res) {
			return $fs_res->file_name;
		}

		return null;
	}

	public function getTextFileContent() {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->select = "`file_text`";
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$fs_res = $fs->find($crit);

		if ($fs_res) {
			return $fs_res->file_text;
		}

		return null;
	}

	public function getImageFileWidth() {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->select = "`image_dimensions_w`";
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$fs_res = $fs->find($crit);

		if ($fs_res) {
			return $fs_res->image_dimensions_w;
		}

		return null;
	}

	public function getImageFileHeight() {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->select = "`image_dimensions_h`";
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$fs_res = $fs->find($crit);

		if ($fs_res) {
			return $fs_res->image_dimensions_h;
		}

		return null;
	}

	public function getMessage($ajaxResponse = true) {
		echo $this->message;
	}

	private function generateFileID() {
		$found = true;

		while ($found) {
			$genMd5From = date("YmdHis").uniqid();
			$file_id = md5($genMd5From);

			$fs = new FileStorage;
			$crit = new CDbCriteria();
			$crit->select = "`file_id`";
			$crit->condition = "`file_id` = '$file_id'";
			$fs_res = $fs->findAll($crit);

			if (!count($fs_res)) {
				$found = false;
			}
		}

		return $file_id;
	}

	private function checkFileDuplicatesByMD5() {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->select = "`file_id`, `file_md5`";
		$crit->condition = "`file_md5` = '".$this->fileAttributes["file_md5"]."'";
		$fs_res = $fs->find($crit);

		if ($fs_res) {
			$this->new_file = false;
			$this->file_id = $fs_res->file_id;
		}
	}

	public function isImage($file_type) {
		$is_image = substr($file_type, 0, 6) === "image/";
		$short_type = substr($file_type, 6, strlen($file_type));
		$allowed_formats = in_array($short_type, array("jpg","jpeg","png","gif"));
		if ($is_image && $allowed_formats) {
			return true;
		}
		return false;
	}

	public function getFileUrl() {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->select = "`file_url`";
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$fs_res = $fs->find($crit);

		if ($fs_res) {
			return $fs_res->file_url;
		}

		return null;
	}

	public function getFileType() {
		$fs = new FileStorage;
		$crit = new CDbCriteria();
		$crit->select = "`file_type`";
		$crit->condition = "`file_id` = '".$this->getFileID()."'";
		$fs_res = $fs->find($crit);

		if ($fs_res) {
			return $fs_res->file_type;
		}

		return null;
	}

	private function checkMimeFunctionExists(){
		if(!function_exists('mime_content_type')) {

			function mime_content_type($filename) {
				$mime_types = array(
					'txt' => 'text/plain',
					'htm' => 'text/html',
					'html' => 'text/html',
					'php' => 'text/html',
					'css' => 'text/css',
					'js' => 'application/javascript',
					'json' => 'application/json',
					'xml' => 'application/xml',
					'swf' => 'application/x-shockwave-flash',
					'flv' => 'video/x-flv',
					// images
					'png' => 'image/png',
					'jpe' => 'image/jpeg',
					'jpeg' => 'image/jpeg',
					'jpg' => 'image/jpeg',
					'gif' => 'image/gif',
					'bmp' => 'image/bmp',
					'ico' => 'image/vnd.microsoft.icon',
					'tiff' => 'image/tiff',
					'tif' => 'image/tiff',
					'svg' => 'image/svg+xml',
					'svgz' => 'image/svg+xml',
					// archives
					'zip' => 'application/zip',
					'rar' => 'application/x-rar-compressed',
					'exe' => 'application/x-msdownload',
					'msi' => 'application/x-msdownload',
					'cab' => 'application/vnd.ms-cab-compressed',
					// audio/video
					'mp3' => 'audio/mpeg',
					'qt' => 'video/quicktime',
					'mov' => 'video/quicktime',
					// adobe
					'pdf' => 'application/pdf',
					'psd' => 'image/vnd.adobe.photoshop',
					'ai' => 'application/postscript',
					'eps' => 'application/postscript',
					'ps' => 'application/postscript',
					// ms office
					'doc' => 'application/msword',
					'rtf' => 'application/rtf',
					'xls' => 'application/vnd.ms-excel',
					'ppt' => 'application/vnd.ms-powerpoint',
					// open office
					'odt' => 'application/vnd.oasis.opendocument.text',
					'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
				);

				$ext = strtolower(array_pop(explode('.',$filename)));
				if (array_key_exists($ext, $mime_types)) {
					return $mime_types[$ext];
				}
				elseif (function_exists('finfo_open')) {
					$finfo = finfo_open(FILEINFO_MIME);
					$mimetype = finfo_file($finfo, $filename);
					finfo_close($finfo);
					return $mimetype;
				}
				else {
					return 'application/octet-stream';
				}
			}
		}
	}
}

?>
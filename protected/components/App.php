<?php


class App
{
    public static function getRolegroup($user = null)
    {
        $user_id = !empty($user) ? $user : userID();

        $usedSessionValue = $_SESSION['tiptime']['rolegroup'] ?? 'notSet';

        if ($usedSessionValue === 'notSet' || !empty($user)) {
            if (!$user_id) {
                return null;
            }

            $user = new User();

            $criteria = new CDbCriteria();
            $criteria->condition = "`user_id` = '$user_id' AND `status` = " . Status::PUBLISHED . " AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`,'" . App::getSetting(
                    'defaultEnd'
                ) . "')";

            $res_user = $user->find($criteria);

            if ($res_user) {
                $_SESSION['tiptime']['rolegroup'] = $res_user->rolegroup_id;

                return $res_user->rolegroup_id;
            } else {
                $_SESSION['tiptime']['rolegroup'] = null;

                return null;
            }
        } else {
            return $_SESSION['tiptime']['rolegroup'];
        }
    }

    public static function getRolegroupName($userId = null)
    {
        $rolegroup = self::getRolegroup($userId);

        $sql = 'SELECT rolegroup_name FROM auth_rolegroup WHERE rolegroup_id = "' . $rolegroup . '" ';
        $result = dbFetchRow($sql);

        return $result['rolegroup_name'];
    }

    public static function getUsersHasThisRight($controller_id, $operation, $column = null)
    {
        $u = new User();
        $criteria = new CDbCriteria();
        $criteria->alias = 'u';
        $criteria->select = 'u.user_id, u.username, u.email, u.employee_id,' . Employee::getParam(
                'fullname',
                'e'
            ) . ' AS fullname ';
        $criteria->join = '
			LEFT JOIN `auth_role_in_group` arig ON arig.`rolegroup_id` = u.`rolegroup_id`
			LEFT JOIN `auth_acl` aa ON aa.`role_id` = arig.`role_id`
			LEFT JOIN `employee` e ON e.employee_id = u.employee_id
				AND e.`status` = 2 AND (NOW() BETWEEN e.`valid_from` AND default_end(e.`valid_to`))
		';
        $criteria->condition = "
			aa.`controller_id` = '$controller_id' AND aa.`operation_id` = '$operation'
			AND u.`status` = 2 AND (NOW() BETWEEN u.`valid_from` AND default_end(u.`valid_to`))
		";
        if (empty($column)) {
            $criteria->condition .= ' AND aa.`column_name` IS NULL';
        } else {
            $criteria->condition .= " AND aa.`column_name` = '$column'";
        }

        return $u->findAll($criteria);
    }

    public static function hasRight($controller_id, $operation, $column = null)
    {
        return App::getRight($controller_id, $operation, $column);
    }

    public static function getColumnRight($controller, $column, $rolegroup_id = 'ALL')
    {
        $c = AnyCache::get('columnRights');
        if ($c) {
            mDoing('returning columnRights from cache');
            $modifiedResult = $c;
        } else {
            $queryColumnRights = "
			SELECT
				column_rights.roles,
				column_rights.controller_id,
				column_rights.rolegroup_id,
				column_rights.column_id
			FROM
				`column_rights`
			LEFT JOIN `auth_rolegroup` ON
					`column_rights`.rolegroup_id = `auth_rolegroup`.rolegroup_id
			LEFT JOIN `user` ON
					`auth_rolegroup`.rolegroup_id = `user`.rolegroup_id
				AND CURDATE() BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`,'" . App::getSetting(
                    'defaultEnd'
                ) . "')
				AND	`user`.`status`=" . Status::PUBLISHED . '
			WHERE
			`column_rights`.`status`=' . Status::PUBLISHED . '
			';

            $result = dbFetchAll($queryColumnRights);

            $modifiedResult = [];

            foreach ($result as $k => $v) {
                $modifiedResult[$v['controller_id']][$v['rolegroup_id']][$v['column_id']] = $v['roles'];
            }

            AnyCache::set('columnRights', $modifiedResult, 'column_rights,auth_rolegroup,user');
        }

        if (App::isRootSessionEnabled()) {
            $rolegroup_id = 'ALL';
        }

        $selection = $modifiedResult[$controller][$rolegroup_id][$column];

        return empty($selection) ? 'read' : $selection;
    }

    // controller rights, not column rights!!!
    public static function getRight(
        ?string $controller_id,
        ?string $operation,
        ?string $column = null,
        ?string $user_id = null
    ) {
        $userId = is_null($user_id) ? userID() : $user_id;

        if (useExperimental('use new getRight method')) {
            return Miner::get('auth acl', $controller_id, $operation, $column, $user_id);
        }


        $outByMiner = Miner::get('auth acl', $controller_id, $operation, $column, $user_id);

        $rolegroup_id = App::getRolegroup($user_id);

        $guest = Yang::isGuest();

        $contr_id = empty($controller_id) ? 'ALL' : $controller_id;

        if (empty($column)) {
            $usedSessionValue = $_SESSION['tiptime']['rights'][$contr_id][$operation] ?? 'notSet';
        } else {
            $usedSessionValue = $_SESSION['tiptime']['rights'][$contr_id][$operation]['column'][$column] ?? 'notSet';
        }

        if ($usedSessionValue === 'notSet') {
            if ((!empty($rolegroup_id) || $guest) /*&& !empty($controller_id)*/ && !empty($operation)) {
                $SQL = "
					SELECT
						*
					FROM
						`auth_acl` aa
					LEFT JOIN
						`auth_role_in_group` arig ON
							aa.`role_id` = arig.`role_id`
					LEFT JOIN
						(
							SELECT
								`row_id`,
								`role_id`
							FROM `user_report_rights`
							WHERE `user_id` = '{$userId}' AND `status` = " . Status::PUBLISHED . " 
							    AND CURDATE() BETWEEN `valid_from` AND 
							        IFNULL(`valid_to`, '" . App::getSetting('defaultEnd') . "')
							GROUP BY `role_id`
						) AS urr ON urr.`role_id` = aa.`role_id`
					WHERE
						aa.`controller_id` = '$controller_id'
							AND aa.`operation_id` = '$operation'
				";

                if (!$guest) {
                    $SQL .= " AND (arig.`rolegroup_id` = '{$rolegroup_id}' OR urr.`row_id` IS NOT NULL)";
                }

                if (empty($column)) {
                    $SQL .= ' AND aa.`column_name` IS NULL';
                } else {
                    $SQL .= " AND aa.`column_name` = '$column'";
                }

                $res_auth_acl = dbFetchAll($SQL);

                // nem látogató a felhasználó VAGY látogató, de a művelethez nem szükséges bejelentkezni
                if (count($res_auth_acl) && (!$guest || (!$res_auth_acl[0]['login_need'] && $guest))) {
                    return self::setSessionAccessRight(
                        $column,
                        $res_auth_acl[0]['access_right'],
                        $controller_id,
                        $operation
                    );
                } else {
                    $original_controller_id = $contr_id;
                    $controller_id = 'ALL';

                    $SQL = "
						SELECT
							*
						FROM
							`auth_acl` aa
						LEFT JOIN
							`auth_role_in_group` arig ON
								aa.`role_id` = arig.`role_id`
						LEFT JOIN
							(
								SELECT
									`row_id`,
									`role_id`
								FROM `user_report_rights`
								WHERE `user_id` = '{$userId}' AND `status` = " . Status::PUBLISHED . " 
								    AND CURDATE() BETWEEN `valid_from` AND 
								        IFNULL(`valid_to`, '" . App::getSetting('defaultEnd') . "')
								GROUP BY `role_id`
							) AS urr ON urr.`role_id` = aa.`role_id`
						WHERE
							aa.`controller_id` = '$controller_id'
								AND aa.`operation_id` = '$operation'
					";

                    if (!$guest) {
                        $SQL .= " AND (arig.`rolegroup_id` = '{$rolegroup_id}' OR urr.`row_id` IS NOT NULL)";
                    }

                    if (empty($column)) {
                        $SQL .= ' AND aa.`column_name` IS NULL';
                    } else {
                        $SQL .= " AND aa.`column_name` = '$column'";
                    }

                    $res_auth_acl = dbFetchAll($SQL);

                    // nem látogató a felhasználó VAGY látogató, de a művelethez nem szükséges bejelentkezni
                    if (count($res_auth_acl) && (!$guest || (!$res_auth_acl[0]['login_need'] && $guest))) {
                        return self::setSessionAccessRight(
                            $column,
                            $res_auth_acl[0]['access_right'],
                            $original_controller_id,
                            $operation
                        );
                    } // oszlop van megadva, de nem találtunk jogot hozzá
                    else {
                        if (!empty($column)) {
                            $_SESSION['tiptime']['rights'][$original_controller_id][$operation]['column'][$column] = true;


                            return true;
                        } else {
                            $_SESSION['tiptime']['rights'][$original_controller_id][$operation] = false;


                            return false;
                        }
                    }
                }
            }
        } else {
            return $usedSessionValue;
        }


        return null;
    }

    /**
     * @param $column
     * @param $accessRight
     * @param string|null $controller_id
     * @param $operation
     * @return mixed
     */
    public static function setSessionAccessRight($column, $accessRight, ?string $controller_id, $operation)
    {
        if (empty($column)) {
            $_SESSION['tiptime']['rights'][$controller_id][$operation] = $accessRight;
        } else {
            $_SESSION['tiptime']['rights'][$controller_id][$operation]['column'][$column] =
                $accessRight;
        }

        return $accessRight;
    }

    public function getUsergroup($controller_id)
    {
        $rolegroup_id = App::getRolegroup();
        $userId = userID();

        if (!empty($rolegroup_id) && !empty($controller_id)) {
            $auth_acl = new AuthAcl();

            $criteria = new CDbCriteria();
            $criteria->alias = 'aa';
            $criteria->join = "
				LEFT JOIN `auth_role_in_group` arig ON aa.`role_id` = arig.`role_id`
				LEFT JOIN (
					SELECT
						`row_id`,
						`role_id`
					FROM `user_report_rights`
					WHERE `user_id` = '{$userId}' AND `status` = " . Status::PUBLISHED . " AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '" . App::getSetting(
                    'defaultEnd'
                ) . "')
					GROUP BY `role_id`
				) AS urr ON urr.`role_id` = aa.`role_id`
			";
            //nincs oszlop kiválasztva!
            $criteria->condition = "
				aa.`controller_id` = '$controller_id' AND aa.`operation_id` = 'refine_query'
				 AND (arig.`rolegroup_id` = '{$rolegroup_id}' OR urr.`row_id` IS NOT NULL)
			";

            $res_auth_acl = $auth_acl->findAll($criteria);

            if (count($res_auth_acl) && $res_auth_acl[count($res_auth_acl) - 1]->access_right) {
                return $res_auth_acl[count($res_auth_acl) - 1]->usergroup_id;
            } else {
                return null;
            }
        }

        return null;
    }

    public function getUsersingroup(string $usergroup_id, bool $prefix = false): string
    {
        $prefix = $prefix ? "$prefix." : '';

        if ($usergroup_id === 'user') {
            $user_id = userID();
            return "$prefix`user_id` = '$user_id'";
        } else {
            if ($usergroup_id === 'all_users') {
                return '1';
            } else {
                $user_in_group = new Useringroup();

                $criteria = new CDbCriteria();
                $criteria->condition = "`usergroup_id` = '$usergroup_id'";

                $res_user_in_group = $user_in_group->findAll($criteria);

                $ret = "$prefix`user_id` IN (";
                $i = 0;
                foreach ($res_user_in_group as $res) {
                    $ret .= ($i > 0 ? ',' : '') . "'" . $res->user_id . "'";
                    $i++;
                }
                $ret .= ')';

                return $ret;
            }
        }
    }

    public static function getSetting(string $setting_id)
    {
        if (useExperimental('use new getSetting')) {
            $found = 0;
            $acKey = AnyCache::key('getSetting.all', []);
            if (AnyCache::has($acKey)) {
                $found = 1;
                $settings = AnyCache::get($acKey);
                if (!isset($settings[$setting_id])) {
                    $found = 0;
                }
            }
            if (!$found) {
                $settings = dbFetchAll('select * from app_settings where valid=1');
                AnyCache::set($acKey, $settings, 'app_settings');
            }

            return valueOrNull($settings[$setting_id] ?? null);
        }

        #improveThis // megmértük, nem rossz ez így se, csak sokkal jobb lenne egyszerre behozni az összeset

        if (!isset($_SESSION['tiptime']['settings'][$setting_id])) {
            $app_settings = new AppSettings();

            $criteria = new CDbCriteria();
            $criteria->condition = "`setting_id` = '$setting_id' AND `valid` = 1";

            $res_app_settings = $app_settings->find($criteria);

            if ($res_app_settings) {
                $setting_value = ($res_app_settings->setting_type === 'array') ?
                    unserialize($res_app_settings->setting_value) :
                    $res_app_settings->setting_value;
            } else {
                $setting_value = null;
            }
            $_SESSION['tiptime']['settings'][$setting_id] = $setting_value;
        }

        return $_SESSION['tiptime']['settings'][$setting_id];
    }

    public static function getAllSettings(): array
    {
        $app_settings = new AppSettings();

        $criteria = new CDbCriteria();
        $criteria->condition = '`valid` = 1';

        $res_app_settings = $app_settings->findAll($criteria);

        $settings = [];

        foreach ($res_app_settings as $res) {
            $settings[$res->setting_id] = ['value' => $res->setting_value, 'type' => $res->setting_type];
        }

        return $settings;
    }

    public static function getAllSettingsJs(): string
    {
        $js = 'function getAppSettings() {' . "\n";
        $js .= 'var appSettings = [];' . "\n";

        $settings = App::getAllSettings();

        foreach ($settings as $id => $setting) {
            if ($setting['type'] === 'array') {
                continue;
            }

            $val = str_replace("'", "\\'", $setting['value']);
            $js .= 'appSettings[\'' . $id . '\'] = \'' . $val . '\';' . "\n";
        }

        $js .= 'return appSettings;' . "\n";
        $js .= '}' . "\n";

        return $js;
    }

    /**
     * getLookup(): get lookup values like
     *    $results_arr:
     *        $one_dim_array = false (default):
     *            array( 0 => array('id' => 'id0', 'value' => 'value0'), 1 => array('id' => 'id1', 'value' => 'value1'), ... )
     *        $one_dim_array = true: for Lookup lists
     *            array( 'id0' => 'value0', 'id1' => 'value1', ... )
     */
    public static function getLookup(
        $lookup_id,
        $one_dim_array = false,
        $default_value = null,
        $filter = [],
        $order = false,
        $module = null
    ): array {
        $lang = Dict::getLang();

        if ($module == null) {
            $module = Yang::getParam('module');
        }

        // Get data from current module...
        $app_lookup = new AppLookup();
        $criteria = new CDbCriteria();
        $criteria->alias = 'a';
        $criteria->select = 'a.`lookup_value` AS lookup_value, d.`dict_value` AS dict_value, a.`lookup_default_value`';
        $criteria->join = 'LEFT JOIN `dictionary` d ON a.`dict_id` = d.`dict_id`';
        $criteria->condition = '
			a.`lookup_id`="' . $lookup_id . '" AND a.`valid`=1 AND d.`dict_id` IS NOT NULL
			AND d.`valid`=1 AND d.`lang`="' . $lang . '" /*AND d.`module`="' . $module . '"*/
		';
        if (is_array($filter) && count($filter)) {
            $criteria->condition .= " AND a.`lookup_value` IN ('" . implode("','", $filter) . "') ";
        }
        if ($order) {
            $criteria->order = 'a.order, d.`dict_value` ASC';
        }

        $res = $app_lookup->findAll($criteria);

        $results = [];
        $results_arr = [];
        $i = 0;
        foreach ($res as $obj) {
            $results[$i] = $obj->getAttributes();
            $v = get_object_vars($obj);
            foreach ($v as $key => $value) {
                $results[$i][$key] = $value;
            }
            if ($one_dim_array) {
                $results_arr[$results[$i]['lookup_value']] = $results[$i]['dict_value'];
            } else {
                $results_arr[$i]['id'] = $results[$i]['lookup_value'];
                $results_arr[$i]['value'] = $results[$i]['dict_value'];
                if (!empty($default_value) && $results_arr[$i]['id'] === $default_value) {
                    $results_arr[$i]['default'] = true;
                }
                if ((int)$results[$i]['lookup_default_value'] === 1) {
                    $results_arr[$i]['default'] = true;
                }
            }
            $i++;
        }

        return $results_arr;
    }

    public static function getLookupValue($lookup_id, $dict_id): string
    {
        $app_lookup = new AppLookup();

        $criteria = new CDbCriteria();
        $criteria->alias = 'a';
        $criteria->select = 'a.`lookup_value` AS lookup_value';
        $criteria->condition = '
			a.`lookup_id`="' . $lookup_id . '" AND a.`dict_id`="' . $dict_id . '"
		';

        $res = $app_lookup->findAll($criteria);

        return $res[0]->lookup_value ?? '';
    }

    public static function getLookupArray($lookup_id): array
    {
        $SQL = "
			SELECT
				lookup_value
			FROM `app_lookup`
			WHERE
					`lookup_id`='$lookup_id'
				AND `valid`=1
		";

        $lookup_values = dbFetchAll($SQL);

        $values = [];

        for ($i = 0; $i < count($lookup_values); ++$i) {
            $values[] = $lookup_values[$i]['lookup_value'];
        }

        return $values;
    }

    public static function generateRandomString(
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
        $length = 10,
        $prefix = null,
        $modelName = null,
        $columnName = null
    ): string {
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        if (!empty($modelName) && !empty($columnName)) {
            $model = new $modelName();
            $criteria = new CDbCriteria();
            $criteria->condition = '`' . $columnName . '` = "' . (!empty($prefix) ? $prefix : '') . $randomString . '"';
            $res = $model->findAll($criteria);
            if (count($res)) {
                return App::generateRandomString($characters, $length, $modelName, $columnName);
            }
        }
        return $prefix . $randomString;
    }

    public static function isRootSessionEnabled(): bool
    {
        if (isset($_SESSION['tiptime']['rootSession']) && $_SESSION['tiptime']['rootSession'] === true) {
            return true;
        }

        return false;
    }

    public static function isUserSwitchEnabled(): bool
    {
        if (isset($_SESSION['tiptime']['userIdToSwitch'])) {
            return true;
        }

        return false;
    }

    public static function replaceSQLFilter($sql = '', $params = [])
    {
        foreach ($params as $id => $val) {
            if (!is_array($val)) {
                $sql = str_replace('{' . $id . '}', $val, $sql);
            }
        }

        return preg_replace('/\{[^}]+\}/', '', $sql); // replace further {}'s to empty str
    }

    public static function getModelAttributesValues($modelName): array
    {
        if (is_null($modelName)) {
            return [];
        }
        $model = new $modelName('select');
        return $model->attributeLabels();
    }

    /**
     * A duplikálás elkerülése érdekében zárjuk a táblát, lekérjük az identifier_name alapján
     * az aktuális azonosítót, növeljük egyel, majd beszúrjuk a táblába. (Létező identifier_name
     * esetén frissítjük az értéket.)
     * A művelet végén feloldjuk a tábla zárolását, innentől más process is használhatja.
     */
    public static function getIncreasedGlobalIdentifier($identifier_name, $start_value = 0, $incrementStep = 1)
    {
        dbExecute('LOCK TABLE `global_identifier` WRITE;');
        {
            $res = dbFetchAll(
                "

				SELECT `identifier_id`
				FROM `global_identifier`
				WHERE `identifier_name` LIKE '$identifier_name'

			"
            );
            $id = $res[0]['identifier_id'] ?? $start_value;
            $id += $incrementStep;
            dbExecute(
                "

				INSERT INTO `global_identifier` (`identifier_name`, `identifier_id`)
				VALUES ('$identifier_name', '$id')
				ON DUPLICATE KEY UPDATE `identifier_id` = VALUES(`identifier_id`)

			"
            );
        }
        dbExecute('UNLOCK TABLES;');

        return $id;
    }

    public static function getUserNameByUserIdInCache(?string $userId): string
    {
        if (empty($userId)) {
            return '';
        }

        $acKey = "system.usernamebyuserid";
        if(AnyCache::has($acKey)) {
            $result = AnyCache::get($acKey);
            return isset($result[$userId]) ? $result[$userId]['username'] : $userId;
        }

        $SQL = 'SELECT
                    u.user_id,
                    u.username
                FROM `user` u
                WHERE
                    u.`status` = '.Status::PUBLISHED.'
                GROUP BY u.`user_id`;
                ';
        $result = dbFetchAll($SQL, 'user_id');
        AnyCache::set($acKey,$result,"-");
        return isset($result[$userId]) ? $result[$userId]['username'] : $userId;
    }

}
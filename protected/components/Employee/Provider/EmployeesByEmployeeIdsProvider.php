<?php

namespace Components\Employee\Provider;
use Components\Employee\Descriptor\EmployeesMainDataByEmployeeIdsFilterDescriptor;

class EmployeesByEmployeeIdsProvider
{
    private EmployeesMainDataByEmployeeIdsFilterDescriptor $employeesFilters;

    public function __construct(EmployeesMainDataByEmployeeIdsFilterDescriptor $employeesFilters)
    {
        $this->employeesFilters = $employeesFilters;
    }

    public function provide(): array
    {
        $defaultEnd  = \App::getSetting("defaultEnd");
        $criteria = new \CDbCriteria();
        $criteria->alias = 't';

        $criteria->addCondition("`status` = :status");
        $criteria->params[':status'] = $this->employeesFilters->getStatus();

        if ($this->employeesFilters->isEmployeeIdsNotEmpty()) {
            $criteria->addCondition("t.employee_id IN ('" . implode("','", $this->employeesFilters->getEmployeeIds()) . "')");
        }

        if ($this->employeesFilters->isValidFromNotEmpty()) {
            $criteria->addCondition('`valid_from` <= :valid_to');
            $criteria->params[':valid_to'] = $this->employeesFilters->getValidFrom()->format('Y-m-d');
        }

        if ($this->employeesFilters->isValidToNotEmpty()) {
            $criteria->addCondition("IFNULL(`valid_to`,'" . $defaultEnd . "') >= :valid_from");
            $criteria->params[':valid_from'] = $this->employeesFilters->getValidTo()->format('Y-m-d');
        }

        return \Employee::model()->findAll($criteria);
    }
}
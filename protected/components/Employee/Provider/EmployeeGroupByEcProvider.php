<?php

namespace Components\Employee\Provider;

class EmployeeGroupByEcProvider
{
    public function provide(array $ecIds, string $group_id)
    {
        $defaultEnd  = \App::getSetting("defaultEnd");

        $criterias = new \CDbCriteria();
        $criterias->alias = 't';
        $criterias->condition = "
        t.status = :published
            AND NOW() BETWEEN t.valid_from AND IFNULL(t.valid_to, :defaultEnd)
            AND t.group_id = :group_id
            AND t.employee_contract_id IN ('" . implode("','", $ecIds) . "')";
        $criterias->params = [
            ':published' => \Status::PUBLISHED,
            ':defaultEnd' => $defaultEnd,
            ':group_id' => $group_id,
        ];

        return \EmployeeGroup::model()->findAll($criterias);
    }
}
<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

final class EmployeesByEmpIdsAndValidityProvider
{
    public function __invoke(array $empIds, \DateTime $validity)
    {
        $criterias = new \CDbCriteria();
        $criterias->condition = "`status` = '".\Status::PUBLISHED . "'".
            ' AND emp_id IN (\''.implode("','", $empIds).'\') '.
            " AND '{$validity->format('Y-m-d')}' BETWEEN `valid_from` AND `valid_to`";

        $lines = \Employee::model()->findAll($criterias);
        if (!\is_array($lines)) {
            $lines = [];
        }

        return array_column($lines, null, 'emp_id');
    }
}

<?php

declare(strict_types = 1);

namespace Components\Employee\Provider;

use Components\Employee\Descriptor\EmployeesMainDataByEmployeeIdsFilterDescriptor;

final class EmployeeContractsFromEmployeeIdsProvider
{
    private EmployeesMainDataByEmployeeIdsFilterDescriptor $employeesFilters;

    public function __construct(EmployeesMainDataByEmployeeIdsFilterDescriptor $employeesFilters)
    {
        $this->employeesFilters = $employeesFilters;
    }
    public function __invoke()
    {
        $defaultEnd  = \App::getSetting("defaultEnd");
        $criteria = new \CDbCriteria();
        $criteria->alias = 't';

        $criteria->addCondition("`status` = :status");
        $criteria->params[':status'] = $this->employeesFilters->getStatus();

        if ($this->employeesFilters->isEmployeeIdsNotEmpty()) {
            $criteria->addCondition("t.employee_id IN ('" . implode("','", $this->employeesFilters->getEmployeeIds()) . "')");
        }

        if ($this->employeesFilters->isValidFromNotEmpty()) {
            $criteria->addCondition('`valid_from` <= :valid_to');
            $criteria->addCondition('`ec_valid_from` <= :valid_to');
            $criteria->params[':valid_to'] = $this->employeesFilters->getValidFrom()->format('Y-m-d');
        }

        if ($this->employeesFilters->isValidToNotEmpty()) {
            $criteria->addCondition("IFNULL(`valid_to`,'" . $defaultEnd . "') >= :valid_from");
            $criteria->addCondition("IFNULL(`ec_valid_to`,'" . $defaultEnd . "') >= :valid_from");
            $criteria->params[':valid_from'] = $this->employeesFilters->getValidTo()->format('Y-m-d');
        }

        return \EmployeeContract::model()->findAll($criteria);
    }
}
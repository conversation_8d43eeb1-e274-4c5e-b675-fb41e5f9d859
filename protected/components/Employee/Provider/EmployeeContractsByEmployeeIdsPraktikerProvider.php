<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

final class EmployeeContractsByEmployeeIdsPraktikerProvider
{
    public function __invoke(array $employeeIds)
    {
        $criteria = new \CDbCriteria();
        $criteria->addCondition('employee_id IN ('.implode(',', $employeeIds).')');
        $criteria->addCondition('CURDATE() BETWEEN valid_from AND valid_to');
        $criteria->addCondition('status = :status');
        $criteria->params = [':status' => \Status::PUBLISHED];
        
        return \EmployeeContract::model()->findAll($criteria);
    }
}

<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

use Components\Core\Exception\ModelNotFoundException;
use Components\Employee\Descriptor\EmployeeCompetencyFilterDescriptor;

final class EmployeeCompetencyProvider
{
    public function provider(EmployeeCompetencyFilterDescriptor $descriptor): \EmployeeCompetency
    {
        $criteria = new \CDbCriteria();
        $from = $descriptor->getStartDate()->format('Y-m-d');
        $to = $descriptor->getEndDate()->format('Y-m-d');
        $criteria->condition = '`status` = ' . \Status::PUBLISHED .
            " AND ('{$from}' <= `valid_to` and '{$to}' >= `valid_from`)";
        if ($descriptor->hasEmployeeContractIds()) {
            $criteria->addInCondition('employee_contract_id', $descriptor->getEmployeeContractIds());
        }
        if ($descriptor->hasCompetencyIds()) {
            $criteria->addInCondition('competency_id', $descriptor->getCompetencyIds());
        }

        $model = \EmployeeCompetency::model()->find($criteria);
        if (!$model instanceof \EmployeeCompetency) {
            throw new ModelNotFoundException($criteria, \EmployeeCompetency::class);
        }
        return $model;
    }
}

<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

use Yii;

final class EmployeesBaseDataByValidIntervalProvider
{
    public function __invoke(string $validFrom, string $validTo): array
    {
        $filter["interval"]["valid_from"] = $validFrom;
        $filter["interval"]["valid_to"] = $validTo;
        $gae = new \GetActiveEmployees($filter, "workForce", NULL, NULL, TRUE, TRUE);
        
        return $gae->getEmployees($filter); 
    }
}

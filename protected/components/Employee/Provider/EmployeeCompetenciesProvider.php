<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

use Components\SchedulingAssistantCore\Interfaces\DateIntervalAndECIDOwnedInterface;

final class EmployeeCompetenciesProvider
{
    public function __invoke(DateIntervalAndECIDOwnedInterface $descriptor): array
    {
        $criterias = new \CDbCriteria();
        $from = $descriptor->getStartDate()->format('Y-m-d');
        $to = $descriptor->getEndDate()->format('Y-m-d');
        $criterias->condition = '`status` = ' . \Status::PUBLISHED .
            " AND `employee_contract_id` IN ('" . implode("', '", $descriptor->getEmployeeContractIds()) . "')" .
            " AND ('{$from}' <= `valid_to` and '{$to}' >= `valid_from`)";

        $employeeCompetencies = \EmployeeCompetency::model()->findAll($criterias);
        if ($employeeCompetencies === null) {
            $employeeCompetencies = [];
        }

        return $employeeCompetencies;
    }
}

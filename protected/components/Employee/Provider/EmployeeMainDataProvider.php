<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

use Components\Employee\Descriptor\EmployeeMainDataFilterDescriptor;
use Components\Employee\Exception\EmployeeMainDataNotFoundException;

final class EmployeeMainDataProvider
{
    /**
     * @throws \Exception
     */
    public function provide(EmployeeMainDataFilterDescriptor $filterDescriptor): array
    {
        $employee = (new \EmployeesWithMainData(
            $filterDescriptor->getValidFrom(),
            $filterDescriptor->getValidTo(),
            $filterDescriptor->getEmployeeContractIDs()
        ))->getEmployeeResults();
        if (empty($employee)) {
            throw new EmployeeMainDataNotFoundException(
                $filterDescriptor->getEmployeeContractIDs(),
                $filterDescriptor->getValidFrom(),
                $filterDescriptor->getValidTo()
            );
        }
        return $employee;
    }
}
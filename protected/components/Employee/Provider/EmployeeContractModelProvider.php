<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

use Components\Core\Exception\NotFoundException;

final class EmployeeContractModelProvider
{
    public function __invoke(string $employeeContractId, string $day): \EmployeeContract
    {
        $criterias = new \CDbCriteria();
        $criterias->condition = "`employee_contract_id` = '{$employeeContractId}'" .
            " AND `status` = '" . \Status::PUBLISHED . "'" .
            " AND '{$day}' BETWEEN `valid_from` AND `valid_to`";

        $employeeContract = \EmployeeContract::model()->find($criterias);
        if ($employeeContract === null) {
            throw new NotFoundException(
                'employee_contract_not_found',
                \CLogger::LEVEL_ERROR,
                'application'
            );
        }

        return $employeeContract;
    }
}

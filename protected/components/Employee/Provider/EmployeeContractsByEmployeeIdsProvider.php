<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

final class EmployeeContractsByEmployeeIdsProvider
{
    public function __invoke(array $employeeIds, string $date = 'CURDATE()')
    {
        $criteria = new \CDbCriteria();
        $criteria->addCondition("employee_id IN ('" . implode("','", $employeeIds) . "')");
        $criteria->addCondition(':date BETWEEN valid_from AND valid_to');
        $criteria->addCondition(':date BETWEEN ec_valid_from AND ec_valid_to');
        $criteria->addCondition('status = :status');
        $criteria->params = [':status' => \Status::PUBLISHED, ':date' => $date];

        return \EmployeeContract::model()->findAll($criteria);
    }
}
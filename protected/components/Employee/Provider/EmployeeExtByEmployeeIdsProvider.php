<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

final class EmployeeExtByEmployeeIdsProvider
{
    public function __invoke(string $extNumber, array $employeeIds, \DateTime $validFrom = null, \DateTime $validTo = null)
    {
        $criteria = new \CDbCriteria();
        $criteria->condition = '`status` = ' . \Status::PUBLISHED . "
            AND `employee_id` IN ('" . implode("', '", $employeeIds) . "')
            ";

        if (!empty($validFrom)) {
            $criteria->addCondition('`valid_from` <= :valid_to');
            $criteria->params[':valid_to'] = $validTo->format('Y-m-d');
        }

        if (!empty($validTo)) {
            $criteria->addCondition("IFNULL(`valid_to`,'" . \App::getSetting("defaultEnd") . "') >= :valid_from");
            $criteria->params[':valid_from'] = $validFrom->format('Y-m-d');
        }
        
        $criteria->order = 'valid_from ASC';
        $className = 'EmployeeExt' . $extNumber;
        $employeeExt = $className::model()->findAll($criteria);
        if (!\is_array($employeeExt)) {
            $employeeExt = [];
        }
        
        return $employeeExt;
    }
}

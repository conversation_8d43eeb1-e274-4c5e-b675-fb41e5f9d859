<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

use Components\Core\Exception\NotFoundException;

final class EmployeeModelProvider
{
    /**
     * @param string $employeeId
     * @param string $date
     * @return \Employee
     */
    public function __invoke(string $employeeId, string $date): \Employee
    {
        $criterias = new \CDbCriteria();
        $criterias->condition = "`employee_id` = '{$employeeId}'" .
            " AND `status` = '" . \Status::PUBLISHED . "'" .
            " AND '{$date}' BETWEEN `valid_from` AND `valid_to`";

        $employee = \Employee::model()->find($criterias);
        if (!$employee instanceof \Employee) {
            throw new NotFoundException(
                'employee_model_not_found',
                \CLogger::LEVEL_ERROR,
                'application'
            );
        }

        return $employee;
    }
}

<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

use Components\Employee\Descriptor\ActiveEmployeeFilterDescriptor;

final class ActiveEmployeeProvider
{
    public const ALL = 'ALL';

    public function provide(ActiveEmployeeFilterDescriptor $filterDescriptor): array
    {
        return $this->provideData($filterDescriptor);
    }

    private function provideData(ActiveEmployeeFilterDescriptor $filterDescriptor): array
    {
        $gae = new \GetActiveEmployees(
            filter: $this->getFilter($filterDescriptor),
            approverProcessId: $filterDescriptor->getApproverProcessIds(),
            controllerID: null,
            userId: null,
            disableApprovalChecks: \App::getRight(null, 'su') ? true : false,
            usePreDefinedFilter: false,
            extraQueryFields: $this->getExtraQueryFields($filterDescriptor),
        );
        return $gae->getEmployees();
    }

    private function convertFilterArrayToString(array $filters): string
    {
        $firstFilter = reset($filters);
        if ($firstFilter === '') {
            return self::ALL;
        } elseif ($firstFilter === 'null') {
            return 'null';
        }
        return implode("','", $filters);
    }

    public function getFilter(ActiveEmployeeFilterDescriptor $filterDescriptor): array
    {
        $filter = [
            'interval' => [
                'valid_from' => $filterDescriptor->getValidFrom()->format('Y-m-d'),
                'valid_to' => $filterDescriptor->getValidTo()->format('Y-m-d'),
            ],
            'employee' => [
                'company_id' => '{company}',
                'payroll_id' => '{payroll}',
            ],
            'unit' => [
                'unit_id' => '{unit}',
            ],
            'employee_contract' => [
                'employee_contract_id' => '{employee_contract}',
            ],
            'workgroup' => [
                'workgroup_id' => '{workgroup}',
            ],
            'company_org_group3' => [
                'company_org_group_id' => '{company_org_group3}',
            ],
        ];
        if (($f = $this->convertFilterArrayToString($filterDescriptor->getExt3Option2())) !== self::ALL) {
            $filter['employee_ext3__ext3_option2'] = $f;
        }
        if (($f = $this->convertFilterArrayToString($filterDescriptor->getExt3Option3())) !== self::ALL) {
            $filter['employee_ext3__ext3_option3'] = $f;
        }
        return $filter;
    }

    public function getExtraQueryFields(ActiveEmployeeFilterDescriptor $filterDescriptor): string
    {
        $extraQueryFields = '';
        if ($filterDescriptor->hasExt3Option2()) {
            $extraQueryFields .= ', `employee_ext3`.`ext3_option2`';
        }
        if ($filterDescriptor->hasExt3Option3()) {
            $extraQueryFields .= ', `employee_ext3`.`ext3_option3`';
        }
        return $extraQueryFields;
    }
}

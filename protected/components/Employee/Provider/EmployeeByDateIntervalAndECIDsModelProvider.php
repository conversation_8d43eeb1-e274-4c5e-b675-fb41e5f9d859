<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

final class EmployeeByDateIntervalAndECIDsModelProvider
{
    /**
     * @param string $from
     * @param string $to
     * @param array $employeeIds
     * @return \Employee[][]
     */
    public function __invoke(string $from, string $to, array $employeeIds): array
    {
        $criterias = new \CDbCriteria();
        $criterias->condition = "`employee_id` IN ('".implode("', '", $employeeIds)."')" .
            " AND `status` = '".\Status::PUBLISHED . "'".
            " AND ('{$from}' BETWEEN `valid_from` AND `valid_to`".
            " OR '{$to}' BETWEEN `valid_from` AND `valid_to`".
            " OR ('{$from}' < `valid_from` AND '{$to}' > `valid_to`))"
        ;

        $employees = \Employee::model()->findAll($criterias);
        if ($employees === null) {
            $employees = [];
        }
        $indexed = [];
        foreach ($employees as $employee) {
            $indexed[$employee->employee_id][] = $employee;
        }

        return $indexed;
    }
}

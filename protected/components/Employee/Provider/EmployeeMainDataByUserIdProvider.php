<?php

declare(strict_types=1);

namespace Components\Employee\Provider;

use Components\Core\Enum\AppConfigEnum;
use Components\Employee\Descriptor\EmployeeMainDataByUserIdFilterDescriptor;
use Components\Employee\Exception\EmployeeMainDataByUserIDNotFoundException;

final class EmployeeMainDataByUserIdProvider
{
    /**
     * @throws \Exception
     */
    public function provide(EmployeeMainDataByUserIdFilterDescriptor $filterDescriptor): array
    {
        $employee = (new \EmployeesWithMainData(
            $filterDescriptor->getValidFrom()->format(AppConfigEnum::DATE_FORMAT),
            $filterDescriptor->getValidTo()->format(AppConfigEnum::DATE_FORMAT),
            [\EmployeesWithMainData::FILTER_KEY_USER_ID => $filterDescriptor->getUserId()]
        ))->getEmployeeResults();
        if (empty($employee)) {
            throw new EmployeeMainDataByUserIDNotFoundException(
                $filterDescriptor->getUserId(),
                $filterDescriptor->getValidFrom()->format(AppConfigEnum::DATE_FORMAT),
                $filterDescriptor->getValidTo()->format(AppConfigEnum::DATE_FORMAT)
            );
        }
        return $employee;
    }
}
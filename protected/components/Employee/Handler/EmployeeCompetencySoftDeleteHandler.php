<?php

declare(strict_types=1);

namespace Components\Employee\Handler;

use Components\Competency\DTO\N1AssociationOfCompetencyIdsDTO;
use Components\Core\Enum\OperationEnum;
use Components\Employee\Descriptor\EmployeeCompetencyFilterDescriptor;
use Components\Employee\Interfaces\EmployeeCompetencyOperationInterface;
use Components\Employee\Provider\EmployeeCompetencyProvider;

final class EmployeeCompetencySoftDeleteHandler implements EmployeeCompetencyOperationInterface
{
    private EmployeeCompetencyProvider $employeeCompetencyProvider;

    public function __construct()
    {
        $this->employeeCompetencyProvider = new EmployeeCompetencyProvider();
    }

    public function handle(N1AssociationOfCompetencyIdsDTO $idsDTO): void
    {
        $now = new \DateTime('now');
        $filterDescriptor = (new EmployeeCompetencyFilterDescriptor())
            ->setStartDate($now)
            ->setEndDate($now)
            ->setEmployeeContractIds([$idsDTO->getEmployeeContractId()])
            ->setCompetencyIds([$idsDTO->getCompetencyId()]);

        $model = $this->employeeCompetencyProvider->provider($filterDescriptor);
        $model->status = \Status::DELETED;
        $model->save();
    }

    public function isSupported(string $operation): bool
    {
        return $operation === OperationEnum::OPERATION_SOFT_DELETE;
    }
}
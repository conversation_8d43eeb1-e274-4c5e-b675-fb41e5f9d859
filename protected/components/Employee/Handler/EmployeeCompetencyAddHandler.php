<?php

declare(strict_types=1);

namespace Components\Employee\Handler;

use Components\Competency\DTO\N1AssociationOfCompetencyIdsDTO;
use Components\Core\Enum\OperationEnum;
use Components\Employee\Descriptor\EmployeeCompetencyDescriptor;
use Components\Employee\Interfaces\EmployeeCompetencyOperationInterface;
use Components\Employee\Transformer\EmployeeCompetencyDescriptorToModelTransformer;

final readonly class EmployeeCompetencyAddHandler implements EmployeeCompetencyOperationInterface
{
    public function handle(N1AssociationOfCompetencyIdsDTO $idsDTO): void
    {
        $nowDate = new \DateTime('now');
        $defaultEndDate = new \DateTime(\App::getSetting('defaultEnd'));
        $descriptor = (new EmployeeCompetencyDescriptor())
            ->setEmployeeContractId($idsDTO->getEmployeeContractId())
            ->setCompetencyId($idsDTO->getCompetencyId())
            ->setLevelId('1')
            ->setStatus(\Status::PUBLISHED)
            ->setValidFrom($nowDate)
            ->setValidTo($defaultEndDate)
            ->setCreatedBy(\Yang::getUserId())
            ->setCreatedOn(new \DateTime());
        $model = (new EmployeeCompetencyDescriptorToModelTransformer())
            ->transform($descriptor, new \EmployeeCompetency());
        if (!$model->validate()) {
            throw new \Exception('CompetencyRequirement did not validated');
        }
        $model->save();
    }

    public function isSupported(string $operation): bool
    {
        return $operation === OperationEnum::OPERATION_ADD;
    }
}
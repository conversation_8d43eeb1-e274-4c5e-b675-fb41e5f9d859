<?php

declare(strict_types=1);

namespace Components\Employee\Descriptor;

final class EmployeeCompetencyFilterDescriptor
{
    private \DateTime $startDate;
    private \DateTime $endDate;
    private array $employeeContractIds;
    private array $competencyIds;

    public function getStartDate(): \DateTime
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTime $startDate): self
    {
        $this->startDate = $startDate;
        return $this;
    }

    public function getEndDate(): \DateTime
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTime $endDate): self
    {
        $this->endDate = $endDate;
        return $this;
    }

    public function getEmployeeContractIds(): array
    {
        return $this->employeeContractIds;
    }

    public function hasEmployeeContractIds(): bool
    {
        return !empty($this->employeeContractIds);
    }

    public function setEmployeeContractIds(array $employeeContractIds): self
    {
        $this->employeeContractIds = $employeeContractIds;
        return $this;
    }

    public function getCompetencyIds(): array
    {
        return $this->competencyIds;
    }

    public function hasCompetencyIds(): bool
    {
        return !empty($this->competencyIds);
    }

    public function setCompetencyIds(array $competencyIds): self
    {
        $this->competencyIds = $competencyIds;
        return $this;
    }
}
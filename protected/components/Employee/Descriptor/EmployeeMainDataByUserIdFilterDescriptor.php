<?php

declare(strict_types=1);

namespace Components\Employee\Descriptor;

final class EmployeeMainDataByUserIdFilterDescriptor
{
    private string $userId;
    private \DateTime $validFrom;
    private \DateTime $validTo;

    public function __construct(string $userId, \DateTime $validFrom, \DateTime $validTo)
    {
        $this->validFrom = $validFrom;
        $this->validTo = $validTo;
        $this->userId = $userId;
    }

    public function getUserId(): string
    {
        return $this->userId;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }
}
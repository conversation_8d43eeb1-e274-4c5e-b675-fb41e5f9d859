<?php

declare(strict_types=1);

namespace Components\Employee\Descriptor;

final class EmployeeMainDataFilterDescriptor
{
    public const EMPLOYEE_CONTRACT_ID = 'employee_contract_id';

    private string $validFrom;
    private string $validTo;
    /**
     * @var string[]
     */
    private array $employeeContractIDs;

    /**
     * @param string[] $employeeContractIDs
     */
    public function __construct(string $validFrom, string $validTo, array $employeeContractIDs)
    {
        $this->validFrom = $validFrom;
        $this->validTo = $validTo;
        $this->employeeContractIDs[self::EMPLOYEE_CONTRACT_ID] = $employeeContractIDs;
    }

    public function getValidFrom(): string
    {
        return $this->validFrom;
    }

    public function getValidTo(): string
    {
        return $this->validTo;
    }

    public function getEmployeeContractIDs(): array
    {
        return $this->employeeContractIDs;
    }
}
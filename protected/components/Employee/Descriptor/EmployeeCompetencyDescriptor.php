<?php

declare(strict_types=1);

namespace Components\Employee\Descriptor;

final class EmployeeCompetencyDescriptor
{
    private string $employeeContractId;
    private string $competencyId;
    private ?string $competencyGroupId;
    private string $level_id;
    private ?string $results;
    private ?int $order;
    private ?string $note;
    private int $status;
    private \DateTime $validFrom;
    private \DateTime $validTo;
    private string $createdBy;
    private \DateTime $createdOn;
    private ?string $modifiedBy;
    private ?\DateTime $modifiedOn;
    private ?int $preRowId;

    public function hasCompetencyGroupId(): bool
    {
        return isset($this->competencyGroupId);
    }

    public function hasResults(): bool
    {
        return isset($this->results);
    }

    public function hasOrder(): bool
    {
        return isset($this->order);
    }

    public function hasNote(): bool
    {
        return isset($this->note);
    }

    public function hasModifiedOn(): bool
    {
        return isset($this->modifiedOn);
    }

    public function hasModifiedBy(): bool
    {
        return isset($this->modifiedBy);
    }

    public function hasPreRowId(): bool
    {
        return isset($this->preRowId);
    }

    public function getEmployeeContractId(): string
    {
        return $this->employeeContractId;
    }

    public function setEmployeeContractId(string $employeeContractId): self
    {
        $this->employeeContractId = $employeeContractId;
        return $this;
    }

    public function getCompetencyId(): string
    {
        return $this->competencyId;
    }

    public function setCompetencyId(string $competencyId): self
    {
        $this->competencyId = $competencyId;
        return $this;
    }

    public function getCompetencyGroupId(): string
    {
        return $this->competencyGroupId;
    }

    public function setCompetencyGroupId(string $competencyGroupId): self
    {
        $this->competencyGroupId = $competencyGroupId;
        return $this;
    }

    public function getLevelId(): string
    {
        return $this->level_id;
    }

    public function setLevelId(string $level_id): self
    {
        $this->level_id = $level_id;
        return $this;
    }

    public function getResults(): ?string
    {
        return $this->results;
    }

    public function setResults(?string $results): self
    {
        $this->results = $results;
        return $this;
    }

    public function getOrder(): ?int
    {
        return $this->order;
    }

    public function setOrder(?int $order): self
    {
        $this->order = $order;
        return $this;
    }

    public function getNote(): ?string
    {
        return $this->note;
    }

    public function setNote(?string $note): self
    {
        $this->note = $note;
        return $this;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function setValidFrom(\DateTime $validFrom): self
    {
        $this->validFrom = $validFrom;
        return $this;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }

    public function setValidTo(\DateTime $validTo): self
    {
        $this->validTo = $validTo;
        return $this;
    }

    public function getCreatedBy(): string
    {
        return $this->createdBy;
    }

    public function setCreatedBy(string $createdBy): self
    {
        $this->createdBy = $createdBy;
        return $this;
    }

    public function getCreatedOn(): \DateTime
    {
        return $this->createdOn;
    }

    public function setCreatedOn(\DateTime $createdOn): self
    {
        $this->createdOn = $createdOn;
        return $this;
    }

    public function getModifiedBy(): ?string
    {
        return $this->modifiedBy;
    }

    public function setModifiedBy(?string $modifiedBy): self
    {
        $this->modifiedBy = $modifiedBy;
        return $this;
    }

    public function getModifiedOn(): ?\DateTime
    {
        return $this->modifiedOn;
    }

    public function setModifiedOn(?\DateTime $modifiedOn): self
    {
        $this->modifiedOn = $modifiedOn;
        return $this;
    }

    public function getPreRowId(): ?int
    {
        return $this->preRowId;
    }

    public function setPreRowId(?int $preRowId): self
    {
        $this->preRowId = $preRowId;
        return $this;
    }
}
<?php

declare(strict_types=1);

namespace Components\Employee\Descriptor;

final class EmployeeMainDataDescriptor
{
    private string $title;
    private string $firstName;
    private string $lastName;
    private string $fullName;
    private string $employeeId;
    private string $empId;
    private string $employeeContractId;
    private string $employeeContractNumber;
    private string $companyId;
    private string $payrollId;
    private string $unitId;
    private string $workgroupId;
    private string $costId;
    private string $costcenterId;
    private string $companyOrgGroup1Id;
    private string $companyOrgGroup2Id;
    private string $companyOrgGroup3Id;
    private int $dailyWorktime;
    private int $workOrder;

    private string $companyName;
    private string $payrollName;
    private string $unitName;
    private string $workgroupName;
    private string $companyOrgGroup1Name;
    private string $companyOrgGroup2Name;
    private string $companyOrgGroup3Name;

    private \DateTime $valIdFrom;
    private \DateTime $valIdTo;


    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): EmployeeMainDataDescriptor
    {
        $this->title = $title;
        return $this;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): EmployeeMainDataDescriptor
    {
        $this->firstName = $firstName;
        return $this;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): EmployeeMainDataDescriptor
    {
        $this->lastName = $lastName;
        return $this;
    }

    public function getFullName(): string
    {
        return $this->fullName;
    }

    public function setFullName(string $fullName): EmployeeMainDataDescriptor
    {
        $this->fullName = $fullName;
        return $this;
    }

    public function getEmployeeId(): string
    {
        return $this->employeeId;
    }

    public function setEmployeeId(string $employeeId): EmployeeMainDataDescriptor
    {
        $this->employeeId = $employeeId;
        return $this;
    }

    public function getEmpId(): string
    {
        return $this->empId;
    }

    public function setEmpId(string $empId): EmployeeMainDataDescriptor
    {
        $this->empId = $empId;
        return $this;
    }

    public function getEmployeeContractId(): string
    {
        return $this->employeeContractId;
    }

    public function setEmployeeContractId(string $employeeContractId): EmployeeMainDataDescriptor
    {
        $this->employeeContractId = $employeeContractId;
        return $this;
    }

    public function getEmployeeContractNumber(): string
    {
        return $this->employeeContractNumber;
    }

    public function setEmployeeContractNumber(string $employeeContractNumber): EmployeeMainDataDescriptor
    {
        $this->employeeContractNumber = $employeeContractNumber;
        return $this;
    }

    public function getCompanyId(): string
    {
        return $this->companyId;
    }

    public function setCompanyId(string $companyId): EmployeeMainDataDescriptor
    {
        $this->companyId = $companyId;
        return $this;
    }

    public function getPayrollId(): string
    {
        return $this->payrollId;
    }

    public function setPayrollId(string $payrollId): EmployeeMainDataDescriptor
    {
        $this->payrollId = $payrollId;
        return $this;
    }

    public function getUnitId(): string
    {
        return $this->unitId;
    }

    public function setUnitId(string $unitId): EmployeeMainDataDescriptor
    {
        $this->unitId = $unitId;
        return $this;
    }

    public function getWorkgroupId(): string
    {
        return $this->workgroupId;
    }

    public function setWorkgroupId(string $workgroupId): EmployeeMainDataDescriptor
    {
        $this->workgroupId = $workgroupId;
        return $this;
    }

    public function getCostId(): string
    {
        return $this->costId;
    }

    public function setCostId(string $costId): EmployeeMainDataDescriptor
    {
        $this->costId = $costId;
        return $this;
    }

    public function getCostcenterId(): string
    {
        return $this->costcenterId;
    }

    public function setCostcenterId(string $costcenterId): EmployeeMainDataDescriptor
    {
        $this->costcenterId = $costcenterId;
        return $this;
    }

    public function getCompanyOrgGroup1Id(): string
    {
        return $this->companyOrgGroup1Id;
    }

    public function setCompanyOrgGroup1Id(string $companyOrgGroup1Id): EmployeeMainDataDescriptor
    {
        $this->companyOrgGroup1Id = $companyOrgGroup1Id;
        return $this;
    }

    public function getCompanyOrgGroup2Id(): string
    {
        return $this->companyOrgGroup2Id;
    }

    public function setCompanyOrgGroup2Id(string $companyOrgGroup2Id): EmployeeMainDataDescriptor
    {
        $this->companyOrgGroup2Id = $companyOrgGroup2Id;
        return $this;
    }

    public function getCompanyOrgGroup3Id(): string
    {
        return $this->companyOrgGroup3Id;
    }

    public function setCompanyOrgGroup3Id(string $companyOrgGroup3Id): EmployeeMainDataDescriptor
    {
        $this->companyOrgGroup3Id = $companyOrgGroup3Id;
        return $this;
    }

    public function getDailyWorktime(): int
    {
        return $this->dailyWorktime;
    }

    public function setDailyWorktime(int $dailyWorktime): EmployeeMainDataDescriptor
    {
        $this->dailyWorktime = $dailyWorktime;
        return $this;
    }

    public function getWorkOrder(): int
    {
        return $this->workOrder;
    }

    public function setWorkOrder(int $workOrder): EmployeeMainDataDescriptor
    {
        $this->workOrder = $workOrder;
        return $this;
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function setCompanyName(string $companyName): EmployeeMainDataDescriptor
    {
        $this->companyName = $companyName;
        return $this;
    }

    public function getPayrollName(): string
    {
        return $this->payrollName;
    }

    public function setPayrollName(string $payrollName): EmployeeMainDataDescriptor
    {
        $this->payrollName = $payrollName;
        return $this;
    }

    public function getUnitName(): string
    {
        return $this->unitName;
    }

    public function setUnitName(string $unitName): EmployeeMainDataDescriptor
    {
        $this->unitName = $unitName;
        return $this;
    }

    public function getWorkgroupName(): string
    {
        return $this->workgroupName;
    }

    public function setWorkgroupName(string $workgroupName): EmployeeMainDataDescriptor
    {
        $this->workgroupName = $workgroupName;
        return $this;
    }

    public function getCompanyOrgGroup1Name(): string
    {
        return $this->companyOrgGroup1Name;
    }

    public function setCompanyOrgGroup1Name(string $companyOrgGroup1Name): EmployeeMainDataDescriptor
    {
        $this->companyOrgGroup1Name = $companyOrgGroup1Name;
        return $this;
    }

    public function getCompanyOrgGroup2Name(): string
    {
        return $this->companyOrgGroup2Name;
    }

    public function setCompanyOrgGroup2Name(string $companyOrgGroup2Name): EmployeeMainDataDescriptor
    {
        $this->companyOrgGroup2Name = $companyOrgGroup2Name;
        return $this;
    }

    public function getCompanyOrgGroup3Name(): string
    {
        return $this->companyOrgGroup3Name;
    }

    public function setCompanyOrgGroup3Name(string $companyOrgGroup3Name): EmployeeMainDataDescriptor
    {
        $this->companyOrgGroup3Name = $companyOrgGroup3Name;
        return $this;
    }

    public function getValIdFrom(): \DateTime
    {
        return $this->valIdFrom;
    }

    public function setValIdFrom(\DateTime $valIdFrom): EmployeeMainDataDescriptor
    {
        $this->valIdFrom = $valIdFrom;
        return $this;
    }

    public function getValIdTo(): \DateTime
    {
        return $this->valIdTo;
    }

    public function setValIdTo(\DateTime $valIdTo): EmployeeMainDataDescriptor
    {
        $this->valIdTo = $valIdTo;
        return $this;
    }
}
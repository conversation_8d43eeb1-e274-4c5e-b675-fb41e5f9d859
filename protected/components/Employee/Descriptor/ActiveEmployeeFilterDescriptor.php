<?php

declare(strict_types=1);

namespace Components\Employee\Descriptor;

final class ActiveEmployeeFilterDescriptor
{
    private array $approverProcessIds;

    private \DateTime $validFrom;
    private \DateTime $validTo;

    private array $ext3Option2;
    private array $ext3Option3;

    public function getApproverProcessIds(): array
    {
        return $this->approverProcessIds;
    }

    public function setApproverProcessIds(array $approverProcessIds): self
    {
        $this->approverProcessIds = $approverProcessIds;
        return $this;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function setValidFrom(\DateTime $validFrom): self
    {
        $this->validFrom = $validFrom;
        return $this;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }

    public function setValidTo(\DateTime $validTo): self
    {
        $this->validTo = $validTo;
        return $this;
    }

    public function getExt3Option2(): array
    {
        return $this->ext3Option2;
    }

    public function hasExt3Option2(): bool
    {
        return isset($this->ext3Option2);
    }

    public function setExt3Option2(array $ext3Option2): self
    {
        $this->ext3Option2 = $ext3Option2;
        return $this;
    }

    public function getExt3Option3(): array
    {
        return $this->ext3Option3;
    }

    public function hasExt3Option3(): bool
    {
        return isset($this->ext3Option3);
    }

    public function setExt3Option3(array $ext3Option3): self
    {
        $this->ext3Option3 = $ext3Option3;
        return $this;
    }
}
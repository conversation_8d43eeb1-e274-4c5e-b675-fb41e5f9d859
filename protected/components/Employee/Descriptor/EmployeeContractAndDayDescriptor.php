<?php

declare(strict_types=1);

namespace Components\Employee\Descriptor;

final class EmployeeContractAndDayDescriptor
{
    private string $employeeContract;
    private string $day;

    public function __construct(string $employeeContract, string $day)
    {
        $this->employeeContract = $employeeContract;
        $this->day = $day;
    }

    public function getEmployeeContract(): string
    {
        return $this->employeeContract;
    }

    public function getDay(): string
    {
        return $this->day;
    }


}
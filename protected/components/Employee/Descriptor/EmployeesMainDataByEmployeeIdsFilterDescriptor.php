<?php

declare(strict_types=1);

namespace Components\Employee\Descriptor;

final class EmployeesMainDataByEmployeeIdsFilterDescriptor
{
    private array $employeeIds;
    private \DateTime $validFrom;
    private \DateTime $validTo;
    private int $status = \Status::PUBLISHED;

    public function __construct(array $employeeIds, \DateTime $validFrom, \DateTime $validTo)
    {
        $this->validFrom = $validFrom;
        $this->validTo = $validTo;
        $this->employeeIds = $employeeIds;
    }

    public function getEmployeeIds(): array
    {
        return $this->employeeIds;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): void
    {
        $this->status = $status;
    }

    public function isEmployeeIdsNotEmpty(): bool
    {
        return !empty($this->employeeIds);
    }

    public function isValidFromNotEmpty(): bool
    {
        return !empty($this->validFrom);
    }

    public function isValidToNotEmpty(): bool
    {
        return !empty($this->validTo);
    }
}
<?php

declare(strict_types=1);

namespace Components\Employee\Descriptor;

final class EmployeeContractsAndIntervalDescriptor
{
    /**
     * @var string[]
     */
    private array $employeeContracts;
    private string $from;
    private string $to;

    public function __construct(array $employeeContracts, string $from, string $to)
    {
        $this->employeeContracts = $employeeContracts;
        $this->from = $from;
        $this->to = $to;
    }

    public function getEmployeeContracts(): array
    {
        return $this->employeeContracts;
    }

    public function getFrom(): string
    {
        return $this->from;
    }

    public function getTo(): string
    {
        return $this->to;
    }
}
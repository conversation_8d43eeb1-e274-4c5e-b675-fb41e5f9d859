<?php

declare(strict_types=1);

namespace Components\Employee\Strategy;

use Components\Employee\Interfaces\EmployeeFullnameGuesserStrategyInterface;

final class HungarianLanguageEmployeeFullnameGuesserStrategy implements EmployeeFullnameGuesserStrategyInterface
{
    public function guess(\Employee $employee): string
    {
        return $employee->title
            ? ($employee->title . ' ' . $employee->last_name . ' ' . $employee->first_name)
            : ($employee->last_name . ' ' . $employee->first_name);
    }
}

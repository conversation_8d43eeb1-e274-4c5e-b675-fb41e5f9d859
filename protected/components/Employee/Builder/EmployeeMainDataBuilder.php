<?php

declare(strict_types=1);

namespace Components\Employee\Builder;

use Components\Employee\Descriptor\EmployeeMainDataDescriptor;
use Components\Employee\Descriptor\EmployeeMainDataFilterDescriptor;
use Components\Employee\Provider\EmployeeMainDataProvider;
use Components\Employee\Transformer\EmployeeMainDataArrayToEmployeeMainDataDescriptorTransformer;

final class EmployeeMainDataBuilder
{
    /**
     * @return EmployeeMainDataDescriptor[][]
     * @throws \Exception
     */
    public function build(EmployeeMainDataFilterDescriptor $filterDescriptor): array
    {
        $employeeDescriptor = new EmployeeMainDataDescriptor();
        $employees = (new EmployeeMainDataProvider())->provide($filterDescriptor);
        $employeeDescriptors = [];
        foreach ($employees as $contractId => $employee) {
            foreach ($employee as $employeeOneRow)
            $employeeDescriptors[$contractId][] =
                (new EmployeeMainDataArrayToEmployeeMainDataDescriptorTransformer())
                    ->transform(
                        $employeeOneRow,
                        $employeeDescriptor
                    );
        }
        return $employeeDescriptors;
    }
}
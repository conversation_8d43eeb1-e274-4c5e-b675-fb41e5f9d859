<?php

declare(strict_types=1);

namespace Components\Employee\Builder;

use Components\Employee\Descriptor\EmployeeMainDataByUserIdFilterDescriptor;
use Components\Employee\Descriptor\EmployeeMainDataDescriptor;
use Components\Employee\Provider\EmployeeMainDataByUserIdProvider;
use Components\Employee\Transformer\EmployeeMainDataArrayToEmployeeMainDataDescriptorTransformer;

final class EmployeeMainDataByUserIdBuilder
{
    private EmployeeMainDataByUserIdProvider $provider;

    public function __construct()
    {
        $this->provider = new EmployeeMainDataByUserIdProvider();
    }

    /**
     * @param EmployeeMainDataByUserIdFilterDescriptor $filterDescriptor
     * @return EmployeeMainDataDescriptor[]
     * @throws \Exception
     */
    public function build(EmployeeMainDataByUserIdFilterDescriptor $filterDescriptor): array
    {
        $employeeDescriptor = new EmployeeMainDataDescriptor();
        $employees = $this->provider->provide($filterDescriptor);
        $employeeDescriptors = [];
        foreach ($employees as $contractId => $employee) {
            foreach ($employee as $employeeOneRow) {
                $employeeDescriptors[$contractId][] =
                    (new EmployeeMainDataArrayToEmployeeMainDataDescriptorTransformer())
                        ->transform(
                            $employeeOneRow,
                            $employeeDescriptor
                        );
            }
        }
        return $employeeDescriptors;
    }
}
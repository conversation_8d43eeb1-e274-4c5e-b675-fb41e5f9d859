<?php

declare(strict_types=1);

namespace Components\Employee\Enum;

final class EmployeeMainDataEnum
{
    public const TITLE = 'title';
    public const FIRST_NAME = 'first_name';
    public const LAST_NAME = 'last_name';
    public const NAME_OF_BIRTH = 'nameofbirth';
    public const TAX_NUMBER = 'tax_number';
    public const GENDER = 'gender';
    public const FULL_NAME = 'fullname';
    public const EMPLOYEE_ID = 'employee_id';
    public const EMP_ID = 'emp_id';
    public const EMPLOYEE_CONTRACT_ID = 'employee_contract_id';
    public const EMPLOYEE_CONTRACT_NUMBER = 'employee_contract_number';
    public const EMPLOYEE_CONTRACT_TYPE = 'employee_contract_type';
    public const WAGE_TYPE = 'wage_type';
    public const COMPANY_ID = 'company_id';
    public const PAYROLL_ID = 'payroll_id';
    public const UNIT_ID = 'unit_id';
    public const WORKGROUP_ID = 'workgroup_id';
    public const EMPLOYEE_POSITION_ID = 'employee_position_id';
    public const DEF_COST_ID = 'def_cost_id';
    public const DEF_COST_CENTER_ID = 'def_cost_center_id';
    public const COMPANY_ORG_GROUP_ID = 'company_org_group_id';
    public const COMPANY_ORG_GROUP_1_ID = 'company_org_group1_id';
    public const COMPANY_ORG_GROUP_2_ID = 'company_org_group2_id';
    public const COMPANY_ORG_GROUP_3_ID = 'company_org_group3_id';
    public const DAILY_WORKTIME = 'daily_worktime';
    public const WORK_ORDER = 'work_order';

    public const COMPANY_NAME = 'company_name';
    public const PAYROLL_NAME = 'payroll_name';
    public const UNIT_NAME = 'unit_name';
    public const WORKGROUP_NAME = 'workgroup_name';
    public const COMPANY_ORG_GROUP_NAME = 'company_org_group_name';
    public const COMPANY_ORG_GROUP_1_NAME = 'company_org_group1_name';
    public const COMPANY_ORG_GROUP_2_NAME = 'company_org_group2_name';
    public const COMPANY_ORG_GROUP_3_NAME = 'company_org_group3_name';

    public const EC_END_TYPE = 'ec_end_type';
    public const EC_VALID_FROM = 'ec_valid_from';
    public const VALID_FROM = 'valid_from';
    public const EC_VALID_TO = 'ec_valid_to';
    public const VALID_TO = 'valid_to';
}
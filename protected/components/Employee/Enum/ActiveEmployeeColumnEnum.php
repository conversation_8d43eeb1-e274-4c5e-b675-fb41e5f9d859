<?php

declare(strict_types=1);

namespace Components\Employee\Enum;

final class ActiveEmployeeColumnEnum
{
    public const EMPLOYEE_ID = 'employee_id';
    public const EMPLOYEE_CONTRACT_ID = 'employee_contract_id';
    public const POSITION_NAME = 'employee_position_name';
    public const FULL_NAME = 'fullname';
    public const FIRST_NAME = 'first_name';
    public const LAST_NAME = 'last_name';
    public const COMPANY_ORG_GROUP3_ID = 'company_org_group3_id';
    public const COMPANY_ORG_GROUP3_NAME = 'company_org_group3_name';
    public const EXT3_OPTION2 = 'ext3_option2';
    public const EXT3_OPTION3 = 'ext3_option3';

}
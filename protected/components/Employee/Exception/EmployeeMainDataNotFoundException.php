<?php

declare(strict_types=1);

namespace Components\Employee\Exception;

use Components\Core\Exception\AbstractNotFoundException;

final class EmployeeMainDataNotFoundException extends AbstractNotFoundException
{
    /**
     * @var string[]
     */
    private array $employeeContractIds;
    private string $from;
    private string $to;

    public function __construct(array $employeeContractIds, string $from, string $to)
    {
        $this->employeeContractIds = $employeeContractIds;
        $this->from = $from;
        $this->to = $to;
    }

    protected function criterias(): string
    {
        $contracts = json_encode($this->employeeContractIds);
        return "from: {$this->from}, to: {$this->to}, employeeContractId: {$contracts}";
    }

    protected function name(): string
    {
        return 'employee';
    }
}
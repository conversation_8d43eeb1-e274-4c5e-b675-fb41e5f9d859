<?php

declare(strict_types=1);

namespace Components\Employee\Exception;

use Components\Core\Exception\AbstractNotFoundException;

final class EmployeeMainDataByUserIDNotFoundException extends AbstractNotFoundException
{
    private string $userId;
    private string $from;
    private string $to;

    public function __construct(string $userId, string $from, string $to)
    {
        $this->userId = $userId;
        $this->from = $from;
        $this->to = $to;
    }

    protected function criterias(): string
    {
        return "from: {$this->from}, to: {$this->to}, userId: {$this->userId}";
    }

    protected function name(): string
    {
        return 'employee';
    }
}
<?php

declare(strict_types=1);

namespace Components\Employee\Transformer;

use Components\Employee\Descriptor\EmployeeMainDataDescriptor;
use Components\Employee\Enum\EmployeeMainDataEnum;
use Components\WorkScheduleCore\Transformer\StringTransformer;

final class EmployeeMainDataArrayToEmployeeMainDataDescriptorTransformer
{
    public const DEF_DAILY_WORKTIME = 8;

    private StringTransformer $stringTransformer;

    public function __construct()
    {
        $this->stringTransformer = new StringTransformer();
    }

    /**
     * @throws \Exception
     */
    public function transform(array $employee, EmployeeMainDataDescriptor $descriptor): EmployeeMainDataDescriptor
    {
        $descriptor
            ->setTitle($employee[EmployeeMainDataEnum::TITLE] ?? '')
            ->setFirstName($employee[EmployeeMainDataEnum::FIRST_NAME] ?? '')
            ->setLastName($employee[EmployeeMainDataEnum::LAST_NAME] ?? '')
            ->setFullName($employee[EmployeeMainDataEnum::FULL_NAME])
            ->setEmployeeId($employee[EmployeeMainDataEnum::EMPLOYEE_ID] ?? '')
            ->setEmpId($employee[EmployeeMainDataEnum::EMP_ID] ?? '')
            ->setEmployeeContractId($employee[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_ID] ?? '')
            ->setEmployeeContractNumber($employee[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_NUMBER] ?? '')
            ->setCompanyId($employee[EmployeeMainDataEnum::COMPANY_ID] ?? '')
            ->setPayrollId($employee[EmployeeMainDataEnum::PAYROLL_ID] ?? '')
            ->setUnitId($employee[EmployeeMainDataEnum::UNIT_ID] ?? '')
            ->setWorkgroupId($employee[EmployeeMainDataEnum::WORKGROUP_ID] ?? '')
            ->setCostId($employee[EmployeeMainDataEnum::DEF_COST_ID] ?? '')
            ->setCostcenterId($employee[EmployeeMainDataEnum::DEF_COST_CENTER_ID] ?? '')
            ->setCompanyOrgGroup1Id($employee[EmployeeMainDataEnum::COMPANY_ORG_GROUP_1_ID] ?? '')
            ->setCompanyOrgGroup2Id($employee[EmployeeMainDataEnum::COMPANY_ORG_GROUP_2_ID] ?? '')
            ->setCompanyOrgGroup3Id($employee[EmployeeMainDataEnum::COMPANY_ORG_GROUP_3_ID] ?? '')
            ->setDailyWorktime((int)($employee[EmployeeMainDataEnum::DAILY_WORKTIME]) ?? self::DEF_DAILY_WORKTIME)
            ->setWorkOrder((int)$employee[EmployeeMainDataEnum::WORK_ORDER])
            ->setCompanyName($employee[EmployeeMainDataEnum::COMPANY_NAME] ?? '')
            ->setPayrollName($employee[EmployeeMainDataEnum::PAYROLL_NAME] ?? '')
            ->setUnitName($employee[EmployeeMainDataEnum::UNIT_NAME] ?? '')
            ->setWorkgroupName($employee[EmployeeMainDataEnum::WORKGROUP_NAME] ?? '')
            ->setCompanyOrgGroup1Name($employee[EmployeeMainDataEnum::COMPANY_ORG_GROUP_1_NAME] ?? '')
            ->setCompanyOrgGroup2Name($employee[EmployeeMainDataEnum::COMPANY_ORG_GROUP_2_NAME] ?? '')
            ->setCompanyOrgGroup3Name($employee[EmployeeMainDataEnum::COMPANY_ORG_GROUP_3_NAME] ?? '')
            ->setValIdFrom(
                $this->stringTransformer->convertStringToDateTime(
                    $employee[EmployeeMainDataEnum::VALID_FROM] . ' 00:00:00'
                )
            )
            ->setValIdTo(
                $this->stringTransformer->convertStringToDateTime(
                    $employee[EmployeeMainDataEnum::VALID_TO] . ' 00:00:00',
                )
            );
        return $descriptor;
    }
}
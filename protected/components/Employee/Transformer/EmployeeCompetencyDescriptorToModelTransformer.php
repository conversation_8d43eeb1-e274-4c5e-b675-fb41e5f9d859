<?php

declare(strict_types=1);

namespace Components\Employee\Transformer;

use Components\Employee\Descriptor\EmployeeCompetencyDescriptor;

final class EmployeeCompetencyDescriptorToModelTransformer
{
    public function transform(EmployeeCompetencyDescriptor $descriptor, \EmployeeCompetency $model): \EmployeeCompetency
    {
        $model->employee_contract_id = $descriptor->getEmployeeContractId();
        $model->competency_id = $descriptor->getCompetencyId();
        $model->status = $descriptor->getStatus();
        $model->valid_from = $descriptor->getValidFrom()->format('Y-m-d');
        $model->valid_to = $descriptor->getValidTo()->format('Y-m-d');
        $model->created_by = $descriptor->getCreatedBy();
        $model->created_on = $descriptor->getCreatedOn()->format('Y-m-d H:i:s');

        if ($descriptor->hasCompetencyGroupId()) {
            $model->competency_group_id = $descriptor->getCompetencyGroupId();
        }
        $model->level_id = $descriptor->getLevelId();
        if ($descriptor->hasResults()) {
            $model->result = $descriptor->getResults();
        }
        if ($descriptor->hasOrder()) {
            $model->result = $descriptor->getOrder();
        }
        if ($descriptor->hasNote()) {
            $model->result = $descriptor->getNote();
        }
        if ($descriptor->hasModifiedBy()) {
            $model->modified_by = $descriptor->getModifiedBy();
        }
        if ($descriptor->hasModifiedOn()) {
            $model->modified_by = $descriptor->getModifiedOn()->format('Y-m-d H:i:s');
        }
        if ($descriptor->hasPreRowId()) {
            $model->modified_by = $descriptor->getPreRowId();
        }
        return $model;
    }
}
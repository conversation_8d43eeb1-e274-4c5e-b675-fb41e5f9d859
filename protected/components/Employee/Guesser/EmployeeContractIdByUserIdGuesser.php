<?php

declare(strict_types=1);

namespace Components\Employee\Guesser;

use Components\Employee\Builder\EmployeeMainDataByUserIdBuilder;
use Components\Employee\Descriptor\EmployeeMainDataByUserIdFilterDescriptor;

final readonly class EmployeeContractIdByUserIdGuesser
{
    private EmployeeMainDataByUserIdBuilder $employeeMainDataByUserIdBuilder;

    public function __construct()
    {
        $this->employeeMainDataByUserIdBuilder = new EmployeeMainDataByUserIdBuilder();
    }

    public function guess(EmployeeMainDataByUserIdFilterDescriptor $filterDescriptor): string
    {
        $employeeDescriptors = $this->employeeMainDataByUserIdBuilder->build($filterDescriptor);
        $employeeContractIds = array_keys($employeeDescriptors);
        if (count($employeeContractIds) !== 1) {
            new \Exception(
                'EmployeeContractIdByUserIdGuesser more employee contracts found. ' .
                '(' . json_encode($employeeContractIds) . ')'
            );
        }
        return reset($employeeContractIds);
    }
}
<?php

declare(strict_types=1);

namespace Components\Employee\Guesser;

use Components\Core\Enum\ConfigParameterNameEnum;
use Components\Core\Enum\CustomerDBPatchNameEnum;
use Components\Core\Enum\LanguageCodeEnum;
use Components\Employee\Interfaces\EmployeeFullnameGuesserStrategyInterface;
use Components\Employee\Strategy\DefaultEmployeeFullnameGuesserStrategy;
use Components\Employee\Strategy\HungarianLanguageEmployeeFullnameGuesserStrategy;
use Components\Employee\Strategy\TCEEmployeeFullnameGuesserStrategy;

final class EmployeeFullnameFromModelGuesser
{
    private array $strategiesByCustomer;
    private array $strategiesByLanguage;
    private EmployeeFullnameGuesserStrategyInterface $defaultStrategy;

    public function __construct()
    {
        $this->strategiesByCustomer = [
            CustomerDBPatchNameEnum::TCE => new TCEEmployeeFullnameGuesserStrategy()
        ];
        $this->strategiesByLanguage = [
            LanguageCodeEnum::HU => new HungarianLanguageEmployeeFullnameGuesserStrategy()
        ];
        $this->defaultStrategy = new DefaultEmployeeFullnameGuesserStrategy();
    }

    public function guess(\Employee $employee): string
    {
        $customerDBPatchName = \Yang::getParam(ConfigParameterNameEnum::CUSTOMER_DB_PATCH_NAME);
        if (isset($this->strategiesByCustomer[$customerDBPatchName])) {
            return $this->strategiesByCustomer[$customerDBPatchName]->guess($employee);
        }

        $languageCode = \Dict::getLang();
        if (isset($this->strategiesByLanguage[$languageCode])) {
            return $this->strategiesByLanguage[$languageCode]->guess($employee);
        }

        return $this->defaultStrategy->guess($employee);
    }
}

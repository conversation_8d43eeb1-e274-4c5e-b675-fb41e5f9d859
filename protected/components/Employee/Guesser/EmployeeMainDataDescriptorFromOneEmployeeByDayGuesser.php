<?php

declare(strict_types=1);

namespace Components\Employee\Guesser;

use Components\Employee\Descriptor\EmployeeMainDataDescriptor;
use Components\WorkScheduleCore\Transformer\StringTransformer;

final class EmployeeMainDataDescriptorFromOneEmployeeByDayGuesser
{
    private StringTransformer $stringTransformer;

    public function __construct()
    {
        $this->stringTransformer = new StringTransformer();
    }

    /**
     * @param EmployeeMainDataDescriptor[] $employeeMainDataDescriptors
     * @throws \Exception
     */
    public function guess(array $employeeMainDataDescriptors, string $day): EmployeeMainDataDescriptor
    {
        $date = $this->stringTransformer->convertStringToDateTime($day.' 00:00:00');
        foreach ($employeeMainDataDescriptors as $employeeMainDataDescriptor) {
            if ($employeeMainDataDescriptor->getValIdFrom() <= $date &&
                $employeeMainDataDescriptor->getValIdTo() >= $date) {
                return $employeeMainDataDescriptor;
            }
        }
        $message = sprintf(
            "Employee not found on %s! employee contract id: %s",
            $day,
            $employeeMainDataDescriptors[0]->getEmployeeContractId()
        );
        throw new \Exception($message);
    }
}
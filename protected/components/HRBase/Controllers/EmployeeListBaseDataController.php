<?php
namespace Components\HRBase\Controllers;
\Yang::loadComponentNamespaces('Employee');
\Yang::loadComponentNamespaces('Cost');

use Grid2Controller;
use Components\Employee\Provider\EmployeeSalaryByContractIdsProvider;
use Components\Employee\Provider\EmployeeExtByEmployeeIdsProvider;
use Components\Cost\Provider\EmployeeCostByEmployeeContractIdsProvider;
use Components\Cost\Provider\CostByValidIntervalProvider;
use Components\Cost\Provider\CostCenterByValidIntervalProvider;
use Components\HRBase\Enum\OptionModelNames;

class EmployeeListBaseDataController extends Grid2Controller
{
    private $defaultEnd;
    private $providers = [];
    private $modelProviderMapping = [
        OptionModelNames::EMPLOYEE_COST_MODEL_NAME => EmployeeCostByEmployeeContractIdsProvider::class,
        OptionModelNames::EMPLOYEE_SALARY_MODEL_NAME => EmployeeSalaryByContractIdsProvider::class,
        OptionModelNames::EMPLOYEE_EXT_MODEL_NAME => EmployeeExtByEmployeeIdsProvider::class,
        OptionModelNames::EMPLOYEE_EXT_2_MODEL_NAME => EmployeeExtByEmployeeIdsProvider::class,
        OptionModelNames::EMPLOYEE_EXT_3_MODEL_NAME => EmployeeExtByEmployeeIdsProvider::class,
        OptionModelNames::EMPLOYEE_EXT_4_MODEL_NAME => EmployeeExtByEmployeeIdsProvider::class,
        OptionModelNames::EMPLOYEE_EXT_5_MODEL_NAME => EmployeeExtByEmployeeIdsProvider::class,
        OptionModelNames::EMPLOYEE_EXT_6_MODEL_NAME => EmployeeExtByEmployeeIdsProvider::class,
        OptionModelNames::EMPLOYEE_EXT_7_MODEL_NAME => EmployeeExtByEmployeeIdsProvider::class,
    ];
    private $extNumberMapping = [
        1 => '',
        2 => '2',
        3 => '3',
        4 => '4',
        5 => '5',
        6 => '6',
        7 => '7'
    ];
    
    public function __construct($controllerID, $modelConfig = [])
    {
        parent::__construct($controllerID);
        $this->defaultEnd = \App::getSetting("defaultEnd");
        $this->maxDays = 365;
        foreach ($modelConfig as $modelName) {
            $this->providers[$modelName] = new $this->modelProviderMapping[$modelName]();
        }
    }

    protected function G2BInit()
    {
        $this->LAGridRights->overrideInitRights("select",			true);
        $this->LAGridRights->overrideInitRights("paging",			true);
        $this->LAGridRights->overrideInitRights("search",			true);
        $this->LAGridRights->overrideInitRights("search_header",	true);
        $this->LAGridRights->overrideInitRights("select",			true);
        $this->LAGridRights->overrideInitRights("column_move",		true);
        $this->LAGridRights->overrideInitRights("col_sorting",		true);
        $this->LAGridRights->overrideInitRights("reload_sortings",	true);
        $this->LAGridRights->overrideInitRights("init_open_search",	true);
        $this->LAGridRights->overrideInitRights("export_xlsx",		true);

        $this->LAGridDB->enableArrMode();
        parent::G2BInit();
    }

    public function search()
    {
        return $this->getPreDefinedSearchFromDb("employeeManagement");
    }
    
    protected function dataArray($gridID, $filter)
    {
        $controllerID = $this->getControllerID();
        $filter["interval"]["valid_from"] = $filter["valid_from"];
        $filter["interval"]["valid_to"] = $filter["valid_to"];
        $validFromDateTime = formatStringToDateTime($filter["valid_from"]);
        $validToDateTime = formatStringToDateTime($filter["valid_to"]);

        $result = [];
        $gae = new \GetActiveEmployees(
            $filter,
            "employeeManagement",
            $controllerID,
            NULL,
            FALSE,
            TRUE,
            FALSE,
            '',
            ', employee.valid_from as emp_valid_from, 
            employee.valid_to as emp_valid_to, 
            employee_contract.valid_from as contract_valid_from, 
            employee_contract.valid_to as contract_valid_to, 
            employee.gender,
            employee_contract.employee_contract_number,
            employee_contract.employee_contract_type,
            employee_contract.wage_type,
            employee_contract.daily_worktime,
            employee_contract.ec_valid_from, 
            employee_contract.ec_valid_to,
            employee.nameofbirth,
            employee.tax_number,
            company.company_name,
            payroll.payroll_name,
            unit.unit_name
            '
        );
        $activeEmployees = $gae->getEmployees($filter);
        $employeeIds = array_column($activeEmployees, 'employee_id');
        $employeeContractIds = array_column($activeEmployees, 'employee_contract_id');
        $costTableData = (new CostByValidIntervalProvider())($validFromDateTime->format('Y-m-d'), $validToDateTime->format('Y-m-d'));
        $costCenterTableData = (new CostCenterByValidIntervalProvider())($validFromDateTime->format('Y-m-d'), $validToDateTime->format('Y-m-d'));
        $genderLookup = \AppLookup::getAppLookUpDictionaryValues('gender');
        
        $providerData = [];
        foreach ($this->extNumberMapping as $number => $extNumber) {
            if ($this->providers[OptionModelNames::EMPLOYEE_EXT_MODEL_NAME . $extNumber]) {
                $this->getExtModelsData($extNumber, $providerData, $employeeIds, OptionModelNames::EMPLOYEE_EXT_MODEL_NAME . $extNumber, $validFromDateTime, $validToDateTime);
            }
        }
        
        if ($this->providers[OptionModelNames::EMPLOYEE_COST_MODEL_NAME]) {
            $this->getCostModelData($providerData, $employeeContractIds);
        }
        
        if ($this->providers[OptionModelNames::EMPLOYEE_SALARY_MODEL_NAME]) {
            $this->getSalaryModelData($providerData, $employeeContractIds, $validFromDateTime, $validToDateTime);
        }
        
        foreach ($activeEmployees as $key => $employee) {
            $result[$key] = $employee;
            $result[$key]['gender'] = $genderLookup[$employee['gender']];
            
            foreach ($providerData[OptionModelNames::EMPLOYEE_EXT_MODEL_NAME][$employee['employee_id']] as $employeeExt) {
                $this->addExtDataToGrid($result[$key], $employeeExt, $employee);
            }
            
            foreach ($providerData[OptionModelNames::EMPLOYEE_EXT_2_MODEL_NAME][$employee['employee_id']] as $employeeExt2) {
                $this->addExt2DataToGrid($result[$key], $employeeExt2, $employee);
            }
            
            foreach ($providerData[OptionModelNames::EMPLOYEE_EXT_3_MODEL_NAME][$employee['employee_id']] as $employeeExt3) {
                $this->addExt3DataToGrid($result[$key], $employeeExt3, $employee);
            }
            
            foreach ($providerData[OptionModelNames::EMPLOYEE_EXT_4_MODEL_NAME][$employee['employee_id']] as $employeeExt4) {
                $this->addExt4DataToGrid($result[$key], $employeeExt4, $employee);
            }
            
            foreach ($providerData[OptionModelNames::EMPLOYEE_EXT_5_MODEL_NAME][$employee['employee_id']] as $employeeExt5) {
                $this->addExt5DataToGrid($result[$key], $employeeExt5, $employee);
            }
            
            foreach ($providerData[OptionModelNames::EMPLOYEE_EXT_6_MODEL_NAME][$employee['employee_id']] as $employeeExt6) {
                $this->addExt6DataToGrid($result[$key], $employeeExt6, $employee);
            }

            foreach ($providerData[OptionModelNames::EMPLOYEE_EXT_7_MODEL_NAME][$employee['employee_id']] as $employeeExt7) {
                $this->addExt7DataToGrid($result[$key], $employeeExt7, $employee);
            }
            
            foreach ($providerData[OptionModelNames::EMPLOYEE_SALARY_MODEL_NAME][$employee['employee_contract_id']] as $employeeSalary) {
                $this->addSalaryDataToGrid($result[$key], $employeeSalary, $employee);
            }

            foreach ($providerData[OptionModelNames::EMPLOYEE_COST_MODEL_NAME][$employee['employee_contract_id']] as $employeeCost) {
                $this->addCostDataToGrid($result[$key], $employeeCost, $employee, $costTableData, $costCenterTableData);
            }
        }
        
        return $result;
    }

    /**
     * @param array $providerData
     * @param string $param
     * @param array $employeeIds
     * @param string $modelName
     * @param \DateTime|null $validFrom
     * @param \DateTime|null $validTo
     * @return void
     */
    public function getExtModelsData(string $extNumber, array &$providerData, array $employeeIds, string $modelName, \DateTime $validFrom = null, \DateTime $validTo = null)
    {
        $employeeExtModels = ($this->providers[$modelName])($extNumber, $employeeIds, $validFrom, $validTo);
        foreach ($employeeExtModels as $employeeExt) {
            $providerData[$modelName][$employeeExt->employee_id][] = $employeeExt;
        }
    }

    /**
     * @param array $providerData
     * @param array $employeeContractIds
     * @param \DateTime|null $validFrom
     * @param \DateTime|null $validTo
     * @return void
     */
    private function getCostModelData(array &$providerData, array $employeeContractIds)
    {
        $employeeCostModels = ($this->providers[OptionModelNames::EMPLOYEE_COST_MODEL_NAME])($employeeContractIds);
        foreach ($employeeCostModels as $employeeCost) {
            $providerData[OptionModelNames::EMPLOYEE_COST_MODEL_NAME][$employeeCost->employee_contract_id][] = $employeeCost;
        }
    }
    
    /**
     * @param array $providerData
     * @param array $employeeContractIds
     * @param \DateTime|null $validFrom
     * @param \DateTime|null $validTo
     * @return void
     */
    private function getSalaryModelData(array &$providerData, array $employeeContractIds, \DateTime $validFrom = null, \DateTime $validTo = null)
    {
        $employeeSalaryModels = ($this->providers[OptionModelNames::EMPLOYEE_SALARY_MODEL_NAME])($employeeContractIds, $validFrom, $validTo);
        foreach ($employeeSalaryModels as $employeeSalary) {
            $providerData[OptionModelNames::EMPLOYEE_SALARY_MODEL_NAME][$employeeSalary->employee_contract_id][] = $employeeSalary;
        }
    }

    /**
     * @param array $gridRow
     * @param \EmployeeExt $employeeExt
     * @param array $employee
     * @return void
     */
    private function addExtDataToGrid(array &$gridRow, \EmployeeExt $employeeExt, array $employee)
    {
        if (!$this->isBetweenEmployeeValidFromValidTo($employeeExt, $employee)) {
            return;
        }

        $gridRow['place_of_birth'] = $employeeExt->place_of_birth;
        $gridRow['date_of_birth'] = $employeeExt->date_of_birth;
        $gridRow['mothers_name'] = $employeeExt->mothers_name;
        $gridRow['ssn'] = $employeeExt->ssn;
        $gridRow['personal_id_card_number'] = $employeeExt->personal_id_card_number;
        $gridRow['passport_number'] = $employeeExt->passport_number;
        $gridRow['option1'] = $employeeExt->option1;
        $gridRow['option2'] = $employeeExt->option2;
        $gridRow['option3'] = $employeeExt->option3;
        $gridRow['option4'] = $employeeExt->option4;
        $gridRow['option5'] = $employeeExt->option5;
        $gridRow['option6'] = $employeeExt->option6;
        $gridRow['option7'] = $employeeExt->option7;
        $gridRow['option8'] = $employeeExt->option8;
        $gridRow['option9'] = $employeeExt->option9;
        $gridRow['option10'] = $employeeExt->option10;
        $gridRow['option11'] = $employeeExt->option11;
        $gridRow['option12'] = $employeeExt->option12;
        $gridRow['option13'] = $employeeExt->option13;
        $gridRow['option14'] = $employeeExt->option14;
        $gridRow['option15'] = $employeeExt->option15;
        $gridRow['option16'] = $employeeExt->option16;
        $gridRow['option17'] = $employeeExt->option17;
        $gridRow['option18'] = $employeeExt->option18;
        $gridRow['option19'] = $employeeExt->option19;
        $gridRow['option20'] = $employeeExt->option20;
        $gridRow['option21'] = $employeeExt->option21;
        $gridRow['option22'] = $employeeExt->option22;
        $gridRow['option23'] = $employeeExt->option23;
        $gridRow['option24'] = $employeeExt->option24;
        $gridRow['option25'] = $employeeExt->option25;
        $gridRow['option26'] = $employeeExt->option26;
        $gridRow['option27'] = $employeeExt->option27;
        $gridRow['option28'] = $employeeExt->option28;
        $gridRow['option29'] = $employeeExt->option29;
        $gridRow['option30'] = $employeeExt->option30;
        $gridRow['option31'] = $employeeExt->option31;
        $gridRow['option32'] = $employeeExt->option32;
        $gridRow['option33'] = $employeeExt->option33;
        $gridRow['option34'] = $employeeExt->option34;
        $gridRow['note'] = $employeeExt->note;
    }

    /**
     * @param array $gridRow
     * @param \EmployeeExt2 $employeeExt2
     * @param array $employee
     * @return void
     */
    private function addExt2DataToGrid(array &$gridRow, \EmployeeExt2 $employeeExt2, array $employee)
    {
        if (!$this->isBetweenEmployeeValidFromValidTo($employeeExt2, $employee)) {
            return;
        }
        
        $gridRow['ext2_option1'] = $employeeExt2->ext2_option1;
        $gridRow['ext2_option2'] = $employeeExt2->ext2_option2;
        $gridRow['ext2_option3'] = $employeeExt2->ext2_option3;
        $gridRow['ext2_option4'] = $employeeExt2->ext2_option4;
        $gridRow['ext2_option5'] = $employeeExt2->ext2_option5;
        $gridRow['ext2_option6'] = $employeeExt2->ext2_option6;
        $gridRow['ext2_option7'] = $employeeExt2->ext2_option7;
        $gridRow['ext2_option8'] = $employeeExt2->ext2_option8;
        $gridRow['ext2_option9'] = $employeeExt2->ext2_option9;
        $gridRow['ext2_option10'] = $employeeExt2->ext2_option10;
        $gridRow['ext2_option11'] = $employeeExt2->ext2_option11;
        $gridRow['ext2_option12'] = $employeeExt2->ext2_option12;
        $gridRow['ext2_option13'] = $employeeExt2->ext2_option13;
        $gridRow['ext2_option14'] = $employeeExt2->ext2_option14;
        $gridRow['ext2_option15'] = $employeeExt2->ext2_option15;
        $gridRow['ext2_option16'] = $employeeExt2->ext2_option16;
        $gridRow['ext2_option17'] = $employeeExt2->ext2_option17;
        $gridRow['ext2_option18'] = $employeeExt2->ext2_option18;
        $gridRow['ext2_option19'] = $employeeExt2->ext2_option19;
        $gridRow['ext2_option20'] = $employeeExt2->ext2_option20;
        $gridRow['ext2_option21'] = $employeeExt2->ext2_option21;
        $gridRow['ext2_option22'] = $employeeExt2->ext2_option22;
        $gridRow['ext2_option23'] = $employeeExt2->ext2_option23;
        $gridRow['ext2_option24'] = $employeeExt2->ext2_option24;
        $gridRow['ext2_option25'] = $employeeExt2->ext2_option25;
        $gridRow['ext2_option26'] = $employeeExt2->ext2_option26;
        $gridRow['ext2_option27'] = $employeeExt2->ext2_option27;
        $gridRow['ext2_option28'] = $employeeExt2->ext2_option28;
        $gridRow['ext2_option29'] = $employeeExt2->ext2_option29;
        $gridRow['ext2_option30'] = $employeeExt2->ext2_option30;
        $gridRow['ext2_option31'] = $employeeExt2->ext2_option31;
        $gridRow['ext2_option32'] = $employeeExt2->ext2_option32;
        $gridRow['ext2_option33'] = $employeeExt2->ext2_option33;
        $gridRow['ext2_option34'] = $employeeExt2->ext2_option34;
        $gridRow['ext2_option35'] = $employeeExt2->ext2_option35;
        $gridRow['ext2_option36'] = $employeeExt2->ext2_option36;
        $gridRow['ext2_option37'] = $employeeExt2->ext2_option37;
        $gridRow['ext2_option38'] = $employeeExt2->ext2_option38;
        $gridRow['ext2_option39'] = $employeeExt2->ext2_option39;
        $gridRow['ext2_option40'] = $employeeExt2->ext2_option40;
        $gridRow['ext2_option41'] = $employeeExt2->ext2_option41;
        $gridRow['ext2_option42'] = $employeeExt2->ext2_option42;
        $gridRow['ext2_option43'] = $employeeExt2->ext2_option43;
        $gridRow['ext2_option44'] = $employeeExt2->ext2_option44;
        $gridRow['ext2_option45'] = $employeeExt2->ext2_option45;
        $gridRow['ext2_option46'] = $employeeExt2->ext2_option46;
    }

    private function addExt3DataToGrid(array &$gridRow, \EmployeeExt3 $employeeExt3, array $employee)
    {
        if (!$this->isBetweenEmployeeValidFromValidTo($employeeExt3, $employee)) {
            return;
        }

        $gridRow['ext3_option1'] = $employeeExt3->ext3_option1;
        $gridRow['ext3_option2'] = $employeeExt3->ext3_option2;
        $gridRow['ext3_option3'] = $employeeExt3->ext3_option3;
        $gridRow['ext3_option4'] = $employeeExt3->ext3_option4;
        $gridRow['ext3_option5'] = $employeeExt3->ext3_option5;
        $gridRow['ext3_option6'] = $employeeExt3->ext3_option6;
        $gridRow['ext3_option7'] = $employeeExt3->ext3_option7;
        $gridRow['ext3_option8'] = $employeeExt3->ext3_option8;
        $gridRow['ext3_option9'] = $employeeExt3->ext3_option9;
        $gridRow['ext3_option10'] = $employeeExt3->ext3_option10;
        $gridRow['ext3_option11'] = $employeeExt3->ext3_option11;
        $gridRow['ext3_option12'] = $employeeExt3->ext3_option12;
        $gridRow['ext3_option13'] = $employeeExt3->ext3_option13;
        $gridRow['ext3_option14'] = $employeeExt3->ext3_option14;
        $gridRow['ext3_option15'] = $employeeExt3->ext3_option15;
        $gridRow['ext3_option16'] = $employeeExt3->ext3_option16;
        $gridRow['ext3_option17'] = $employeeExt3->ext3_option17;
        $gridRow['ext3_option18'] = $employeeExt3->ext3_option18;
        $gridRow['ext3_option19'] = $employeeExt3->ext3_option19;
        $gridRow['ext3_option20'] = $employeeExt3->ext3_option20;        
    }

    private function addExt4DataToGrid(array &$gridRow, \EmployeeExt4 $employeeExt4, array $employee)
    {
        if (!$this->isBetweenEmployeeValidFromValidTo($employeeExt4, $employee)) {
            return;
        }
        
        $gridRow['ext4_option1'] = $employeeExt4->ext4_option1;
        $gridRow['ext4_option2'] = $employeeExt4->ext4_option2;
        $gridRow['ext4_option3'] = $employeeExt4->ext4_option3;
        $gridRow['ext4_option4'] = $employeeExt4->ext4_option4;
        $gridRow['ext4_option5'] = $employeeExt4->ext4_option5;
        $gridRow['ext4_option6'] = $employeeExt4->ext4_option6;
        $gridRow['ext4_option7'] = $employeeExt4->ext4_option7;
        $gridRow['ext4_option8'] = $employeeExt4->ext4_option8;
        $gridRow['ext4_option9'] = $employeeExt4->ext4_option9;
        $gridRow['ext4_option10'] = $employeeExt4->ext4_option10;
        $gridRow['ext4_option11'] = $employeeExt4->ext4_option11;
        $gridRow['ext4_option12'] = $employeeExt4->ext4_option12;
        $gridRow['ext4_option13'] = $employeeExt4->ext4_option13;
        $gridRow['ext4_option14'] = $employeeExt4->ext4_option14;
        $gridRow['ext4_option15'] = $employeeExt4->ext4_option15;
        $gridRow['ext4_option16'] = $employeeExt4->ext4_option16;
        $gridRow['ext4_option17'] = $employeeExt4->ext4_option17;
        $gridRow['ext4_option18'] = $employeeExt4->ext4_option18;
        $gridRow['ext4_option19'] = $employeeExt4->ext4_option19;
        $gridRow['ext4_option20'] = $employeeExt4->ext4_option20;
        $gridRow['ext4_option21'] = $employeeExt4->ext4_option21;
        $gridRow['ext4_option22'] = $employeeExt4->ext4_option22;
        $gridRow['ext4_option23'] = $employeeExt4->ext4_option23;
        $gridRow['ext4_option24'] = $employeeExt4->ext4_option24;
    }

    private function addExt5DataToGrid(array &$gridRow, \EmployeeExt5 $employeeExt5, array $employee)
    {
        if (!$this->isBetweenEmployeeValidFromValidTo($employeeExt5, $employee)) {
            return;
        }
        
        $gridRow['ext5_option1'] = $employeeExt5->ext5_option1;
        $gridRow['ext5_option2'] = $employeeExt5->ext5_option2;
        $gridRow['ext5_option3'] = $employeeExt5->ext5_option3;
        $gridRow['ext5_option4'] = $employeeExt5->ext5_option4;
        $gridRow['ext5_option5'] = $employeeExt5->ext5_option5;
        $gridRow['ext5_option6'] = $employeeExt5->ext5_option6;
        $gridRow['ext5_option7'] = $employeeExt5->ext5_option7;
        $gridRow['ext5_option8'] = $employeeExt5->ext5_option8;
        $gridRow['ext5_option9'] = $employeeExt5->ext5_option9;
        $gridRow['ext5_option10'] = $employeeExt5->ext5_option10;
        $gridRow['ext5_option11'] = $employeeExt5->ext5_option11;
        $gridRow['ext5_option12'] = $employeeExt5->ext5_option12;
        $gridRow['ext5_option13'] = $employeeExt5->ext5_option13;
        $gridRow['ext5_option14'] = $employeeExt5->ext5_option14;
        $gridRow['ext5_option15'] = $employeeExt5->ext5_option15;
        $gridRow['ext5_option16'] = $employeeExt5->ext5_option16;
        $gridRow['ext5_option17'] = $employeeExt5->ext5_option17;
        $gridRow['ext5_option18'] = $employeeExt5->ext5_option18;
        $gridRow['ext5_option19'] = $employeeExt5->ext5_option19;
        $gridRow['ext5_option20'] = $employeeExt5->ext5_option20;
        $gridRow['ext5_option21'] = $employeeExt5->ext5_option21;
        $gridRow['ext5_option22'] = $employeeExt5->ext5_option22;
        $gridRow['ext5_option23'] = $employeeExt5->ext5_option23;
        $gridRow['ext5_option24'] = $employeeExt5->ext5_option24;
        $gridRow['ext5_option25'] = $employeeExt5->ext5_option25;
        $gridRow['ext5_option26'] = $employeeExt5->ext5_option26;
        $gridRow['ext5_option27'] = $employeeExt5->ext5_option27;
        $gridRow['ext5_option28'] = $employeeExt5->ext5_option28;
        $gridRow['ext5_option29'] = $employeeExt5->ext5_option29;
        $gridRow['ext5_option30'] = $employeeExt5->ext5_option30;
        $gridRow['ext5_option31'] = $employeeExt5->ext5_option31;
        $gridRow['ext5_option32'] = $employeeExt5->ext5_option32;
        $gridRow['ext5_option33'] = $employeeExt5->ext5_option33;
        $gridRow['ext5_option34'] = $employeeExt5->ext5_option34;
        $gridRow['ext5_option35'] = $employeeExt5->ext5_option35;
        $gridRow['ext5_option36'] = $employeeExt5->ext5_option36;
        $gridRow['ext5_option37'] = $employeeExt5->ext5_option37;
        $gridRow['ext5_option38'] = $employeeExt5->ext5_option38;
        $gridRow['ext5_option39'] = $employeeExt5->ext5_option39;
        $gridRow['ext5_option40'] = $employeeExt5->ext5_option40;
        $gridRow['ext5_option41'] = $employeeExt5->ext5_option41;
        $gridRow['ext5_option42'] = $employeeExt5->ext5_option42;
    }

    private function addExt6DataToGrid(array &$gridRow, \EmployeeExt6 $employeeExt6, array $employee)
    {
        if (!$this->isBetweenEmployeeValidFromValidTo($employeeExt6, $employee)) {
            return;
        }
        
        $gridRow['ext6_option1'] = $employeeExt6->ext6_option1;
        $gridRow['ext6_option2'] = $employeeExt6->ext6_option2;
        $gridRow['ext6_option3'] = $employeeExt6->ext6_option3;
        $gridRow['ext6_option4'] = $employeeExt6->ext6_option4;
        $gridRow['ext6_option5'] = $employeeExt6->ext6_option5;
        $gridRow['ext6_option6'] = $employeeExt6->ext6_option6;
        $gridRow['ext6_option7'] = $employeeExt6->ext6_option7;
        $gridRow['ext6_option8'] = $employeeExt6->ext6_option8;
    }

    private function addExt7DataToGrid(array &$gridRow, \EmployeeExt7 $employeeExt7, array $employee)
    {
        if (!$this->isBetweenEmployeeValidFromValidTo($employeeExt7, $employee)) {
            return;
        }
        
        $gridRow['ext7_option1'] = $employeeExt7->ext7_option1;
        $gridRow['ext7_option2'] = $employeeExt7->ext7_option2;
        $gridRow['ext7_option3'] = $employeeExt7->ext7_option8;
    }

    /**
     * @param array $gridRow
     * @param \EmployeeSalary $employeeSalary
     * @param array $employee
     * @return void
     */
    private function addSalaryDataToGrid(array &$gridRow,\EmployeeSalary $employeeSalary, array $employee)
    {
        if (!$this->isBetweenContractValidFromValidTo($employeeSalary, $employee)) {
            return;
        }

        $gridRow['personal_month_salary'] = $employeeSalary->personal_month_salary;
        $gridRow['personal_hour_salary'] = $employeeSalary->personal_hour_salary;
        $gridRow['shift'] = $employeeSalary->shift;
        $gridRow['shift_bonus_in_percent'] = $employeeSalary->shift_bonus_in_percent;
        $gridRow['es_option1'] = $employeeSalary->es_option1;
        $gridRow['es_option2'] = $employeeSalary->es_option2;
        $gridRow['es_option3'] = $employeeSalary->es_option3;
        $gridRow['es_option4'] = $employeeSalary->es_option4;
        $gridRow['es_option5'] = $employeeSalary->es_option5;
        $gridRow['es_option6'] = $employeeSalary->es_option6;
        $gridRow['es_option7'] = $employeeSalary->es_option7;
        $gridRow['es_option8'] = $employeeSalary->es_option8;
    }
    
    /**
     * @param array $gridRow
     * @param \EmployeeCost $employeeCost
     * @param array $employee
     * @param array $costTableData
     * @return void
     */
    private function addCostDataToGrid(array &$gridRow,\EmployeeCost $employeeCost, array $employee, array $costTableData, array $costCenterTableData)
    {
        if (!$this->isBetweenContractValidFromValidTo($employeeCost, $employee)) {
            return;
        }
        
        $gridRow['cost_id'] = $employeeCost->cost_id;
        $gridRow['cost_name'] = $costTableData[$employeeCost->cost_id] ?? '';
        $gridRow['cost_center_id'] = $employeeCost->cost_center_id;
        $gridRow['cost_center_name'] = $costCenterTableData[$employeeCost->cost_center_id] ?? '';
    }
    
    /**
     * @param $model
     * @param array $employee
     * @return bool
     */
    public function isBetweenEmployeeValidFromValidTo($model, array $employee): bool
    {
        return (($model->valid_to ?? $this->defaultEnd) >= $employee['valid_from'] &&
            $model->valid_from <= ($employee['valid_to'] ?? $this->defaultEnd));
    }

    /**
     * @param $model
     * @param array $employee
     * @return bool
     */
    public function isBetweenContractValidFromValidTo($model, array $employee): bool
    {
        return (($model->valid_to ?? $this->defaultEnd) >= $employee['contract_valid_from'] &&
            $model->valid_from <= ($employee['contract_valid_to'] ?? $this->defaultEnd) &&
            ($model->valid_to ?? $this->defaultEnd) >= $employee['ec_valid_from'] &&
            $model->valid_from <= ($employee['ec_valid_to'] ?? $this->defaultEnd));
    }

    public function columns()
    {
        $columns = [
            'emp_id'                    => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            'fullname'		            => ['width'=>200, 'col_type'=>'ed', 'align' => 'left'],
            'company_name'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'left'],
            'payroll_name'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            'nameofbirth'               => ['width'=>200, 'col_type'=>'ed', 'align' => 'left'],
            'gender'                    => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            'tax_number'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
        ];
        
        if (isset($this->providers[OptionModelNames::EMPLOYEE_SALARY_MODEL_NAME])) {
            $columns = array_merge($columns, [
                'personal_month_salary'     => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'personal_hour_salary'      => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'shift'                     => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'shift_bonus_in_percent'    => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option1'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option2'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option3'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option4'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option5'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option6'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option7'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option8'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option9'                => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'es_option10'               => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            ]);
        }
        
        $columns = array_merge($columns, [
            'ec_valid_from'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            'ec_valid_to'               => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            'employee_contract_number'  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            'employee_contract_type'    => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            'wage_type'                 => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            'daily_worktime'            => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
        ]);
        
        if (isset($this->providers[OptionModelNames::EMPLOYEE_EXT_MODEL_NAME])) {
            $columns = array_merge($columns, [
                'place_of_birth'            => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'date_of_birth'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'mothers_name'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'left'],
                'ssn'                       => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option1'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option2'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option3'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option4'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option5'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option6'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option7'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option8'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option9'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option10'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option11'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option12'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option13'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option14'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option15'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option16'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option17'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option18'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option19'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option20'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option21'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                ]);
        }
        
        if (isset($this->providers[OptionModelNames::EMPLOYEE_COST_MODEL_NAME])) {
            $columns = array_merge($columns, [
                'cost_id'                   => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'cost_name'                 => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'cost_center_id'            => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'cost_center_name'          => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            ]);
        }
        
         if (isset($this->providers[OptionModelNames::EMPLOYEE_EXT_MODEL_NAME])) {
            $columns = array_merge($columns, [
                'option24'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option25'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option26'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option27'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option28'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option29'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option30'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option31'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option32'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option33'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'option34'                  => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'note'                      => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
            ]);
         }
        
        if (isset($this->providers[OptionModelNames::EMPLOYEE_EXT_2_MODEL_NAME])) {
            $columns = array_merge($columns, [
                'ext2_option1'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option2'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option3'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option4'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option5'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option6'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option7'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option8'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option9'              => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option10'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option11'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option12'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option13'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option14'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option15'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option16'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option17'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option18'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option19'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option20'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option21'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option22'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option23'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option24'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option25'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option26'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option27'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option28'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option29'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option30'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option31'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option32'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option33'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option34'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option35'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option36'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option37'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option38'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option39'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option40'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option41'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option42'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option43'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option44'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option45'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'],
                'ext2_option46'             => ['width'=>200, 'col_type'=>'ed', 'align' => 'center'], 
            ]);
        }
        
        if (isset($this->providers[OptionModelNames::EMPLOYEE_EXT_3_MODEL_NAME])) {
            $columns = array_merge($columns, [
               'ext3_option1' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option2' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option3' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option4' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option5' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option6' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option7' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option8' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option9' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option10' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option11' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option12' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option13' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option14' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option15' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option16' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option17' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option18' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option19' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext3_option20' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
            ]);
        }
           
        if (isset($this->providers[OptionModelNames::EMPLOYEE_EXT_4_MODEL_NAME])) {
            $columns = array_merge($columns, [
               'ext4_option1' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option2' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option3' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option4' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option5' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option6' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option7' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option8' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option9' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option10' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option11' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option12' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option13' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option14' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option15' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option16' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option17' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option18' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option19' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option20' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option21' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option22' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option23' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
               'ext4_option24' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
            ]);
        }
        
        if (isset($this->providers[OptionModelNames::EMPLOYEE_EXT_5_MODEL_NAME])) {
            $columns = array_merge($columns, [
                'ext5_option1' => ['width' => 200, 'col_type' => 'ed', 'align' => 'left'],
                'ext5_option2' => ['width' => 200, 'col_type' => 'ed', 'align' => 'left'],
                'ext5_option3' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option4' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option5' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option6' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option7' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option8' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option9' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option10' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option11' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option12' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option13' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option14' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option15' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option16' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option17' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option18' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option19' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option20' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option21' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option22' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option23' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option24' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option25' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option26' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option27' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option28' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option29' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option30' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option31' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option32' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option33' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option34' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option35' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option36' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option37' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option38' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option39' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option40' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option41' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext5_option42' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
            ]);
        }
        
        if (isset($this->providers[OptionModelNames::EMPLOYEE_EXT_6_MODEL_NAME])) {
            $columns = array_merge($columns, [
                'ext6_option1' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext6_option2' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext6_option3' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext6_option4' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext6_option5' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext6_option6' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext6_option7' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext6_option8' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
            ]);
        }

        if (isset($this->providers[OptionModelNames::EMPLOYEE_EXT_7_MODEL_NAME])) {
            $columns = array_merge($columns, [
                'ext7_option1' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext7_option2' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
                'ext7_option3' => ['width' => 200, 'col_type' => 'ed', 'align' => 'center'],
            ]);
        }
        
        $columns = array_merge($columns,[
            'unit_name' => ['width' => 200, 'col_type' => 'ed', 'align' => 'left'], 
        ]);
        
        return $columns;
    }

    public function attributeLabels()
    {
        return [
            'emp_id'		           => \Dict::getValue("emp_id"),
            'fullname'		           => \Dict::getValue("employee_name"),
            'company_name'             => \Dict::getValue("company_name"),
            'payroll_name'             => \Dict::getValue("payroll_name"),
            'nameofbirth'              => \Dict::getValue("nameofbirth"),
            'gender'                   => \Dict::getValue("gender"), 
            'tax_number'               => \Dict::getValue("tax_number"),
            'personal_month_salary'    => \Dict::getValue("personal_month_salary"),
            'personal_hour_salary'     => \Dict::getValue("personal_hour_salary"),
            'shift'                    => \Dict::getValue("shift"),
            'shift_bonus_in_percent'   => \Dict::getValue("shift_bonus_in_percent"),
            'es_option1'               => \Dict::getValue("es_option1"),
            'es_option2'               => \Dict::getValue("es_option2"),
            'es_option3'               => \Dict::getValue("es_option3"),
            'es_option4'               => \Dict::getValue("es_option4"),
            'es_option5'               => \Dict::getValue("es_option5"),
            'es_option6'               => \Dict::getValue("es_option6"),
            'es_option7'               => \Dict::getValue("es_option7"),
            'es_option8'               => \Dict::getValue("es_option8"),
            'es_option9'               => \Dict::getValue('es_option9'),
            'es_option10'              => \Dict::getValue('es_option10'),
            'ec_valid_from'            => \Dict::getValue("ec_valid_from"),
            'ec_valid_to'              => \Dict::getValue("ec_valid_to"),
            'employee_contract_number' => \Dict::getValue("employee_contract_number"),
            'employee_contract_type'   => \Dict::getValue("employee_contract_type"),
            'wage_type'                => \Dict::getValue("wage_type"),
            'daily_worktime'           => \Dict::getValue("daily_worktime"),
            'place_of_birth'           => \Dict::getValue("place_of_birth"),
            'date_of_birth'            => \Dict::getValue("date_of_birth"),
            'mothers_name'             => \Dict::getValue("mothers_name"),
            'ssn'                      => \Dict::getValue("ssn"),
            'option1'                  => \Dict::getValue("option1"),
            'option2'                  => \Dict::getValue("option2"),
            'option3'                  => \Dict::getValue("option3"),
            'option4'                  => \Dict::getValue("option4"),
            'option5'                  => \Dict::getValue("option5"),
            'option6'                  => \Dict::getValue("option6"),
            'option7'                  => \Dict::getValue("option7"),
            'option8'                  => \Dict::getValue("option8"),
            'option9'                  => \Dict::getValue("option9"),
            'option10'                 => \Dict::getValue("option10"),
            'option11'                 => \Dict::getValue("option11"),
            'option12'                 => \Dict::getValue("option12"),
            'option13'                 => \Dict::getValue("option13"),
            'option14'                 => \Dict::getValue("option14"),
            'option15'                 => \Dict::getValue("option15"),
            'option16'                 => \Dict::getValue("option16"),
            'option17'                 => \Dict::getValue("option17"),
            'option18'                 => \Dict::getValue("option18"),
            'option19'                 => \Dict::getValue("option19"),
            'option20'                 => \Dict::getValue("option20"),
            'option21'                 => \Dict::getValue("option21"),
            'cost_id'                  => \Dict::getValue("cost_id"),
            'cost_name'                => \Dict::getValue("cost_name"),
            'cost_center_id'           => \Dict::getValue("cost_center_id"),
            'cost_center_name'         => \Dict::getValue("cost_center_name"),
            'option24'                 => \Dict::getValue("option24"),
            'option25'                 => \Dict::getValue("option25"),
            'option26'                 => \Dict::getValue("option26"),
            'option27'                 => \Dict::getValue("option27"),
            'option28'                 => \Dict::getValue("option28"),
            'option29'                 => \Dict::getValue("option29"),
            'option30'                 => \Dict::getValue("option30"),
            'option31'                 => \Dict::getValue("option31"),
            'option32'                 => \Dict::getValue("option32"),
            'option33'                 => \Dict::getValue("option33"),
            'option34'                 => \Dict::getValue("option34"),
            'note'                     => \Dict::getValue("note"),
            'ext2_option1'             => \Dict::getValue("ext2_option1"),
            'ext2_option2'             => \Dict::getValue("ext2_option2"),
            'ext2_option3'             => \Dict::getValue("ext2_option3"),
            'ext2_option4'             => \Dict::getValue("ext2_option4"),
            'ext2_option5'             => \Dict::getValue("ext2_option5"),
            'ext2_option6'             => \Dict::getValue("ext2_option6"),
            'ext2_option7'             => \Dict::getValue("ext2_option7"),
            'ext2_option8'             => \Dict::getValue("ext2_option8"),
            'ext2_option9'             => \Dict::getValue("ext2_option9"),
            'ext2_option10'            => \Dict::getValue("ext2_option10"),
            'ext2_option11'            => \Dict::getValue("ext2_option11"),
            'ext2_option12'            => \Dict::getValue("ext2_option12"),
            'ext2_option13'            => \Dict::getValue("ext2_option13"),
            'ext2_option14'            => \Dict::getValue("ext2_option14"),
            'ext2_option15'            => \Dict::getValue("ext2_option15"),
            'ext2_option16'            => \Dict::getValue("ext2_option16"),
            'ext2_option17'            => \Dict::getValue("ext2_option17"),
            'ext2_option18'            => \Dict::getValue("ext2_option18"),
            'ext2_option19'            => \Dict::getValue("ext2_option19"),
            'ext2_option20'            => \Dict::getValue("ext2_option20"),
            'ext2_option21'            => \Dict::getValue("ext2_option21"),
            'ext2_option22'            => \Dict::getValue("ext2_option22"),
            'ext2_option23'            => \Dict::getValue("ext2_option23"),
            'ext2_option24'            => \Dict::getValue("ext2_option24"),
            'ext2_option25'            => \Dict::getValue("ext2_option25"),
            'ext2_option26'            => \Dict::getValue("ext2_option26"),
            'ext2_option27'            => \Dict::getValue("ext2_option27"),
            'ext2_option28'            => \Dict::getValue("ext2_option28"),
            'ext2_option29'            => \Dict::getValue("ext2_option29"),
            'ext2_option30'            => \Dict::getValue("ext2_option30"),
            'ext2_option31'            => \Dict::getValue("ext2_option31"),
            'ext2_option32'            => \Dict::getValue("ext2_option32"),
            'ext2_option33'            => \Dict::getValue("ext2_option33"),
            'ext2_option34'            => \Dict::getValue("ext2_option34"),
            'ext2_option35'            => \Dict::getValue("ext2_option35"),
            'ext2_option36'            => \Dict::getValue("ext2_option36"),
            'ext2_option37'            => \Dict::getValue("ext2_option37"),
            'ext2_option38'            => \Dict::getValue("ext2_option38"),
            'ext2_option39'            => \Dict::getValue("ext2_option39"),
            'ext2_option40'            => \Dict::getValue("ext2_option40"),
            'ext2_option41'            => \Dict::getValue("ext2_option41"),
            'ext2_option42'            => \Dict::getValue("ext2_option42"),
            'ext2_option43'            => \Dict::getValue("ext2_option43"),
            'ext2_option44'            => \Dict::getValue("ext2_option44"),
            'ext2_option45'            => \Dict::getValue("ext2_option45"),
            'ext2_option46'            => \Dict::getValue("ext2_option46"),
            'ext3_option1'             => \Dict::getValue("ext3_option1"),
            'ext3_option2'             => \Dict::getValue("ext3_option2"),
            'ext3_option3'             => \Dict::getValue("ext3_option3"),
            'ext3_option4'             => \Dict::getValue("ext3_option4"),
            'ext3_option5'             => \Dict::getValue("ext3_option5"),
            'ext3_option6'             => \Dict::getValue("ext3_option6"),
            'ext3_option7'             => \Dict::getValue("ext3_option7"),
            'ext3_option8'             => \Dict::getValue("ext3_option8"),
            'ext3_option9'             => \Dict::getValue("ext3_option9"),
            'ext3_option10'            => \Dict::getValue("ext3_option10"),
            'ext3_option11'            => \Dict::getValue("ext3_option11"),
            'ext3_option12'            => \Dict::getValue("ext3_option12"),
            'ext3_option13'            => \Dict::getValue("ext3_option13"),
            'ext3_option14'            => \Dict::getValue("ext3_option14"),
            'ext3_option15'            => \Dict::getValue("ext3_option15"),
            'ext3_option16'            => \Dict::getValue("ext3_option16"),
            'ext3_option17'            => \Dict::getValue("ext3_option17"),
            'ext3_option18'            => \Dict::getValue("ext3_option18"),
            'ext3_option19'            => \Dict::getValue("ext3_option19"),
            'ext3_option20'            => \Dict::getValue("ext3_option20"),
            'ext4_option1'             => \Dict::getValue("ext4_option1"),
            'ext4_option2'             => \Dict::getValue("ext4_option2"),
            'ext4_option3'             => \Dict::getValue("ext4_option3"),
            'ext4_option4'             => \Dict::getValue("ext4_option4"),
            'ext4_option5'             => \Dict::getValue("ext4_option5"),
            'ext4_option6'             => \Dict::getValue("ext4_option6"),
            'ext4_option7'             => \Dict::getValue("ext4_option7"),
            'ext4_option8'             => \Dict::getValue("ext4_option8"),
            'ext4_option9'             => \Dict::getValue("ext4_option9"),
            'ext4_option10'            => \Dict::getValue("ext4_option10"),
            'ext4_option11'            => \Dict::getValue("ext4_option11"),
            'ext4_option12'            => \Dict::getValue("ext4_option12"),
            'ext4_option13'            => \Dict::getValue("ext4_option13"),
            'ext4_option14'            => \Dict::getValue("ext4_option14"),
            'ext4_option15'            => \Dict::getValue("ext4_option15"),
            'ext4_option16'            => \Dict::getValue("ext4_option16"),
            'ext4_option17'            => \Dict::getValue("ext4_option17"),
            'ext4_option18'            => \Dict::getValue("ext4_option18"),
            'ext4_option19'            => \Dict::getValue("ext4_option19"),
            'ext4_option20'            => \Dict::getValue("ext4_option20"),
            'ext4_option21'            => \Dict::getValue("ext4_option21"),
            'ext4_option22'            => \Dict::getValue("ext4_option22"),
            'ext4_option23'            => \Dict::getValue("ext4_option23"),
            'ext4_option24'            => \Dict::getValue("ext4_option24"),
            'ext5_option1'             => \Dict::getValue("ext5_option1"),
            'ext5_option2'             => \Dict::getValue("ext5_option2"),
            'ext5_option3'             => \Dict::getValue("ext5_option3"),
            'ext5_option4'             => \Dict::getValue("ext5_option4"),
            'ext5_option5'             => \Dict::getValue("ext5_option5"),
            'ext5_option6'             => \Dict::getValue("ext5_option6"),
            'ext5_option7'             => \Dict::getValue("ext5_option7"),
            'ext5_option8'             => \Dict::getValue("ext5_option8"),
            'ext5_option9'             => \Dict::getValue("ext5_option9"),
            'ext5_option10'            => \Dict::getValue("ext5_option10"),
            'ext5_option11'            => \Dict::getValue("ext5_option11"),
            'ext5_option12'            => \Dict::getValue("ext5_option12"),
            'ext5_option13'            => \Dict::getValue("ext5_option13"),
            'ext5_option14'            => \Dict::getValue("ext5_option14"),
            'ext5_option15'            => \Dict::getValue("ext5_option15"),
            'ext5_option16'            => \Dict::getValue("ext5_option16"),
            'ext5_option17'            => \Dict::getValue("ext5_option17"),
            'ext5_option18'            => \Dict::getValue("ext5_option18"),
            'ext5_option19'            => \Dict::getValue("ext5_option19"),
            'ext5_option20'            => \Dict::getValue("ext5_option20"),
            'ext5_option21'            => \Dict::getValue("ext5_option21"),
            'ext5_option22'            => \Dict::getValue("ext5_option22"),
            'ext5_option23'            => \Dict::getValue("ext5_option23"),
            'ext5_option24'            => \Dict::getValue("ext5_option24"),
            'ext5_option25'            => \Dict::getValue("ext5_option25"),
            'ext5_option26'            => \Dict::getValue("ext5_option26"),
            'ext5_option27'            => \Dict::getValue("ext5_option27"),
            'ext5_option28'            => \Dict::getValue("ext5_option28"),
            'ext5_option29'            => \Dict::getValue("ext5_option29"),
            'ext5_option30'            => \Dict::getValue("ext5_option30"),
            'ext5_option31'            => \Dict::getValue("ext5_option31"),
            'ext5_option32'            => \Dict::getValue("ext5_option32"),
            'ext5_option33'            => \Dict::getValue("ext5_option33"),
            'ext5_option34'            => \Dict::getValue("ext5_option34"),
            'ext5_option35'            => \Dict::getValue("ext5_option35"),
            'ext5_option36'            => \Dict::getValue("ext5_option36"),
            'ext5_option37'            => \Dict::getValue("ext5_option37"),
            'ext5_option38'            => \Dict::getValue("ext5_option38"),
            'ext5_option39'            => \Dict::getValue("ext5_option39"),
            'ext5_option40'            => \Dict::getValue("ext5_option40"),
            'ext5_option41'            => \Dict::getValue("ext5_option41"),
            'ext5_option42'            => \Dict::getValue("ext5_option42"),
            'ext6_option1'             => \Dict::getValue("ext6_option1"),
            'ext6_option2'             => \Dict::getValue("ext6_option2"),
            'ext6_option3'             => \Dict::getValue("ext6_option3"),
            'ext6_option4'             => \Dict::getValue("ext6_option4"),
            'ext6_option5'             => \Dict::getValue("ext6_option5"),
            'ext6_option6'             => \Dict::getValue("ext6_option6"),
            'ext6_option7'             => \Dict::getValue("ext6_option7"),
            'ext6_option8'             => \Dict::getValue("ext6_option8"),
            'ext7_option1'             => \Dict::getValue("ext7_option1"),
            'ext7_option2'             => \Dict::getValue("ext7_option2"),
            'ext7_option3'             => \Dict::getValue("ext7_option3"),
            'unit_name'                => \Dict::getValue("unit_name")
        ];
    }

    public function filters() {
        return [
            'accessControl', // perform access control for CRUD operations
        ];
    }

    public function accessRules()
    {
        return
            [
                [
                    'allow', // allow authenticated users to access all actions
                    'users' => ['@'],
                ],
                [
                    'deny',  // deny all users
                    'users' => ['*'],
                ],
            ];
    }
}
?>

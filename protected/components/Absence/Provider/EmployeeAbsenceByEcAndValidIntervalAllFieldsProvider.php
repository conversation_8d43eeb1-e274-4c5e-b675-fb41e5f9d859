<?php

declare(strict_types=1);

namespace Components\Absence\Provider;

final class EmployeeAbsenceByEcAndValidIntervalAllFieldsProvider
{
    public function __invoke(string $validFrom, string $validTo, array $employeeContractIds, array $status)
    {
        $criterias = new \CDbCriteria();
        $criterias->select = 't.*';
        $criterias->alias = 't';
        $criterias->condition =
                        " t.day BETWEEN :validFrom AND :validTo
                        AND t.employee_contract_id IN ('" . join("','", $employeeContractIds) . "')
                        AND t.status IN ('" . join("','", $status) . "')
                        ";
        $criterias->params = [
            ':validFrom' => $validFrom,
            ':validTo' => $validTo,
        ];
        
        $result = \EmployeeAbsence::model()->findAll($criterias);
        if (!\is_array($result)) {
            $result = [];
        }

        $resultArray = [];
        foreach ($result as $value) {
            $resultArray[$value['day']][$value['employee_contract_id']] = $value;
        }

        return $resultArray;
    }
}

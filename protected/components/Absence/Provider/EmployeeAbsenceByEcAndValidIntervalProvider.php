<?php

declare(strict_types=1);

namespace Components\Absence\Provider;

final class EmployeeAbsenceByEcAndValidIntervalProvider
{
    public function __invoke(string $validFrom, string $validTo, array $employeeContractIds)
    {
        $criterias = new \CDbCriteria();
        $criterias->select = 't.employee_contract_id,t.state_type_id, t.day';
        $criterias->alias = 't';
        $criterias->condition =
                        "t.status = :published
						AND t.day BETWEEN :validFrom AND :validTo
						AND t.employee_contract_id IN ('" . join("','", $employeeContractIds) . "')
						";
        $criterias->params = [
            ':validFrom' => $validFrom,
            ':validTo' => $validTo,
            ':published' => \Status::PUBLISHED
        ];
        
        $result = \EmployeeAbsence::model()->findAll($criterias);
        if (!\is_array($result)) {
            $result = [];
        }

        $resultArray = [];
        foreach ($result as $value) {
            $resultArray[$value['day']][$value['employee_contract_id']] = $value['state_type_id'];
        }

        return $resultArray;
    }
}

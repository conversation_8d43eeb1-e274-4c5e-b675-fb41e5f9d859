<?php

declare(strict_types=1);

namespace Components\Absence\Provider;

final class StateTypeProvider
{
    public function __invoke()
    {
        $result =
            \Yii::app()->db->createCommand()
                ->select('t.state_type_id, dict.dict_value as value')
                ->from('state_type t')
                ->join('dictionary dict',
                    "dict.`module` = 'ttwa-ahp'
                        AND dict.`lang` = '" . \Dict::getLang() . "'
                        AND dict.`valid` = 1
                        AND dict.`dict_id` = t.`name_dict_id`")
                ->where('t.status = :published
						', [':published' => \Status::PUBLISHED])
                ->queryAll()
        ;

        if (!\is_array($result)) {
            $result = [];
        }

        $resultArray = [];
        foreach ($result as $value) {
            $resultArray[$value['state_type_id']] = $value['value'];
        }
        
        return $resultArray;
    }
}

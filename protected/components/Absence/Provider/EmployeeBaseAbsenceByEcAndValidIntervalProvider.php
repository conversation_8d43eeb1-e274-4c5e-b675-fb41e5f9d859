<?php

declare(strict_types=1);

namespace Components\Absence\Provider;

final class EmployeeBaseAbsenceByEcAndValidIntervalProvider
{
    public function __invoke(string $validFrom, string $validTo, array $employeeContractIds)
    {
        $criteria = new \CDbCriteria();
        $criteria->addCondition('employee_contract_id IN ('.implode(',', $employeeContractIds).')');
        $criteria->addCondition('valid_from = :validFrom');
        $criteria->addCondition('valid_to = :valid_to');
        $criteria->addCondition('status = :status');
        $criteria->params = [
            ':status' => \Status::PUBLISHED,
            ':validFrom' => $validFrom,
            ':valid_to' => $validTo
        ];
        
        return \EmployeeBaseAbsence::model()->findAll($criteria);
    }
}

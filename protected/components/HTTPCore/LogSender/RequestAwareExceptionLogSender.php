<?php

declare(strict_types=1);

namespace Components\HTTPCore\LogSender;

use Components\Core\LogSender\AbstractExceptionLogSender;
use Components\HTTPCore\Interfaces\RequestAwareExceptionInterface;

final class RequestAwareExceptionLogSender extends AbstractExceptionLogSender
{
    public function send(\Throwable $exception): void
    {
        /** @var RequestAwareExceptionInterface $requestUri */
        $requestUri = $exception->getRequest()->getUri()->__toString();
        $message = "Request failed! Request URI: {$requestUri}";
        if ($exception->hasResponse()) {
            $response = $exception->getResponse();
            $responseBody = $response->getBody()->__toString();
            $responseCode = $response->getStatusCode();
            $message .= " Response body: {$responseBody}, Response code: {$responseCode}";
        }
        \Yii::log(
            $message,
            $this->getLogLevel($exception),
            $this->getCategory($exception)
        );
    }

    public function isSupported($exception): bool
    {
        return $exception instanceof RequestAwareExceptionInterface;
    }
}

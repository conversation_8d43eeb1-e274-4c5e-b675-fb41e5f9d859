<?php

declare(strict_types=1);

namespace Components\HTTPCore\ResponseSender;

use Psr\Http\Message\ResponseInterface;

final class DefaultResponseSender
{
    public function send(ResponseInterface $response): void
    {
        header(
            sprintf(
                'HTTP/%s %s %s',
                $response->getProtocolVersion(),
                $response->getStatusCode(),
                $response->getReasonPhrase()
            )
        );
        foreach ($response->getHeaders() as $headerName => $headerValues) {
            foreach ($headerValues as $headerValue) {
                header(sprintf('%s: %s', $headerName, $headerValue), false);
            }
        }
        echo $response->getBody();
    }
}

<?php

declare(strict_types=1);

namespace Components\HTTPCore\Exception;

use Psr\Http\Message\ServerRequestInterface;

final class HTTPCoreRequestVariableMissingErrorException extends \RuntimeException
{
    private ServerRequestInterface $serverRequest;
    private string $notFoundedVariableName;

    public function __construct(ServerRequestInterface $serverRequest, string $notFoundedVariableName)
    {
        parent::__construct();
        $this->serverRequest = $serverRequest;
        $this->notFoundedVariableName = $notFoundedVariableName;
    }

    public function getErrorMessage(): string
    {
        return "The variable name " . $this->notFoundedVariableName . "could not be found!"; //TODO: Dict
    }

    public function getServerRequest(): ServerRequestInterface
    {
        return $this->serverRequest;
    }


}
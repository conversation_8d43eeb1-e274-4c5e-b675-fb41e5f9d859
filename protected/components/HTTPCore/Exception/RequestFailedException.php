<?php

declare(strict_types=1);

namespace Components\HTTPCore\Exception;

use Components\HTTPCore\Interfaces\RequestAwareExceptionInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

final class RequestFailedException extends \RuntimeException implements
    RequestAwareExceptionInterface
{
    private RequestInterface $request;
    private ResponseInterface $response;

    public function __construct(
        RequestInterface $request,
        ResponseInterface $response = null,
        \Throwable $previous = null
    ) {
        $this->request = $request;
        $this->response = $response;
        parent::__construct('', 0, $previous);
    }

    public function getRequest(): RequestInterface
    {
        return $this->request;
    }

    public function getResponse(): ResponseInterface
    {
        return $this->response;
    }

    public function hasResponse(): bool
    {
        return isset($this->response);
    }
}

<?php

declare(strict_types=1);

namespace Components\HTTPCore\Builder;

use Components\Core\Form\ArrayFormModel;
use Psr\Http\Message\ServerRequestInterface;

final class RequestValidatorBuilder
{
    private ServerRequestInterface $request;

    public function __construct(ServerRequestInterface $request)
    {
        $this->request = $request;
    }

    public function __invoke(array $rules): ArrayFormModel
    {
        $validator = new ArrayFormModel(get_class($this));
        $validator->setRules($rules);
        $values = [];
        foreach ($this->request->getQueryParams() as $requestName => $requestValue) {
            $values[$requestName] = $requestValue;
        }
        $validator->setValues($values);

        return $validator;
    }
}

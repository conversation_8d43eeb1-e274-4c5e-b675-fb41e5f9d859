<?php

declare(strict_types=1);

namespace Components\HTTPCore\Builder;

use Components\HTTPCore\Interfaces\JsonResponseBuilderInterface;
use Fig\Http\Message\StatusCodeInterface;
use <PERSON>yholm\Psr7\Factory\Psr17Factory;
use Psr\Http\Message\ResponseInterface;

final class JsonResponseBuilder implements JsonResponseBuilderInterface
{
    private array $data;
    private int $status;
    private array $headers;

    public function __construct()
    {
        $this->data = [];
        $this->status = StatusCodeInterface::STATUS_OK;
        $this->headers = [];
    }

    public function setData(array $data): self
    {
        $this->data = $data;
        return $this;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function setHeaders(array $headers): self
    {
        $this->headers = $headers;
        return $this;
    }

    public function build(): ResponseInterface
    {
        try {
            $responseJson = json_encode($this->data, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            $responseJson = '{"message": "System Error", "status": 0}';
        }

        $psr17Factory = new Psr17Factory();
        $response = $psr17Factory->createResponse($this->status)
            ->withBody($psr17Factory->createStream($responseJson));
        foreach ($this->headers as $name => $header) {
            $response = $response->withHeader($name, $header);
        }
        return $response;
    }
}

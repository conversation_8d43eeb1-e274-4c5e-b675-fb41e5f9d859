<?php

declare(strict_types=1);

namespace Components\HTTPCore\Builder;

use <PERSON>yholm\Psr7\Factory\Psr17Factory;
use <PERSON>yholm\Psr7Server\ServerRequestCreator;
use Psr\Http\Message\ServerRequestInterface;

final class RequestBuilder
{
    public function build(): ServerRequestInterface
    {
        $psr17Factory = new Psr17Factory();
        $creator = new ServerRequestCreator(
            $psr17Factory,
            $psr17Factory,
            $psr17Factory,
            $psr17Factory
        );
        return $creator->fromGlobals();
    }
}
<?php

declare(strict_types=1);

namespace Components\HTTPCore\Builder;

use Components\HTTPCore\Interfaces\AjaxResponseBuilderInterface;
use Components\HTTPCore\Interfaces\AjaxResponseDataPartBuilderInterface;
use Fig\Http\Message\StatusCodeInterface;
use Psr\Http\Message\ResponseInterface;

class DefaultAjaxResponseBuilder implements AjaxResponseBuilderInterface
{
    /**
     * @var AjaxResponseDataPartBuilderInterface[]
     */
    protected array $partBuilders;
    protected array $headers;
    protected int $status;

    public function __construct()
    {
        $this->partBuilders = [];
        $this->headers = [];
        $this->status = StatusCodeInterface::STATUS_OK;
    }

    public function addPartBuilder(AjaxResponseDataPartBuilderInterface $builder): self
    {
        $this->partBuilders[] = $builder;

        return $this;
    }

    public function setHeaders(array $headers): self
    {
        $this->headers = $headers;
        return $this;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function build(): ResponseInterface
    {
        $data = [];
        foreach ($this->partBuilders as $partBuilder) {
            $data = $partBuilder->build($data);
        }

        return (new JsonResponseBuilder())
            ->setData($data)
            ->setHeaders($this->headers)
            ->setStatus($this->status)
            ->build();
    }
}

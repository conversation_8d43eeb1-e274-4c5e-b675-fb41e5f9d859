<?php

declare(strict_types=1);

namespace Components\HTTPCore\Builder;

use Components\Core\Interfaces\ParametersAwareExceptionInterface;
use Components\Grid2Core\Enum\AjaxDefaultFieldNameEnum;
use Components\HTTPCore\Interfaces\AjaxErrorResponseDataPartBuilderInterface;

final class DefaultAjaxErrorResponseDataPartBuilder implements
    AjaxErrorResponseDataPartBuilderInterface
{
    private \Throwable $exception;
    private string $dictionaryId;
    private string $module;
    private int $status;
    private array $params;

    public function __construct(string $dictionaryId, string $module, array $params = [], int $status = 0)
    {
        $this->dictionaryId = $dictionaryId;
        $this->module = $module;
        $this->status = $status;
        $this->params = $params;
    }

    public function setException(\Throwable $exception): self
    {
        $this->exception = $exception;

        return $this;
    }

    public function build(array $part): array
    {
        $params = $this->params;
        if ($this->exception instanceof ParametersAwareExceptionInterface) {
            $params = array_merge($params, $this->exception->getParameters());
        }
        $part[AjaxDefaultFieldNameEnum::MESSAGE] = \Dict::getValue(
            $this->dictionaryId,
            $params,
            $this->module
        );
        $part[AjaxDefaultFieldNameEnum::STATUS] = $this->status;

        return $part;
    }
}

<?php

declare(strict_types=1);

namespace Components\HTTPCore\Builder;

use Components\HTTPCore\Interfaces\AjaxErrorResponseDataPartBuilderInterface;
use Components\HTTPCore\Interfaces\AjaxExceptionAwareResponseBuilderInterface;
use Components\HTTPCore\Interfaces\AjaxResponseDataPartBuilderInterface;
use Fig\Http\Message\StatusCodeInterface;
use Psr\Http\Message\ResponseInterface;

final class SystemErrorResponseBuilder implements AjaxExceptionAwareResponseBuilderInterface
{
    private \Throwable $exception;

    /**
     * @var AjaxResponseDataPartBuilderInterface[]
     */
    private array $partBuilders;
    private array $headers;
    private int $status;

    public function __construct()
    {
        $this->partBuilders = [];
        $this->headers = [];
        $this->status = StatusCodeInterface::STATUS_OK;
    }

    public function setException(\Throwable $exception): self
    {
        $this->exception = $exception;

        return $this;
    }

    public function addPartBuilder(AjaxResponseDataPartBuilderInterface $builder): self
    {
        $this->partBuilders[] = $builder;

        return $this;
    }

    public function setHeaders(array $headers): self
    {
        $this->headers = $headers;
        return $this;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function build(): ResponseInterface
    {
        $responsePart = [];
        foreach ($this->partBuilders as $builder) {
            if ($builder instanceof AjaxErrorResponseDataPartBuilderInterface) {
                $builder->setException($this->exception);
            }
            $responsePart = $builder->build($responsePart);
        }

        return (new JsonResponseBuilder())
            ->setData($responsePart)
            ->setHeaders($this->headers)
            ->setStatus($this->status)
            ->build();
    }
}

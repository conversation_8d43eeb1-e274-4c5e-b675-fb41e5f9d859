<?php

declare(strict_types=1);

namespace Components\HTTPCore\Builder;

use Psr\Http\Message\ServerRequestInterface;
use JsonException;

final class JsonRequestBuilder
{
    /**
     * @throws JsonException
     */
    public function build(): ServerRequestInterface
    {
        $request = (new RequestBuilder())->build();
        $body = json_decode((string)$request->getBody(), true, 512, JSON_THROW_ON_ERROR);
        return $request->withParsedBody($body);
    }

}
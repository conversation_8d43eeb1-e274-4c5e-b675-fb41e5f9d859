<?php

declare(strict_types=1);

namespace Components\NexonApi\Descriptor;

use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;

final class EmployeeGroupDescriptor
{
    private string|null $rowId;
    private string|null $employeeContractId;
    private string $groupId;
    private string $groupValue;
    private \DateTime $validFrom;
    private \DateTime $validTo;
    private int $status;
    private string $createdBy;
    private \DateTime $createdOn;

    public function __construct()
    {
        $this->rowId = null;
        $this->employeeContractId = null;
        $this->status = \Status::PUBLISHED;
        $this->createdBy = EmployeeTablesFieldsEnum::DEFAULT_CREATED_BY;
        $this->createdOn = new \Datetime(EmployeeTablesFieldsEnum::DEFAULT_CREATED_ON);
    }

    public function getRowId(): ?string
    {
        return $this->rowId;
    }

    public function setRowId(?string $rowId): void
    {
        $this->rowId = $rowId;
    }

    public function getEmployeeContractId(): ?string
    {
        return $this->employeeContractId;
    }

    public function setEmployeeContractId(?string $employeeContractId): void
    {
        $this->employeeContractId = $employeeContractId;
    }

    public function getGroupId(): string
    {
        return $this->groupId;
    }

    public function setGroupId(string $groupId): void
    {
        $this->groupId = $groupId;
    }

    public function getGroupValue(): string
    {
        return $this->groupValue;
    }

    public function setGroupValue(string $groupValue): void
    {
        $this->groupValue = $groupValue;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function setValidFrom($validFrom): void
    {
        if (is_string($validFrom)) {
            $validFrom = new \DateTime($validFrom);
        }
        $this->validFrom = $validFrom;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }

    public function setValidTo($validTo): void
    {
        if (is_null($validTo)) {
            $validTo = new \Datetime(EmployeeTablesFieldsEnum::DEFAULT_VALID_TO);
        }
        if (is_string($validTo)) {
            $validTo = new \DateTime($validTo);
        }
        $this->validTo = $validTo;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): void
    {
        $this->status = $status;
    }

    public function getCreatedBy(): string
    {
        return $this->createdBy;
    }

    public function setCreatedBy(string $createdBy): void
    {
        $this->createdBy = $createdBy;
    }

    public function getCreatedOn(): \DateTime
    {
        return $this->createdOn;
    }

    public function setCreatedOn(\DateTime $createdOn): void
    {
        $this->createdOn = $createdOn;
    }

    public function getEmployeeDataMD5Hash(): string
    {
        $data = (string)$this->rowId .
            (string)$this->employeeContractId .
            (string)$this->groupId .
            (string)$this->groupValue .
            (string)$this->validFrom->format('Y-m-d') .
            (string)$this->validTo->format('Y-m-d');
        return md5($data);
    }
}
<?php

namespace Components\NexonApi\Descriptor;

final class NexonRawDataDescriptor
{
    private string $rawdata;
    private string $workflow;
    private string $handlerName;
    private int $processId;

    public function __construct(
        string $rawdata
    ) {
        $this->rawdata = $rawdata;
        $this->workflow = '';
        $this->processId = 0;
    }

    public function getRawData(): string
    {
        return $this->rawdata;
    }

    public function getMd5Hash(): string
    {
        return md5($this->rawdata);
    }

    public function getWorkflow(): string
    {
        return $this->workflow;
    }

    public function setWorkflow(string $workflow): void
    {
        $this->workflow = $workflow;
    }

    public function getProcessId(): int
    {
        return $this->processId;
    }

    public function setProcessId(int $processId): void
    {
        $this->processId = $processId;
    }

    public function getHandlerName(): string
    {
        return $this->handlerName;
    }

    public function setHandlerName(string $handlerName): void
    {
        $this->handlerName = $handlerName;
    }
}

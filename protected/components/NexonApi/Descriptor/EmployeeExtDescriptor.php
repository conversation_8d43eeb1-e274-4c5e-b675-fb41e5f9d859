<?php

declare(strict_types=1);

namespace Components\NexonApi\Descriptor;

use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;

final class EmployeeExtDescriptor
{
    private int|null $rowId;
    private string $employeeId;
    private string|null $placeOfBirth;
    private \DateTime|null $dateOfBirth;
    private string|null $mothersName;
    private string|null $ssn;
    private string|null $personalIdCardNumber;
    private string|null $passportNumber;
    private string|null $option1;
    private string|null $option2;
    private string|null $option3;
    private string|null $option4;
    private string|null $option5;
    private int $status;
    private \DateTime $validFrom;
    private \DateTime $validTo;
    private string $createdBy;
    private \DateTime $createdOn;
    private string $insertOrUpdate;

    public function __construct()
    {
        $this->rowId = null;
        $this->placeOfBirth = null;
        $this->dateOfBirth = null;
        $this->mothersName = null;
        $this->ssn = null;
        $this->personalIdCardNumber = null;
        $this->passportNumber = null;
        $this->option1 = null;
        $this->option2 = null;
        $this->option3 = null;
        $this->option4 = null;
        $this->option5 = null;
        $this->status = \Status::PUBLISHED;
        $this->createdBy = EmployeeTablesFieldsEnum::DEFAULT_CREATED_BY;
        $this->createdOn = new \Datetime(EmployeeTablesFieldsEnum::DEFAULT_CREATED_ON);
        $this->insertOrUpdate = EmployeeTablesFieldsEnum::DEFAULT_INSERT_OR_UPDATE;
    }

    public function getRowId(): ?int
    {
        return $this->rowId;
    }

    public function setRowId(?int $rowId): void
    {
        $this->rowId = $rowId;
    }

    public function getEmployeeId(): string
    {
        return $this->employeeId;
    }

    public function setEmployeeId(string $employeeId): void
    {
        $this->employeeId = $employeeId;
    }

    public function getPlaceOfBirth(): ?string
    {
        return $this->placeOfBirth;
    }

    public function setPlaceOfBirth(?string $placeOfBirth): void
    {
        $this->placeOfBirth = $placeOfBirth;
    }

    public function getDateOfBirth(): ?\DateTime
    {
        return $this->dateOfBirth;
    }

    public function setDateOfBirth($dateOfBirth): void
    {
        if (is_string($dateOfBirth)) {
            $dateOfBirth = new \DateTime($dateOfBirth);
        }
        $this->dateOfBirth = $dateOfBirth;
    }


    public function getMothersName(): ?string
    {
        return $this->mothersName;
    }

    public function setMothersName(?string $mothersName): void
    {
        $this->mothersName = $mothersName;
    }

    public function getSsn(): ?string
    {
        return $this->ssn;
    }

    public function setSsn(?string $ssn): void
    {
        $this->ssn = $ssn;
    }

    public function getPersonalIdCardNumber(): ?string
    {
        return $this->personalIdCardNumber;
    }

    public function setPersonalIdCardNumber(?string $personalIdCardNumber): void
    {
        $this->personalIdCardNumber = $personalIdCardNumber;
    }

    public function getPassportNumber(): ?string
    {
        return $this->passportNumber;
    }

    public function setPassportNumber(?string $passportNumber): void
    {
        $this->passportNumber = $passportNumber;
    }

    public function getOption1(): ?string
    {
        return $this->option1;
    }

    public function setOption1(?string $option1): void
    {
        $this->option1 = $option1;
    }

    public function getOption2(): ?string
    {
        return $this->option2;
    }

    public function setOption2(?string $option2): void
    {
        $this->option2 = $option2;
    }

    public function getOption3(): ?string
    {
        return $this->option3;
    }

    public function setOption3(?string $option3): void
    {
        $this->option3 = $option3;
    }

    public function getOption4(): ?string
    {
        return $this->option4;
    }

    public function setOption4(?string $option4): void
    {
        $this->option4 = $option4;
    }

    public function getOption5(): ?string
    {
        return $this->option5;
    }

    public function setOption5(?string $option5): void
    {
        $this->option5 = $option5;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): void
    {
        $this->status = $status;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function setValidFrom($validFrom): void
    {
        if (is_string($validFrom)) {
            $validFrom = new \DateTime($validFrom);
        }
        $this->validFrom = $validFrom;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }

    public function setValidTo($validTo): void
    {
        if (is_null($validTo)) {
            $validTo = new \Datetime(EmployeeTablesFieldsEnum::DEFAULT_VALID_TO);
        }
        if (is_string($validTo)) {
            $validTo = new \DateTime($validTo);
        }
        $this->validTo = $validTo;
    }

    public function getCreatedBy(): string
    {
        return $this->createdBy;
    }

    public function setCreatedBy(string $createdBy): void
    {
        $this->createdBy = $createdBy;
    }

    public function getCreatedOn(): \DateTime
    {
        return $this->createdOn;
    }

    public function setCreatedOn(\DateTime $createdOn): void
    {
        $this->createdOn = $createdOn;
    }

    public function getInsertOrUpdate(): string
    {
        $this->insertOrUpdate = 'update';
        if (is_null($this->getRowId())) {
            $this->insertOrUpdate = 'insert';
        }
        return $this->insertOrUpdate;
    }

    public function getIsNewRecord(): bool
    {
        if (is_null($this->getRowId())) {
            return true;
        }
        return false;
    }

    public function getEmployeeExtDataMD5Hash(): string
    {
        $data = (string)$this->rowId .
            (string)$this->employeeId .
            (string)$this->ssn .
            (string)$this->validFrom->format('Y-m-d') .
            (string)$this->validTo->format('Y-m-d');
        return md5($data);
    }

}
<?php

namespace Components\NexonApi\Descriptor;

use Components\NexonApi\Enum\EmployeeCostTableFieldsEnum;

final class EmployeeCostDescriptor
{
    private int|null $rowId;
    private string $employeeContractId;
    private string $costId;
    private string $costCenterId;
    private string|null $percent;
    private \DateTime $validFrom;
    private \DateTime $validTo;
    private int|null $status;
    private string|null $createdBy;
    private \DateTime $createdOn;
    private string $insertOrUpdate;

    public function __construct(
    )
    {
        $this->rowId = null;
        $this->createdBy = EmployeeCostTableFieldsEnum::CREATED_BY;
        $this->createdOn = new \Datetime(EmployeeCostTableFieldsEnum::CREATED_ON);
        $this->insertOrUpdate = EmployeeCostTableFieldsEnum::DEFAULT_INSERT_OR_UPDATE;
    }

    public function getRowId(): ?int
    {
        return $this->rowId;
    }

    public function setRowId(?int $rowId): void
    {
        $this->rowId = $rowId;
    }

    public function getEmployeeContractId(): string
    {
        return $this->employeeContractId;
    }

    public function setEmployeeContractId(string $employeeContractId): void
    {
        $this->employeeContractId = $employeeContractId;
    }

    public function getCostId(): string
    {
        return $this->costId;
    }

    public function setCostId(?string $costId): void
    {
        $this->costId = $costId;
    }

    public function getCostCenterId(): string
    {
        return $this->costCenterId;
    }

    public function setCostCenterId(?string $costCenterId): void
    {
        $this->costCenterId = $costCenterId;
    }

    public function getPercent(): ?string
    {
        return $this->percent;
    }

    public function setPercent(?string $percent): void
    {
        $this->percent = $percent;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function setValidFrom($validFrom): void
    {
        if (is_string($validFrom)) {
            $validFrom = new \DateTime($validFrom);
        }
        $this->validFrom = $validFrom;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }

    public function setValidTo( $validTo): void
    {
        if (is_null($validTo)) {
            $validTo = new \Datetime(EmployeeCostTableFieldsEnum::VALID_TO);
        }
        if (is_string($validTo)) {
            $validTo = new \DateTime($validTo);
        }
        $this->validTo = $validTo;
    }

    public function getStatus(): ?int
    {
        return $this->status;
    }

    public function setStatus(?int $status): void
    {
        $this->status = $status;
    }

    public function getCreatedBy(): ?string
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?string $createdBy): void
    {
        $this->createdBy = $createdBy;
    }

    public function getCreatedOn(): \DateTime
    {
        return $this->createdOn;
    }

    public function setCreatedOn(\DateTime $createdOn): void
    {
        $this->createdOn = $createdOn;
    }

    public function getEmployeeCostDataMD5Hash() : string
    {
        $data = (string)$this->employeeContractId .
            (string)$this->costId .
            (string)$this->costCenterId .
            (string)$this->validFrom->format("Y-m-d") .
            (string)$this->validTo->format("Y-m-d");
        return md5($data);
    }

    public function getInsertOrUpdate(): string
    {
        $this->insertOrUpdate = "update";
        if (is_null($this->getRowId())) {
            $this->insertOrUpdate = "insert";
        }
        return $this->insertOrUpdate;
    }
}
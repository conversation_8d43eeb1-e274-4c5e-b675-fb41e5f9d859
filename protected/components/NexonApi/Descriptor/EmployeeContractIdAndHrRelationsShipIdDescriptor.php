<?php

namespace Components\NexonApi\Descriptor;

final class EmployeeContractIdAndHrRelationsShipIdDescriptor
{
    private string $hrRelationShipId;
    private string $employeeContractId;

    public function getHrRelationShipId(): string
    {
        return $this->hrRelationShipId;
    }

    public function setHrRelationShipId(?string $hrRelationShipId): void
    {
        $this->hrRelationShipId = $hrRelationShipId;
    }

    public function getEmployeeContractId(): string
    {
        return $this->employeeContractId;
    }

    public function setEmployeeContractId(?string $employeeContractId): void
    {
        $this->employeeContractId = $employeeContractId;
    }
}
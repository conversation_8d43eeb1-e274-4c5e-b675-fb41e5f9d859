<?php

namespace Components\NexonApi\Descriptor;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;


final class BaseAbsenceDescriptor
{
    private int|null $rowId;
    private string $employeeContractId;
    private string $baseAbsenceTypeId;
    private float|null $quantity;
    private float|null $quantityHour;
    private string|null $note;
    private \DateTime $validFrom;
    private \DateTime $validTo;
    private string|null $status;
    private string|null $createdBy;
    private \DateTime $createdOn;
    private string $insertOrUpdate;

    public function __construct(
    ) {
        $this->rowId = null;
        $this->status = null;
        $this->quantityHour = null;
        $this->createdBy = EmployeeTablesFieldsEnum::DEFAULT_CREATED_BY;
        $this->createdOn = new \Datetime(EmployeeTablesFieldsEnum::DEFAULT_CREATED_ON);
        $this->insertOrUpdate = EmployeeTablesFieldsEnum::DEFAULT_INSERT_OR_UPDATE;
    }

    public function getRowId()
    {
        return $this->rowId;
    }
    public function setRowId($rowId)
    {
        $this->rowId = $rowId;
    }

    public function getEmployeeContractId()
    {
        return $this->employeeContractId;
    }

    public function setEmployeeContractId($employeeContractId)
    {
        $this->employeeContractId = $employeeContractId;
    }

    public function getBaseAbsenceTypeId()
    {
        return $this->baseAbsenceTypeId;
    }

    public function setBaseAbsenceTypeId($baseAbsenceTypeId)
    {
        $this->baseAbsenceTypeId = $baseAbsenceTypeId;
    }

    public function getQuantity()
    {
        return $this->quantity;
    }

    public function setQuantity($quantity)
    {
        $this->quantity = $quantity;
    }

    public function getQuantityHour()
    {
        return $this->quantityHour;
    }

    public function setQuantityHour($quantityHour)
    {
        $this->quantityHour = $quantityHour;
    }

    public function getNote()
    {
        return $this->note;
    }

    public function setNote($note)
    {
        $this->note = $note;
    }

    public function getValidFrom()
    {
        return $this->validFrom;
    }
    public function setValidFrom($validFrom)
    {
        if (is_string($validFrom)) {
            $validFrom = new \DateTime($validFrom);
        }
        $this->validFrom = $validFrom;
    }

    public function getValidTo()
    {
        return $this->validTo;
    }

    public function setValidTo($validTo)
    {
        if (is_null($validTo)) {
            $validTo = new \Datetime(EmployeeTablesFieldsEnum::DEFAULT_VALID_TO);
        }
        if (is_string($validTo)) {
            $validTo = new \DateTime($validTo);
        }
        $this->validTo = $validTo;
    }

    public function getStatus()
    {
        return $this->status;
    }
    public function setStatus($status)
    {
        $this->status = $status;
    }

    public function getCreatedBy()
    {
        return $this->createdBy;
    }

    public function setCreatedBy($createdBy)
    {
        $this->createdBy = $createdBy;
    }

    public function getCreatedOn()
    {
        return $this->createdOn;
    }

    public function setCreatedOn($createdOn)
    {
        $this->createdOn = $createdOn;
    }

    public function getInsertOrUpdate(): string
    {
        $this->insertOrUpdate = "update";
        if (is_null($this->getRowId())) {
            $this->insertOrUpdate = "insert";
        }
        return $this->insertOrUpdate;
    }

    public function getIsNewRecord() : bool
    {
        if (is_null($this->getRowId())) {
            return true;
        }
        return false;
    }

    public function getBaseAbsenceMD5Hash() : string
    {
        $data = (string)$this->employeeContractId .
                (string)$this->baseAbsenceTypeId .
                (string)$this->quantity . 
                (string)$this->validFrom->format("Y-m-d") .
                (string)$this->validTo->format("Y-m-d");
        return md5($data);        
    }

    public function getBaseAbsenceWithoutQuantityMD5Hash(): string
    {
        $data = (string)$this->employeeContractId .
            (string)$this->baseAbsenceTypeId .
            (string)$this->quantity .
            (string)$this->quantityHour .
            (string)$this->validFrom->format("Y-m-d") .
            (string)$this->validTo->format("Y-m-d");
        return md5($data);
    }

    public function getBaseAbsenceOnlyStateTypeMD5Hash() : string
    {
        $data = (string)$this->employeeContractId .
                (string)$this->baseAbsenceTypeId .
                (string)$this->validFrom->format("Y-m-d") . 
                (string)$this->validTo->format("Y-m-d");
        return md5($data);        
    }
}
<?php

namespace Components\NexonApi\Writer;

use Components\NexonApi\Descriptor\NexonRawDataDescriptor;
use Components\NexonApi\Interfaces\NexonApiDataWriterInterface;

final class NexonApiDataWriter implements NexonApiDataWriterInterface
{
    public function write(NexonRawDataDescriptor $descriptor) : void
    {
        $model = new \NexonApiRawdata();
        $model->process_id = $descriptor->getProcessId();
        $model->workflow = "{$descriptor->getWorkflow()}:{$descriptor->getHandlerName()}";
        $model->raw_data = $descriptor->getRawData();
        $model->md5hash = $descriptor->getMd5Hash();
        $model->status = \Status::PUBLISHED;
        $model->created_by = UserId();
        $model->created_on = date('Y-m-d H:i:s');
        $model->save();
    }
}

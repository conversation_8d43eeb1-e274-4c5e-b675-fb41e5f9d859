<?php

declare(strict_types=1);

namespace Components\NexonApi\Guesser;

use Components\NexonApi\Descriptor\EmployeeDescriptor;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;

final class EmployeeValidityToDescriptorGuesser
{
    public function guess(array $value) :EmployeeDescriptor
    {
        $descriptor = new EmployeeDescriptor();
        $descriptor->setRowId($value[EmployeeTablesFieldsEnum::ROW_ID] ?? null);
        $descriptor->setEmployeeId($value[EmployeeTablesFieldsEnum::EMPLOYEE_ID] ?? null);
        $descriptor->setCompanyId($value[EmployeeTablesFieldsEnum::COMPANY_ID] ?? null);
        $descriptor->setCompanyOrgGroupId1($value[EmployeeTablesFieldsEnum::COMPANY_ORG_GROUP1_ID] ?? null);
        $descriptor->setCompanyOrgGroupId2($value[EmployeeTablesFieldsEnum::COMPANY_ORG_GROUP2_ID] ?? null);
        $descriptor->setCompanyOrgGroupId3($value[EmployeeTablesFieldsEnum::COMPANY_ORG_GROUP3_ID] ?? null);
        $descriptor->setUnitId($value[EmployeeTablesFieldsEnum::UNIT_ID] ?? null);
        $descriptor->setPayrollId($value[EmployeeTablesFieldsEnum::PAYROLL_ID] ?? null);
        $descriptor->setEmpId($value[EmployeeTablesFieldsEnum::EMP_ID] ?? null);
        $descriptor->setTitle($value[EmployeeTablesFieldsEnum::TITLE] ?? null);
        $descriptor->setFirstName($value[EmployeeTablesFieldsEnum::FIRST_NAME] ?? null);
        $descriptor->setLastName($value[EmployeeTablesFieldsEnum::LAST_NAME] ?? null);
        $descriptor->setNameofbirth($value[EmployeeTablesFieldsEnum::NAMEOFBIRTH] ?? null);
        $descriptor->setTaxNumber($value[EmployeeTablesFieldsEnum::TAX_NUMBER] ?? null);
        $descriptor->setGender($value[EmployeeTablesFieldsEnum::GENDER] ?? null);
        $descriptor->setEmployeeImage($value[EmployeeTablesFieldsEnum::EMPLOYEE_IMAGE] ?? null);
        $descriptor->setBlacklistNote($value[EmployeeTablesFieldsEnum::BLACKLIST_NOTE] ?? null);
        $descriptor->setStatus(\Status::PUBLISHED);
        $descriptor->setValidFrom($value[EmployeeTablesFieldsEnum::VALID_FROM] ?? null);
        $descriptor->setValidTo($value[EmployeeTablesFieldsEnum::VALID_TO] ?? null);

        return $descriptor;
    }


}
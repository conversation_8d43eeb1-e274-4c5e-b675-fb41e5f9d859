<?php

declare(strict_types=1);

namespace Components\NexonApi\Guesser;

use Components\NexonApi\Descriptor\EmployeeContractDescriptor;
use Components\NexonApi\Enum\UserTableFieldsEnum;

final class UserRoleGroupRolesGuesser
{
    public function guess(EmployeeContractDescriptor $descriptor, int $roleNumber) :array
    {
        switch ($roleNumber) {
            case 1 : return $this->roleGroupRole1($descriptor);
            case 2 : return $this->roleGroupRole2();
        }

        return [];
    }

    private function roleGroupRole1(EmployeeContractDescriptor $descriptor) : array
    {
        if (in_array($descriptor->getEmployeeContractPositionId(), UserTableFieldsEnum::BOLTVEZETO_POSITIONS)) {
            return [UserTableFieldsEnum::PLANNING_ROLEGROUP, true];
        }
        return [UserTableFieldsEnum::DEFAULT_ROLEGROUP, false];
    }

    private function roleGroupRole2() : array
    {
        return [UserTableFieldsEnum::PENNY_DEFAULT_AUTH_ROLEGROUP, true];
    }
}
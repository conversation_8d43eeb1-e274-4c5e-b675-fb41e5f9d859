<?php

declare(strict_types=1);

namespace Components\NexonApi\Guesser;

use Components\NexonApi\Enum\UserTableFieldsEnum;
use Flow\ETL\Row;

final class UserApproverGuesser
{
    public function guess(Row $row, int $roleNumber) :array
    {
        switch ($roleNumber) {
            case 1 : return $this->approverRule1($row);
            case 2 : return $this->approverRule2($row);
        }

        return [];
    }

    private function approverRule1(Row $row) : array
    {
        $descriptors = $row->valueOf('employeeDescriptors');
        if (empty($descriptors)) {
            $descriptors = $row->valueOf('employeeFromDbDescriptors');
        }
        $lastKey = array_key_last($descriptors);
        $lastDescriptor = $descriptors[$lastKey] ?? null;
        if (is_null($lastDescriptor)) {
            return [];
        }
        $approverModel = [];
        $relatedValue['Unit'] = $lastDescriptor->getUnitId();
        $relatedValue['Company'] = $lastDescriptor->getCompanyId();
        foreach (UserTableFieldsEnum::APPROVER_PROCESS_IDS AS $processId) {
            foreach (UserTableFieldsEnum::APPROVER_RELATED_MODELS AS $relatedModel => $relatedModelId) {
                $approver = new \Approver();
                $approver->process_id = $processId;
                $approver->related_model = $relatedModel;
                $approver->related_id = $relatedModelId;
                $approver->related_value = $relatedValue[$relatedModel];
                $approver->approver_user_id = $row->valueOf('EmployeeNumber');
                $approver->valid_from = date("Y").'-01-01';
                $approver->valid_to = UserTableFieldsEnum::VALID_TO;
                $approver->created_by = UserTableFieldsEnum::CREATED_BY;
                $approver->created_on = (new \DateTime(UserTableFieldsEnum::CREATED_ON))->format("Y-m-d");
                $approverModel[] = $approver;
            }
        }
        return $approverModel;
    }

    private function approverRule2(Row $row) : array
    {
        $descriptors = $row->valueOf('employeeDescriptors');
        $lastKey = array_key_last($descriptors);
        $lastDescriptor = $descriptors[$lastKey] ?? null;
        if (is_null($lastDescriptor)) {
            return [];
        }
        $approverModel = [];
        $relatedValue['Unit'] = $lastDescriptor->getUnitId();
        $relatedValue['CompanyOrgGroup1'] = $lastDescriptor->getCompanyOrgGroupId1();
        $relatedValue['CompanyOrgGroup3'] = $lastDescriptor->getCompanyOrgGroupId3();
        foreach (UserTableFieldsEnum::APPROVER_PROCESS_IDS AS $processId) {
            foreach (UserTableFieldsEnum::PENNY_APPROVER_RELATED_MODELS AS $relatedModel => $relatedModelId) {
                $approver = new \Approver();
                $approver->process_id = $processId;
                $approver->related_model = $relatedModel;
                $approver->related_id = $relatedModelId;
                $approver->related_value = empty($relatedValue[$relatedModel]) ? 'ALL' : $relatedValue[$relatedModel];
                $approver->approver_user_id = $row->valueOf('EmployeeNumber');
                $approver->valid_from = date("Y").'-01-01';
                $approver->valid_to = UserTableFieldsEnum::VALID_TO;
                $approver->created_by = UserTableFieldsEnum::CREATED_BY;
                $approver->created_on = (new \DateTime(UserTableFieldsEnum::CREATED_ON))->format("Y-m-d");
                $approverModel[] = $approver;
            }
        }
        return $approverModel;
    }
}
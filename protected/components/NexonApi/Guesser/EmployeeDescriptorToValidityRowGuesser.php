<?php

declare(strict_types=1);

namespace Components\NexonApi\Guesser;

use Components\Core\Descriptor\ValidityRowDescriptor;
use Components\NexonApi\Descriptor\EmployeeDescriptor;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;

final class EmployeeDescriptorToValidityRowGuesser
{
    public function guess(EmployeeDescriptor $descriptorRow) :ValidityRowDescriptor
    {
        $validityRow = new ValidityRowDescriptor();
        $validityRow->setValidFrom($descriptorRow->getValidFrom());
        $validityRow->setValidTo($descriptorRow->getValidTo());
        $tempData = [];
        $tempData[EmployeeTablesFieldsEnum::ROW_ID] = $descriptorRow->getRowId();
        $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_ID] = $descriptorRow->getEmployeeId();
        $tempData[EmployeeTablesFieldsEnum::COMPANY_ID] = $descriptorRow->getCompanyId();
        $tempData[EmployeeTablesFieldsEnum::COMPANY_ORG_GROUP1_ID] = $descriptorRow->getCompanyOrgGroupId1();
        $tempData[EmployeeTablesFieldsEnum::COMPANY_ORG_GROUP2_ID] = $descriptorRow->getCompanyOrgGroupId2();
        $tempData[EmployeeTablesFieldsEnum::COMPANY_ORG_GROUP3_ID] = $descriptorRow->getCompanyOrgGroupId3();
        $tempData[EmployeeTablesFieldsEnum::UNIT_ID] = $descriptorRow->getUnitId();
        $tempData[EmployeeTablesFieldsEnum::PAYROLL_ID] = $descriptorRow->getPayrollId();
        $tempData[EmployeeTablesFieldsEnum::EMP_ID] = $descriptorRow->getEmpId();
        $tempData[EmployeeTablesFieldsEnum::TITLE] = $descriptorRow->getTitle();
        $tempData[EmployeeTablesFieldsEnum::FIRST_NAME] = $descriptorRow->getFirstName();
        $tempData[EmployeeTablesFieldsEnum::LAST_NAME] = $descriptorRow->getLastName();
        $tempData[EmployeeTablesFieldsEnum::NAMEOFBIRTH] = $descriptorRow->getNameofbirth();
        $tempData[EmployeeTablesFieldsEnum::TAX_NUMBER] = $descriptorRow->getTaxNumber();
        $tempData[EmployeeTablesFieldsEnum::GENDER] = $descriptorRow->getGender();
        $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_IMAGE] = $descriptorRow->getEmployeeImage();
        $tempData[EmployeeTablesFieldsEnum::BLACKLIST_NOTE] = $descriptorRow->getBlacklistNote();
        $tempData[EmployeeTablesFieldsEnum::STATUS] = $descriptorRow->getStatus();

        $validityRow->setValidityRowArray($tempData);

        return $validityRow;
    }


}
<?php

declare(strict_types=1);

namespace Components\NexonApi\Filter\RowFilterRules;

use Components\ETL\Enum\EtlEnum;
use Components\NexonApi\Interfaces\RowFilterRuleInterface;
use Flow\ETL\Row;

final readonly class CostCenterRule implements RowFilterRuleInterface
{
    public function matches(Row $row): bool
    {
        $costCenter = ($row)->get(EtlEnum::HR_RELATION_SHIPS_TO_PERSON)->value();
        if (empty($costCenter)) {
            return false;
        }
        $costCenterCode = array_filter(
            array_column(
                $costCenter,
                EtlEnum::COSTCENTER_RULE_COLUMN_NAME
            ),
            function ($item) {
                return !is_null($item);
            }
        );

        if (empty($costCenterCode)) {
            return false;
        }

        return (bool)preg_match(
            EtlEnum::COSTCENTER_RULE_PATTERN,
            $costCenterCode[array_key_last($costCenterCode)]
        );
    }

}
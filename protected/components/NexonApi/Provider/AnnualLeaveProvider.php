<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

use Components\NexonApi\Enum\NexonAPIExtratorEnum;
use Components\Core\Tool\ArrayTool;
use Components\Core\Transformer\JsonToArrayTransformer;

final class AnnualLeaveProvider
{
    public function __invoke(string $annualLeaveModel) : array
    {
        $annualLeaves = (new JsonToArrayTransformer)->transform($annualLeaveModel);

        return (new ArrayTool)->groupByKey($annualLeaves['value'], NexonAPIExtratorEnum::HR_RELATIONSHIP_ID);
    }
}
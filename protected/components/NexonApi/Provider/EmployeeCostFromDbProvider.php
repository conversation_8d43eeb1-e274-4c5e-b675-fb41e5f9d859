<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

final class EmployeeCostFromDbProvider
{
    public function __invoke()
    {
        $statusPublished = \Status::PUBLISHED;
        $company = new \EmployeeCost();
        $criterias = new \CDbCriteria();
        $criterias->condition = "
            t.`status` = $statusPublished
            ";
        $lines = $company->findAll($criterias);
        if (!\is_array($lines)) {
            $lines = [];
        }

        return $lines;
    }
}

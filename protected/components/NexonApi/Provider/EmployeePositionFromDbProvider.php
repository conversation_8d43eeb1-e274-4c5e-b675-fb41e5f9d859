<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

final class EmployeePositionFromDbProvider
{
    public function __invoke()
    {
        $statusPublished = \Status::PUBLISHED;
        $employeePosition = new \EmployeePosition;
        $criterias = new \CDbCriteria();
        $criterias->condition = "
            t.`status` = $statusPublished
            ";
        $lines = $employeePosition->findAll($criterias);
        if (!\is_array($lines)) {
            $lines = [];
        }

        return $lines;
    }
}

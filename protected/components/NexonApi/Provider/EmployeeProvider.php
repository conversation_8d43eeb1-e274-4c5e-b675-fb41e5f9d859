<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

final class EmployeeProvider
{
    public function __invoke()
    {
        $statusPublished = \Status::PUBLISHED;
        $employee = new \Employee;
        $criterias = new \CDbCriteria();
        $criterias->condition = "
            t.`status` = $statusPublished
            ";
        $lines = $employee->findAll($criterias);
        if (!\is_array($lines)) {
            $lines = [];
        }

        return $lines;
    }
}

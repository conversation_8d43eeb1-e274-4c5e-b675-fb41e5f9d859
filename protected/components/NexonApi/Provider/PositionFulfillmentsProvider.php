<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

use Components\NexonApi\Enum\NexonAPIExtratorEnum;
use Components\Core\Tool\ArrayTool;
use Components\Core\Transformer\JsonToArrayTransformer;

final class PositionFulfillmentsProvider
{
    public function __invoke(string $positionFulfillmentModel) : array
    {
        $positionFulfillment =(new JsonToArrayTransformer)->transform($positionFulfillmentModel);

        return (new ArrayTool)->groupByKey($positionFulfillment['value'], NexonAPIExtratorEnum::HR_RELATIONSHIP_ID);
    }
}
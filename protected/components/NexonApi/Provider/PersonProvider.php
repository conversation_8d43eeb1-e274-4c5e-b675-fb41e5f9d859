<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

use Components\NexonApi\Enum\NexonAPIExtratorEnum;
use Components\Core\Tool\ArrayTool;
use Components\Core\Transformer\JsonToArrayTransformer;

final class PersonProvider
{
    public function __invoke(string $personNameModel) : array
    {
        $persons = (new JsonToArrayTransformer())->transform($personNameModel);

        return (new ArrayTool)->indexBy($persons['value'], NexonAPIExtratorEnum::PERSON_ID);
    }
}
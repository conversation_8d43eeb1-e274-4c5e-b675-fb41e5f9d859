<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

use Components\NexonApi\Enum\NexonAPIExtratorEnum;
use Components\Core\Tool\ArrayTool;
use Components\Core\Transformer\JsonToArrayTransformer;

final class Person2LinesProvider
{
    public function __invoke(string $person2Model) : array
    {
        $person2lines = (new JsonToArrayTransformer)->transform($person2Model);

        return (new ArrayTool)->indexBy($person2lines['value'], NexonAPIExtratorEnum::PERSON_ID);
    }
}
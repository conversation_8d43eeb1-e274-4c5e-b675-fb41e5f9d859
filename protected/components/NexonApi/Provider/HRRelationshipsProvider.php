<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

use Components\NexonApi\Enum\NexonAPIExtratorEnum;
use Components\Core\Tool\ArrayTool;
use Components\Core\Transformer\JsonToArrayTransformer;

final class HRRelationshipsProvider
{
    public function __invoke(string $hrModel) : array
    {
        $allHrLines = (new JsonToArrayTransformer)->transform($hrModel);
        return (new ArrayTool)->groupByKey($allHrLines['value'], NexonAPIExtratorEnum::PERSON_ID);
    }
}
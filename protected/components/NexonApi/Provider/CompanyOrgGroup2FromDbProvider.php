<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

final class CompanyOrgGroup2FromDbProvider
{
    public function __invoke()
    {
        $statusPublished = \Status::PUBLISHED;
        $unit = new \CompanyOrgGroup2();
        $criterias = new \CDbCriteria();
        $criterias->condition = "
            t.`status` = $statusPublished
            ";
        $lines = $unit->findAll($criterias);
        if (!\is_array($lines)) {
            $lines = [];
        }

        return $lines;
    }
}

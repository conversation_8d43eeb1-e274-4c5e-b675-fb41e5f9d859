<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

final class UsersProvider
{
    public function __invoke()
    {
        $criterias = new \CDbCriteria();
        $criterias->condition = "`status` = '".\Status::PUBLISHED . "'".
            " AND CURDATE() BETWEEN `valid_from` AND `valid_to`";

        $lines = \User::model()->findAll($criterias);
        if (!\is_array($lines)) {
            $lines = [];
        }

        return array_column($lines, null, 'username');
    }
}

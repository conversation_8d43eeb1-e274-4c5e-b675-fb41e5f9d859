<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

final readonly class EmployeeExtProvider
{
    public function __invoke()
    {
        $statusPublished = \Status::PUBLISHED;
        $employee = new \EmployeeExt();
        $criteria = new \CDbCriteria();
        $criteria->condition = "
            t.`status` = $statusPublished
            ";
        $lines = $employee->findAll($criteria);
        if (!\is_array($lines)) {
            $lines = [];
        }

        return $lines;
    }
}

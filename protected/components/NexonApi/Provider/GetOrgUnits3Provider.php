<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

use Components\NexonApi\Enum\NexonAPIExtratorEnum;
use Components\Core\Tool\ArrayTool;
use Components\Core\Transformer\JsonToArrayTransformer;

final class GetOrgUnits3Provider
{
    public function __invoke(string $model) : array
    {
        $data = (new JsonToArrayTransformer)->transform($model);

        return (new ArrayTool)->groupByKey($data['value'], NexonAPIExtratorEnum::ID);
    }
}
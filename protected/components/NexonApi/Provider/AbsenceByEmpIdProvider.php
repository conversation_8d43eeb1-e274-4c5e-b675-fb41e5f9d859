<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

final class AbsenceByEmpIdProvider
{
    public function __invoke()
    {
        $statusPublished = \Status::PUBLISHED;
        $employeeAbsences = new \Employee;
        $criterias = new \CDbCriteria();
        $criterias->select = "t.emp_id, employee_base_absence.quantity, employee_base_absence.quantity_hour, employee_base_absence.valid_from";
        $criterias->join = ' INNER JOIN `employee_contract` ON `employee_contract`.`employee_id` = `t`.`employee_id`';
        $criterias->join .= ' INNER JOIN `employee_base_absence` ON `employee_base_absence`.`employee_contract_id` = `employee_contract`.`employee_contract_id`';
        $criterias->condition = "
        employee_contract.status = $statusPublished
        AND employee_base_absence.status = $statusPublished
        AND 
            t.`status` = $statusPublished
            AND CURDATE() BETWEEN t.`valid_from` AND t.`valid_to`
            AND CURDATE() BETWEEN employee_contract.`valid_from` AND employee_contract.`valid_to`
            AND CURDATE() BETWEEN employee_contract.`ec_valid_from` AND employee_contract.`ec_valid_to`
            AND CURDATE() BETWEEN employee_base_absence.`valid_from` AND employee_base_absence.`valid_to`
            ";
        $criterias->group = "t.emp_id, employee_base_absence.base_absence_type_id";
        $lines = $employeeAbsences->findAll($criterias);
        if (!\is_array($lines)) {
            $lines = [];
        }

        return array_column($lines, null, 'empid');
    }
}

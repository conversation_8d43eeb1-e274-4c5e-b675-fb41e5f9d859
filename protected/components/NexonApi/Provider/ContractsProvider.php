<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

use Components\NexonApi\Enum\NexonAPIExtratorEnum;
use Components\Core\Tool\ArrayTool;
use Components\Core\Transformer\JsonToArrayTransformer;

final class ContractsProvider
{
    public function __invoke(string $contractsModel) : array
    {
        $contracts = (new JsonToArrayTransformer)->transform($contractsModel);

        return (new ArrayTool)->groupByKey($contracts['value'], NexonAPIExtratorEnum::HR_RELATIONSHIP_ID);
    }
}
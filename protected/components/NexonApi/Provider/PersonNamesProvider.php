<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

use Components\NexonApi\Enum\NexonAPIExtratorEnum;
use Components\Core\Tool\ArrayTool;
use Components\Core\Transformer\JsonToArrayTransformer;

final class PersonNamesProvider
{
    public function __invoke(string $personNameModel) : array
    {
        $jsonToArray = new JsonToArrayTransformer();
        $allPersonNames = $jsonToArray->transform($personNameModel);
        $personNames = array_filter($allPersonNames["value"], fn ($values) => $values["PersonNameTypeId"] === 1);
        
        return (new ArrayTool)->groupByKey($personNames, NexonAPIExtratorEnum::PERSON_ID);
    }
}
<?php

namespace Components\NexonApi\Provider;

use Components\NexonApi\Enum\ConnectEnum;
use Components\NexonApi\Interfaces\DataProviderInterface;
use Guzzle<PERSON>ttp\Psr7\Request;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

final class DataProvider implements DataProviderInterface
{
    public function provide($params)
    {
        $client = new Client($this->getClientParam($params));
        $request = new Request(
            $params[ConnectEnum::REQUEST_TYPE],
            $params[ConnectEnum::HOSTNAME].$params[ConnectEnum::APICALLURL],
            [
                'X-Api-Key' => $params[ConnectEnum::APIKEY],
                'host' => $params[ConnectEnum::APIHOST],
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            $params[ConnectEnum::BODY]
        );

        try
        {
            $response = $client->send($request);
            $data = $response->getBody();

        } catch (RequestException $e) {
            $data = json_encode($e->getMessage());
        }

        return $data;
    }

    private function getClientParam(array $params) : array {
        $apiClientParam = (array)json_decode(\App::getSetting('nexonApiClientParams'));
        if (empty($apiClientParam)) {
            return [];
        }

        return [
            'base_uri' => $params[ConnectEnum::HOSTNAME],
            'headers' => [
                'Host' => $params[ConnectEnum::APIHOST],
                'Connection' => 'Keep-Alive',
                'Proxy-Connection' => 'Keep-Alive'
            ],
            'proxy' => $apiClientParam['proxy'],
            'verify' => $apiClientParam['verify']
        ];
    }
}
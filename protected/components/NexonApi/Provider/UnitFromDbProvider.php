<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

final class UnitFromDbProvider
{
    public function __invoke()
    {
        $statusPublished = \Status::PUBLISHED;
        $unit = new \Unit();
        $criterias = new \CDbCriteria();
        $criterias->condition = "
            t.`status` = $statusPublished
            ";
        $lines = $unit->findAll($criterias);
        if (!\is_array($lines)) {
            $lines = [];
        }

        return $lines;
    }
}

<?php

declare(strict_types=1);

namespace Components\NexonApi\Provider;

final class NexonAPIRawDataModelProvider
{
    public function __invoke(string $rowId): \NexonApiRawdata
    {
        $criterias = new \CDbCriteria();
        $criterias->condition = "`status` = '".\Status::PUBLISHED . "'".
            " AND `row_id` = '{$rowId}'";

        return (new \NexonApiRawdata())::model()->find($criterias);
    }

}

<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\UserTableFieldsEnum;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;


final readonly class UsersLockUpdateTransformer implements Transformer
{
    public function __construct(
        private string $cacheName
    )
    {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $rowsArray = iterator_to_array($context->cache()->read($this->cacheName));
        /** @var Rows $userRows */
        $userRows = reset($rowsArray);
        $userArrays = $userRows[0]->toArray();
        $employeeIds = [];
        foreach ($userArrays as $user) {
            $employeeIds[$user->employee_id] = $user;
        }

        foreach ($rows as $row) {
            $descriptors = $row->valueOf('employeeContractDescriptors');
            if (empty($descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT])) {
                continue;
            }
            $employeeContractDescriptors = $descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT];
            $lastEmployeeContract = array_key_last($employeeContractDescriptors);
            $descriptor = $employeeContractDescriptors[$lastEmployeeContract] ?? null;
            if ($descriptor === null) {
                continue;
            }
            $eId = $descriptor->getEmployeeId();
            if (!isset($employeeIds[$eId])) {
                continue;
            }
            $ecValidTo = $descriptor->getEcValidTo();
            if ($employeeIds[$eId]->valid_to != $ecValidTo->format("Y-m-d")) {
                \User::model()->updateByPk(
                    [UserTableFieldsEnum::ROW_ID => $employeeIds[$eId]->row_id],
                    [UserTableFieldsEnum::COLUMN_VALID_TO => $ecValidTo->format("Y-m-d")]
                );

            }
        }
        return $rows;
    }

    public function __serialize(): array
    {
        return [
            'cacheName' => $this->cacheName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->cacheName = $data['cacheName'];
    }
}

<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Row\EntryReference;
use Flow\ETL\Row\Reference;
use Flow\ETL\Row\References;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;

/**
 * @implements Transformer<array{fromEncoding: string, toEncoding: string, ref: References}>
 */
final class StringConvertIconvTransformer implements Transformer
{
    private readonly References $refs;

    public function __construct(
        private readonly string $fromEncoding,
        private readonly string $toEncoding,
        string|Reference $entry,
    ) {
        $this->refs = References::init($entry);
    }

    public function __serialize(): array
    {
        return [
            'fromEncoding' => $this->fromEncoding,
            'toEncoding' => $this->toEncoding,
            'ref' => $this->refs
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->fromEncoding = $data['fromEncoding'];
        $this->toEncoding = $data['toEncoding'];
        $this->refs = $data['ref'];
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        /** @var EntryReference $ref */
        foreach ($this->refs as $ref) {
            $transformer = function (Row $row) use ($ref): Row {
                $entry = $row->get($ref);

                return $row->set(
                    new Row\Entry\StringEntry($entry->name(), \iconv($this->fromEncoding, $this->toEncoding, $entry->toString()))
                );
            };

            $rows = $rows->map($transformer);
        }

        return $rows;
    }
}

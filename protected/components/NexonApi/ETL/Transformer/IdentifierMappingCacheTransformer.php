<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\Core\Enum\AppSettingsIdEnum;
use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\FlowContext;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;

final readonly class IdentifierMappingCacheTransformer implements Transformer
{
    public function __construct(
        private string $newCacheName = AppSettingsIdEnum::IDENTIFIER_MAPPING,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function __serialize(): array
    {
        return [
            'newCacheName' => $this->newCacheName,
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->newCacheName = $data['newCacheName'];
        $this->rowFactory = new ArrayRowFactory();
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        if ($context->cache()->has($this->newCacheName)) {
            return $rows;
        }

        $appSettingsJson = \App::getSetting(AppSettingsIdEnum::IDENTIFIER_MAPPING);
        $baseAbsenceTypeMapping = json_decode($appSettingsJson, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Invalid JSON in app settings "%s": %s',
                    AppSettingsIdEnum::IDENTIFIER_MAPPING,
                    json_last_error_msg()
                )
            );
        }

        $rowLines = [];
        $rowLines[] = $this->rowFactory->create($baseAbsenceTypeMapping);

        $context->cache()->add($this->newCacheName, new Rows(...$rowLines));

        return $rows;
    }
}
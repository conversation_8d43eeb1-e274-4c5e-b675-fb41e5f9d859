<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\FlowContext;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\EmployeeCardTableFieldsEnum;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;


final readonly class NewEmployeeCardsToModelsTransformer implements Transformer
{
    public function __construct(
        private string          $cacheName,
        private string          $newCacheName,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $rowsArray = iterator_to_array($context->cache()->read($this->cacheName));
        /** @var Rows $cardRows */
        $cardRows = reset($rowsArray);
        $cardArrays = $cardRows[0]->toArray();
        $models = [];
        $employeeContracts = [];
        foreach ($cardArrays as $card) {
            $employeeContracts[$card->employee_contract_id] = $card->card;
        }

        foreach($rows as $row) {
        
            $descriptors = $row->valueOf('employeeContractDescriptors');
            foreach($descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT] as $descriptor) {
                $ecId = $descriptor->getEmployeeContractId();
                if (isset($employeeContracts[$ecId])) {
                    continue;
                }
                $model = new \EmployeeCard();
                $model->card = $row->valueOf('EmployeeNumber');
                $model->employee_contract_id = $ecId;
                $model->card_type = EmployeeCardTableFieldsEnum::CARD_TYPE;
                $model->temporarily_blocked = EmployeeCardTableFieldsEnum::TEMPORARILY_BLOCKED;
                $model->rgnet_check_freq = EmployeeCardTableFieldsEnum::RGNET_CHECK_FREQ;
                $model->status = \Status::PUBLISHED;
                $model->valid_from = ($descriptor->getEcValidFrom())->format("Y-m-d");
                $model->valid_to = ($descriptor->getEcValidTo())->format("Y-m-d");
                $model->created_by = EmployeeCardTableFieldsEnum::CREATED_BY;
                $model->created_on = (new \DateTime(EmployeeCardTableFieldsEnum::CREATED_ON))->format("Y-m-d");
                $models[] = $model;
                $employeeContracts[$ecId] = $row->valueOf('EmployeeNumber');
            }
        }

        $rowLines[] = $this->rowFactory->create($models);
        $context->cache()->add($this->newCacheName, new Rows(...$rowLines));

        return $rows;
    }

    public function __serialize(): array
    {
        return [
            'cacheName' => $this->cacheName,
            'newCacheName' => $this->newCacheName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->cacheName = $data['cacheName'];
        $this->newCacheName = $data['newCacheName'];
    }
}

<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Components\NexonApi\Descriptor\EmployeeContractDescriptor;
use Components\NexonApi\Guesser\UserApproverGuesser;
use Flow\ETL\FlowContext;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\UserTableFieldsEnum;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;

final readonly class UsersRoleGroupUpdateTransformer implements Transformer
{
    public function __construct(
        private string          $cacheName,
        private string          $newCacheName,
        private int          $roleNumber,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    )
    {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $rowsArray = iterator_to_array($context->cache()->read($this->cacheName));
        /** @var Rows $userRows */
        $userRows = reset($rowsArray);
        $userArrays = $userRows[0]->toArray();
        $employeeIds = [];
        $approver = [];
        foreach ($userArrays as $user) {
            $employeeIds[$user->employee_id] = $user;
        }

        foreach ($rows as $row) {
            $descriptors = $row->valueOf('employeeContractDescriptors');
            if (empty($descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT])) {
                $descriptors = $row->valueOf('employeeContractFromDbDescriptors');
                $employeeContractDescriptors = $descriptors[$row->valueOf('EmployeeNumber')] ?? [];
            }
            else {
                $employeeContractDescriptors = $descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT];
            }

            $lastEmployeeContract = array_key_last($employeeContractDescriptors);
            $descriptor = $employeeContractDescriptors[$lastEmployeeContract] ?? null;
            if ($descriptor === null) {
                continue;
            }
            $eId = $descriptor->getEmployeeId();
            if (!isset($employeeIds[$eId])) {
                continue;
            }
            $roleGroupId = $this->getRoleGroupId($descriptor);
            if ($employeeIds[$eId]->rolegroup_id != $roleGroupId) {
                \User::model()->updateByPk(
                    [UserTableFieldsEnum::ROW_ID => $employeeIds[$eId]->row_id],
                    [UserTableFieldsEnum::ROLEGROUP_ID => $roleGroupId]
                );
                $attributes = ['status' => \Status::DELETED];
                $condition = 'approver_user_id=:approver_user_id AND status=:status';
                $params = [':approver_user_id' => $employeeIds[$eId]->user_id, ':status' => \Status::PUBLISHED];
                \Approver::model()->updateAll($attributes, $condition, $params);

                if ($this->hasPlanningRoleGroupId($roleGroupId)) {
                   $approver = array_merge($approver, (new UserApproverGuesser())->guess($row, $this->roleNumber));
                }
            }
        }
        $rowLines[] = $this->rowFactory->create($approver);
        $context->cache()->add($this->newCacheName, new Rows(...$rowLines));
        return $rows;
    }

    private function getRoleGroupId(EmployeeContractDescriptor $descriptor): string
    {
        if (in_array($descriptor->getEmployeeContractPositionId(), UserTableFieldsEnum::BOLTVEZETO_POSITIONS)) {
            return UserTableFieldsEnum::PLANNING_ROLEGROUP;
        }
        return UserTableFieldsEnum::DEFAULT_ROLEGROUP;
    }

    private function hasPlanningRoleGroupId($roleGroupId): bool
    {
        return $roleGroupId == UserTableFieldsEnum::PLANNING_ROLEGROUP;
    }

    public function __serialize(): array
    {
        return [
            'cacheName' => $this->cacheName,
            'newCacheName' => $this->newCacheName,
            'roleNumber' => $this->roleNumber
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->cacheName = $data['cacheName'];
        $this->newCacheName = $data['newCacheName'];
        $this->roleNumber = $data['roleNumber'];
    }
}

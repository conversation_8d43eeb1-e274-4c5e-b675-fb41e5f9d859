<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\Core\Descriptor\ValidityRowDescriptor;
use Components\Core\Transformer\ValidityRowDescriptorSortingToArrayTransformer;
use Components\NexonApi\Descriptor\EmployeeCompetencyDescriptor;
use Components\NexonApi\Enum\EmployeeCompetencyTableFieldsEnum;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;

final readonly class EmployeeCompetencyCreateDescriptorTransformer implements Transformer
{
    public function __construct(
        private string $arrayEntryName
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {

        $transformer = function (Row $row): Row {
            $descriptors = [];

            $employeeContractDescriptors = $row->valueOf('employeeContractDescriptors');
            $employeeContractPositionValidity = $this->employeeContractPositionValidity($employeeContractDescriptors);
            foreach ($employeeContractPositionValidity as $validity) {
                $competencyIds = EmployeeCompetencyTableFieldsEnum::POSITION_ASSIGNMENT_COMPETENCY[$validity[EmployeeCompetencyTableFieldsEnum::EMPLOYEE_POSITION]] ?? null;
                if (is_null($competencyIds)) {
                    continue;
                }
                foreach ($competencyIds as $competencyId) {
                    $descriptor = new EmployeeCompetencyDescriptor();
                    $descriptor->setEmployeeContractId($validity[EmployeeCompetencyTableFieldsEnum::EMPLOYEE_CONTRACT_ID]);
                    $descriptor->setCompetencyId($competencyId);
                    $descriptor->setLevelId('1');
                    $descriptor->setStatus(\Status::PUBLISHED);
                    $descriptor->setValidFrom($validity[EmployeeCompetencyTableFieldsEnum::VALID_FROM]);
                    $descriptor->setValidTo($validity[EmployeeCompetencyTableFieldsEnum::VALID_TO]);
                    if (!in_array($descriptor->getEmployeeCompetencyDataMD5Hash(), $this->getEmployeeCompetencyDescriptorDataToHash($row->valueOf('employeeCompetencyDescriptors'), $validity[EmployeeCompetencyTableFieldsEnum::EMPLOYEE_CONTRACT_ID]))) {
                        $competencyWithoutValidTo = $this->getEmployeeCompetencyDescriptorDataToHashWithoutValidTo($row->valueOf('employeeCompetencyDescriptors'), $validity[EmployeeCompetencyTableFieldsEnum::EMPLOYEE_CONTRACT_ID]);
                        if (isset($competencyWithoutValidTo[$descriptor->getEmployeeCompetencyDataMD5HashWithoutValidTo()])) {
                            \EmployeeCompetency::model()->updateByPk(
                                [EmployeeCompetencyTableFieldsEnum::ROW_ID => $competencyWithoutValidTo[$descriptor->getEmployeeCompetencyDataMD5HashWithoutValidTo()]],
                                [EmployeeCompetencyTableFieldsEnum::VALID_TO => $descriptor->getValidTo()->format("Y-m-d")]
                            );
                        }
                        else {
                            $descriptors[] = $descriptor;
                        }

                    }
                }
            }
            return $row->set(new Row\Entry\ArrayEntry(
                $this->arrayEntryName,
                $descriptors
            ));
        };

        return $rows->map($transformer);
    }

    public function __serialize(): array
    {
        return [
            'arrayEntryName' => $this->arrayEntryName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->arrayEntryName = $data['arrayEntryName'];
    }

    private function employeeContractPositionValidity(array $employeeContractDescriptors) : array
    {
        $validityRows = [];
        foreach($employeeContractDescriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT] as $descriptor) {
            $validityRow = new ValidityRowDescriptor();
            $validityRow->setValidFrom($descriptor->getValidFrom());
            $validityRow->setValidTo($descriptor->getValidTo());
            $tempData = [];
            $tempData[EmployeeCompetencyTableFieldsEnum::EMPLOYEE_POSITION] = $descriptor->getEmployeeContractPositionId();
            $tempData[EmployeeCompetencyTableFieldsEnum::EMPLOYEE_CONTRACT_ID] = $descriptor->getEmployeeContractId();
            $validityRow->setValidityRowArray($tempData);
            $validityRows[] = $validityRow;
        }
        $validityTransformer = new ValidityRowDescriptorSortingToArrayTransformer();
        return $validityTransformer->transform($validityRows);
    }

    private function getEmployeeCompetencyDescriptorDataToHash($descriptorRows, $employeeContractId): array
    {
        if (!isset($descriptorRows[$employeeContractId])) {
            return [];
        }
        $hashs = [];
        foreach ($descriptorRows[$employeeContractId] as $descriptorRow) {
            $hashs[] = $descriptorRow->getEmployeeCompetencyDataMD5Hash();
        }
        return $hashs;
    }

    private function getEmployeeCompetencyDescriptorDataToHashWithoutValidTo($descriptorRows, $employeeContractId): array
    {
        if (!isset($descriptorRows[$employeeContractId])) {
            return [];
        }
        $hashs = [];
        foreach ($descriptorRows[$employeeContractId] as $descriptorRow) {
            $hashs[$descriptorRow->getEmployeeCompetencyDataMD5HashWithoutValidTo()] = $descriptorRow->getRowId();
        }
        return $hashs;
    }
}

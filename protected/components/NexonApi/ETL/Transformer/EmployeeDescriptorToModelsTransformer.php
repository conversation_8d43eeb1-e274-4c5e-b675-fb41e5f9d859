<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;


final readonly class EmployeeDescriptorToModelsTransformer implements Transformer
{
    public function __construct(
        private string          $rowEntryName,
        private string          $sourceCacheName,
        private string          $newCacheName,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        if (empty($this->sourceCacheName)) {
            $newEmployeeModels = $this->sourceRowEntry($rows);
        }
        else {
            $newEmployeeModels = $this->sourceCache($this->sourceCacheName, $context);
        }

        $rowLines[] = $this->rowFactory->create($newEmployeeModels);
        $context->cache()->add($this->newCacheName, new Rows(
            ...$rowLines
        ));

        return $rows;
    }

    private function sourceCache(string $cacheName, FlowContext $context) :array
    {
        $rowsArray = iterator_to_array($context->cache()->read($cacheName));
        /** @var Rows $userRows */
        $employeeArray = reset($rowsArray);
        $employee = $employeeArray[0]->toArray();
        $newEmployeeModels = [];
        foreach ($employee as $descriptor) {
            $model = new \Employee($descriptor->getInsertOrUpdate());
            $model->employee_id = $descriptor->getEmployeeId();
            $model->company_id = $descriptor->getCompanyId();
            $model->company_org_group1_id = $descriptor->getCompanyOrgGroupId1();
            $model->company_org_group2_id = $descriptor->getCompanyOrgGroupId2();
            $model->company_org_group3_id = $descriptor->getCompanyOrgGroupId3();
            $model->unit_id = $descriptor->getUnitId();
            $model->payroll_id = $descriptor->getPayrollId();
            $model->emp_id = $descriptor->getEmpId();
            $model->title = $descriptor->getTitle();
            $model->first_name = $descriptor->getFirstName();
            $model->last_name = $descriptor->getLastName();
            $model->nameofbirth = $descriptor->getNameofbirth();
            $model->tax_number = $descriptor->getTaxNumber();
            $model->gender = $descriptor->getGender();
            $model->employee_image = $descriptor->getEmployeeImage();
            $model->blacklist_note = $descriptor->getBlacklistNote();
            $model->status = $descriptor->getStatus();
            $model->valid_from = $descriptor->getValidFrom()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
            $model->valid_to = $descriptor->getValidTo()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
            $model->created_by = $descriptor->getCreatedBy();
            $model->created_on = $descriptor->getCreatedOn()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
            $newEmployeeModels[] = $model;
        }
        return $newEmployeeModels;
    }

    private function sourceRowEntry(Rows $rows): array
    {
        $newEmployeeModels = [];
        /** @var Row $row */
        foreach ($rows->getIterator() as $row) {
            $descriptors = $row->valueOf($this->rowEntryName);
            foreach ($descriptors as $descriptor) {
                $model = new \Employee($descriptor->getInsertOrUpdate());
                $model->employee_id = $descriptor->getEmployeeId();
                $model->company_id = $descriptor->getCompanyId();
                $model->company_org_group1_id = $descriptor->getCompanyOrgGroupId1();
                $model->company_org_group2_id = $descriptor->getCompanyOrgGroupId2();
                $model->company_org_group3_id = $descriptor->getCompanyOrgGroupId3();
                $model->unit_id = $descriptor->getUnitId();
                $model->payroll_id = $descriptor->getPayrollId();
                $model->emp_id = $descriptor->getEmpId();
                $model->title = $descriptor->getTitle();
                $model->first_name = $descriptor->getFirstName();
                $model->last_name = $descriptor->getLastName();
                $model->nameofbirth = $descriptor->getNameofbirth();
                $model->tax_number = $descriptor->getTaxNumber();
                $model->gender = $descriptor->getGender();
                $model->employee_image = $descriptor->getEmployeeImage();
                $model->blacklist_note = $descriptor->getBlacklistNote();
                $model->status = $descriptor->getStatus();
                $model->valid_from = $descriptor->getValidFrom()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->valid_to = $descriptor->getValidTo()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->created_by = $descriptor->getCreatedBy();
                $model->created_on = $descriptor->getCreatedOn()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $newEmployeeModels[] = $model;
            }
        }
        return $newEmployeeModels;
    }

    public function __serialize(): array
    {
        return [
            'rowEntryName' => $this->rowEntryName,
            'sourceCacheName' => $this->sourceCacheName,
            'newCacheName' => $this->newCacheName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->rowEntryName = $data['rowEntryName'];
        $this->sourceCacheName = $data['sourceCacheName'];
        $this->newCacheName = $data['newCacheName'];
    }
}

<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;


final class EmployeeContractDescriptorToModelsTransformer implements Transformer
{
    public function __construct(
        private readonly string $rowEntryName,
        private readonly string $newCacheName,
        private readonly ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $contractModels = [];

        /** @var Row $row */
        foreach ($rows->getIterator() as $row) {
            $descriptors = $row->valueOf($this->rowEntryName);
            foreach ($descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT] as $contract) {
                $model = new \EmployeeContract($contract->getInsertOrUpdate());
                $model->employee_contract_id = $contract->getEmployeeContractId();
                $model->employee_id = $contract->getEmployeeId();
                $model->employee_contract_number = $contract->getEmployeeContractNumber();
                $model->ec_valid_from = $contract->getEcValidFrom()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->ec_valid_to = $contract->getEcValidto()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->employee_contract_type = $contract->getEmployeeContractType();
                $model->wage_type = $contract->getwageType();
                $model->workgroup_id = $contract->getWorkGroupId();
                $model->employee_position_id = $contract->getEmployeeContractPositionId();
                $model->daily_worktime = $contract->getDailyWorkTime();
                $model->status = $contract->getStatus();
                $model->valid_from = $contract->getValidFrom()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->valid_to = $contract->getValidTo()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->created_by = $contract->getCreatedBy();
                $model->created_on = $contract->getCreatedOn()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $contractModels[] = $model;
            }
        }

        $rowLines[] = $this->rowFactory->create($contractModels);
        $context->cache()->add($this->newCacheName, new Rows(
            ...$rowLines
        ));

        return $rows;
    }

    public function __serialize(): array
    {
        return [
            'rowEntryName' => $this->rowEntryName,
            'newCacheName' => $this->newCacheName,
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->rowEntryName = $data['rowEntryName'];
        $this->newCacheName = $data['newCacheName'];
    }
}

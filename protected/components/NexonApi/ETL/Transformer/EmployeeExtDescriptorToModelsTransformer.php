<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Components\NexonApi\Descriptor\EmployeeExtDescriptor;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;


final readonly class EmployeeExtDescriptorToModelsTransformer implements Transformer
{
    public function __construct(
        private string $rowEntryName,
        private string $newCacheName,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $newEmployeeExtModels = [];
        /** @var Row $row */
        foreach ($rows->getIterator() as $row) {
            $descriptors = $row->valueOf($this->rowEntryName);
            /** @var EmployeeExtDescriptor $descriptor */
            foreach ($descriptors as $descriptor) {
                $model = new \EmployeeExt($descriptor->getInsertOrUpdate());
                $model->employee_id = $descriptor->getEmployeeId();
                $model->ssn = $descriptor->getSsn();
                $model->status = $descriptor->getStatus();
                $model->valid_from = $descriptor->getValidFrom()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->valid_to = $descriptor->getValidTo()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->created_by = $descriptor->getCreatedBy();
                $model->created_on = $descriptor->getCreatedOn()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $newEmployeeExtModels[] = $model;
            }
        }

        $rowLines[] = $this->rowFactory->create($newEmployeeExtModels);
        $context->cache()->add(
            $this->newCacheName,
            new Rows(
                ...$rowLines
            )
        );

        return $rows;
    }

    public function __serialize(): array
    {
        return [
            'rowEntryName' => $this->rowEntryName,
            'newCacheName' => $this->newCacheName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->rowEntryName = $data['rowEntryName'];
        $this->newCacheName = $data['newCacheName'];
    }
}

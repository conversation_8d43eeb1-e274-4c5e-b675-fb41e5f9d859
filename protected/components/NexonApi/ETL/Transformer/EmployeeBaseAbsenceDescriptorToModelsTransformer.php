<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;


final readonly class EmployeeBaseAbsenceDescriptorToModelsTransformer implements Transformer
{
    public function __construct(
        private string          $rowEntryName,
        private string          $newCacheName,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $baseAbsenceModels = [];

        /** @var Row $row */
        foreach ($rows->getIterator() as $row) {
            $descriptors = $row->valueOf($this->rowEntryName);
            foreach ($descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_BASE_ABSENCE] as $baseAbsence) {
                if ($baseAbsence->getBaseAbsenceTypeId() == '') {
                    continue;
                }

                if ($baseAbsence->getInsertOrUpdate() == 'update') {
                    \EmployeeBaseAbsence::model()->updateByPk(
                        [EmployeeTablesFieldsEnum::ROW_ID => $baseAbsence->getRowId()],
                        [EmployeeTablesFieldsEnum::QUANTITY => $baseAbsence->getQuantity()]
                    );
                    continue;
                }

                $model = new \EmployeeBaseAbsence();
                $model->employee_contract_id = $baseAbsence->getEmployeeContractId();
                $model->base_absence_type_id = $baseAbsence->getBaseAbsenceTypeId();
                $model->quantity = $baseAbsence->getQuantity();
                $model->status = $baseAbsence->getStatus();
                $model->valid_from = $baseAbsence->getValidFrom()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->valid_to = $baseAbsence->getValidTo()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $model->created_by = $baseAbsence->getCreatedBy();
                $model->created_on = $baseAbsence->getCreatedOn()->format(EmployeeTablesFieldsEnum::DEFAULT_DATE_FORMAT);
                $baseAbsenceModels[] = $model;
            }
        }

        $rowLines[] = $this->rowFactory->create($baseAbsenceModels);
        $context->cache()->add($this->newCacheName, new Rows(
            ...$rowLines
        ));

        return $rows;
    }

    public function __serialize(): array
    {
        return [
            'rowEntryName' => $this->rowEntryName,
            'newCacheName' => $this->newCacheName,
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->rowEntryName = $data['rowEntryName'];
        $this->newCacheName = $data['newCacheName'];
    }
}

<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\Core\Enum\AppSettingsIdEnum;
use Components\NexonApi\Descriptor\EmployeeContractIdAndHrRelationsShipIdDescriptor;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;
use Components\NexonApi\Enum\NexonFieldsEnum;
use Components\Core\Descriptor\ValidityRowDescriptor;
use Components\Core\Transformer\ValidityRowDescriptorSortingToArrayTransformer;
use Components\NexonApi\Descriptor\EmployeeContractDescriptor;
use Components\NexonApi\Descriptor\BaseAbsenceDescriptor;

final readonly class EmployeeContractsAndAbsenceMoreLevelCreateDescriptorTransformer implements Transformer
{
    public function __construct(
        private string $arrayEntryName
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $employeePositionRows = iterator_to_array($context->cache()->read('employeePositions'));
        $baseAbsenceRows = iterator_to_array($context->cache()->read('baseAbsence'));
        $baseAbsenceMappingRows = [];

        if ($context->cache()->has(AppSettingsIdEnum::BASE_ABSENCE_TYPE_MAPPING)) {
            $baseAbsenceMappingRows = iterator_to_array($context->cache()->read(AppSettingsIdEnum::BASE_ABSENCE_TYPE_MAPPING));
        }

        $transformer = function (Row $row) use ($baseAbsenceMappingRows, $employeePositionRows, $baseAbsenceRows): Row {
            $descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT] = [];
            $descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_BASE_ABSENCE] = [];
            $employeePositionsArray = $this->rowsToArray($employeePositionRows);
            $baseAbsenceArray = $this->rowsToArray($baseAbsenceRows);
            $baseAbsenceMappingArray = $this->rowsToArray($baseAbsenceMappingRows);
            $employeeDataMd5Hashs = $this->getEmployeeDescriptorDataToHash($row->valueOf('employeeContractFromDbDescriptors'));
            $employeeDescriptors = $row->valueOf('employeeDescriptors') ?? [];
            $employeeIdValidity = $this->setValidityEmployeeId($employeeDescriptors, $row->valueOf('employeeContractFromDbDescriptors'));

            if (empty($employeeIdValidity)) {
                return $row->set(new Row\Entry\ArrayEntry(
                    $this->arrayEntryName,
                    $descriptors
                ));
            }

            $hrRelationsShipIds = $row->valueOf('hrRelationshipIdsToPerson');
            $employeeContractNumber = 0;
            foreach ($hrRelationsShipIds as $hrRelationshipId) {
                $positionFulFillments = $row->valueOf('positionFulfillmentsToPerson');
                if (!isset($positionFulFillments[$hrRelationshipId])) {
                    continue;
                }
                $employeeContractNumber++;
                $validityRows = [];

                array_push($validityRows, ...$employeeIdValidity);
                $employeeContractFromDb = $this->getEmployeeFromDb($row->valueOf('employeeContractFromDbDescriptors'), $employeeContractNumber);
                array_push($validityRows, ...$employeeContractFromDb);
                $employeeContractValidityByHrRelationShips = $this->setValidityByHrRelationShips(
                    $row->valueOf('hrRelationshipsToPerson'),
                    $employeeContractNumber
                );
                array_push($validityRows, ...$employeeContractValidityByHrRelationShips);
                $positionValidity = $this->setValidityPosition($row->valueOf('positions'), $positionFulFillments, (string)$hrRelationshipId, $employeeContractNumber, $employeePositionsArray);

                if (empty($positionValidity)) {
                    continue;
                }
                array_push($validityRows, ...$positionValidity);

                $validityArray = new ValidityRowDescriptorSortingToArrayTransformer();
                $employeeValidity = $validityArray->transform($validityRows);
                $employeeValidity = $this->setEcValidFromAndEcValidTo($employeeValidity);
                $employeeValidity = $this->setEmployeeContractId($employeeValidity);
                $employeeContractId = null;
                foreach ($employeeValidity as $key => $value) {
                    $descriptor = new EmployeeContractDescriptor();
                    $descriptor->setRowId($value[EmployeeTablesFieldsEnum::ROW_ID] ?? null);
                    $descriptor->setEmployeeContractId($value[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_ID] ?? null);
                    $descriptor->setEmployeeId($value[EmployeeTablesFieldsEnum::EMPLOYEE_ID] ?? null);
                    $descriptor->setEmployeeContractNumber($value[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_NUMBER] ?? null);
                    $descriptor->setEcValidFrom($value[EmployeeTablesFieldsEnum::EC_VALID_FROM] ?? null);
                    $descriptor->setEcValidTo($value[EmployeeTablesFieldsEnum::EC_VALID_TO] ?? null);
                    $descriptor->setEmployeeContractType($value[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_TYPE] ?? null);
                    $descriptor->setWageType($value[EmployeeTablesFieldsEnum::WAGE_TYPE] ??
                        EmployeeTablesFieldsEnum::DEFAULT_CONTRACT_WAGE_TYPE);
                    $descriptor->setWorkGroupId($value[EmployeeTablesFieldsEnum::WORKGROUP_ID] ?? null);
                    $descriptor->setEmployeeContractPositionId($value[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_POSITION_ID] ?? null);
                    $descriptor->setDailyWorkTime($value[EmployeeTablesFieldsEnum::DAILY_WORKTIME] ??
                        EmployeeTablesFieldsEnum::DEFAULT_CONTRACT_DAILY_WORKTIME);
                    $descriptor->setStatus(\Status::PUBLISHED);
                    $descriptor->setValidFrom($value[EmployeeTablesFieldsEnum::VALID_FROM] ?? null);
                    $descriptor->setValidTo($value[EmployeeTablesFieldsEnum::VALID_TO] ?? null);
                    $employeeContractId = $value[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_ID];
                    if (!in_array($descriptor->getEmployeeDataMD5Hash(), $employeeDataMd5Hashs)) {
                        $descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT][] = $descriptor;
                    }
                }

                $annualLeavePerson = $row->valueOf('annualLeavePerson')[$hrRelationshipId] ?? [];
                $annualLeaveDescriptors = $this->annualLeaveDescriptors($annualLeavePerson, $employeeContractId, $baseAbsenceMappingArray);
                $descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_BASE_ABSENCE] = $this->baseAbsenceCreateDescriptors($employeeContractId, $annualLeaveDescriptors, $baseAbsenceArray);

                $employeeContractIdAndHrRelationsShipIdDescriptor = new EmployeeContractIdAndHrRelationsShipIdDescriptor();
                $employeeContractIdAndHrRelationsShipIdDescriptor->setHrRelationShipId((string)$hrRelationshipId);
                $employeeContractIdAndHrRelationsShipIdDescriptor->setEmployeeContractId((string)$employeeContractId);
                $descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_ID_AND_HR_RELATIONS_SHIP_ID] = $employeeContractIdAndHrRelationsShipIdDescriptor;
            }

            return $row->set(new Row\Entry\ArrayEntry(
                $this->arrayEntryName,
                $descriptors
            ));
        };

        return $rows->map($transformer);
    }

    public function __serialize(): array
    {
        return [
            'arrayEntryName' => $this->arrayEntryName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->arrayEntryName = $data['arrayEntryName'];
    }

    private function getEmployeeDescriptorDataToHash(array $descriptorArray): array
    {
        $employeeDataMd5Hashs = [];
        foreach ($descriptorArray as $descriptorRows) {
            foreach ($descriptorRows as $descriptorRow) {
                $employeeDataMd5Hashs[] = $descriptorRow->getEmployeeDataMD5Hash();
            }
        }

        return $employeeDataMd5Hashs;
    }

    private function setValidityEmployeeId(array $employeeDescriptors, array $descriptorArray): array
    {
        $validityRows = [];
        $employeeId = null;
        if (!empty($employeeDescriptors)) {
            $employeeId = $employeeDescriptors[0]->getEmployeeId();
        } elseif (!empty($descriptorArray)) {
            foreach ($descriptorArray as $descriptorRows) {
                $descriptorRow = reset($descriptorRows);
                $employeeId = $descriptorRow->getEmployeeId();
                break;
            }
        }

        if (!is_null($employeeId)) {
            $validityRow = new ValidityRowDescriptor();
            $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_ID] = $employeeId;
            $validityRow->setValidityRowArray($tempData);
            $validityRows[] = $validityRow;
        }

        return $validityRows;
    }

    private function setValidityByHrRelationShips(array $hrRelationshipsToPerson, int $employeeContractNumber): array
    {
        $validityRows = [];
        foreach ($hrRelationshipsToPerson as $hrRelationship) {
            $validityRow = new ValidityRowDescriptor();
            $validityRow->setValidFrom($hrRelationship[NexonFieldsEnum::VALID_FROM]);
            $validityRow->setValidTo($hrRelationship[NexonFieldsEnum::VALID_TO]);
            $tempData = [];
            $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_TYPE] = EmployeeTablesFieldsEnum::DEFAULT_CONTRACT_TYPE;
            $tempData[EmployeeTablesFieldsEnum::WAGE_TYPE] = EmployeeTablesFieldsEnum::DEFAULT_CONTRACT_WAGE_TYPE;
            $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_NUMBER] = $employeeContractNumber;
            $validityRow->setValidityRowArray($tempData);

            $validityRows[] = $validityRow;
        }
        return $validityRows;
    }

    private function setValidityPosition(array $positions, array $positionFulFillments, string $hrRelationShipId, int $employeeContractNumber, array $employeePositionsArray): array
    {
        $validityRows = [];

        foreach ($positionFulFillments[$hrRelationShipId] as $positionFulFillment) {
            $company = $positions[$hrRelationShipId][$positionFulFillment[NexonFieldsEnum::POSITION_ID]][NexonFieldsEnum::LEVEL] ?? [];
            $position = $positions[$hrRelationShipId][$positionFulFillment[NexonFieldsEnum::POSITION_ID]][NexonFieldsEnum::POSITION_NAME][0][NexonFieldsEnum::VALUE] ?? [];
            if (empty($position) || empty($company)) {
                continue;
            }

            $validityRow = new ValidityRowDescriptor();
            $tempData = [];
            $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_POSITION_ID] = array_search($position, $employeePositionsArray);
            $tempData[EmployeeTablesFieldsEnum::DAILY_WORKTIME] = $positionFulFillment[NexonFieldsEnum::WORK_IN_HOUR];
            $tempData[EmployeeTablesFieldsEnum::WORKGROUP_ID] = array_key_exists($positionFulFillment[NexonFieldsEnum::WORK_IN_HOUR], EmployeeTablesFieldsEnum::WORKGROUPS_ROSSMAN) ? EmployeeTablesFieldsEnum::WORKGROUPS_ROSSMAN[$positionFulFillment[NexonFieldsEnum::WORK_IN_HOUR]] : null;
            $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_TYPE] = EmployeeTablesFieldsEnum::DEFAULT_CONTRACT_TYPE;
            $tempData[EmployeeTablesFieldsEnum::WAGE_TYPE] = EmployeeTablesFieldsEnum::DEFAULT_CONTRACT_WAGE_TYPE;
            $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_NUMBER] = $employeeContractNumber;
            $validityRow->setValidFrom($positionFulFillment[NexonFieldsEnum::VALID_FROM]);
            $validityRow->setValidTo($positionFulFillment[NexonFieldsEnum::VALID_TO]);
            $validityRow->setValidityRowArray($tempData);
            $validityRows[] = $validityRow;
        }


        return $validityRows;
    }

    private function getEmployeeFromDb(array $descriptorArray, int $employeeContractNumber): array
    {
        $validityRows = [];
        foreach ($descriptorArray as $descriptorRows) {
            foreach ($descriptorRows as $descriptorRow) {
                if ($descriptorRow->getEmployeeContractNumber() != $employeeContractNumber) {
                    continue;
                }
                $validityRow = new ValidityRowDescriptor();
                $validityRow->setValidFrom($descriptorRow->getValidFrom());
                $validityRow->setValidTo($descriptorRow->getValidTo());
                $tempData = [];
                $tempData[EmployeeTablesFieldsEnum::ROW_ID] = $descriptorRow->getRowId();
                $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_ID] = $descriptorRow->getEmployeeContractId();
                $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_ID] = $descriptorRow->getEmployeeId();
                $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_NUMBER] = $descriptorRow->getEmployeeContractNumber();
                $tempData[EmployeeTablesFieldsEnum::EC_VALID_FROM] = $descriptorRow->getEcValidFrom();
                $tempData[EmployeeTablesFieldsEnum::EC_VALID_TO] = $descriptorRow->getEcValidTo();
                $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_TYPE] = $descriptorRow->getEmployeeContractType();
                $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_POSITION_ID] = $descriptorRow->getEmployeeContractPositionId();
                $tempData[EmployeeTablesFieldsEnum::WAGE_TYPE] = $descriptorRow->getWageType();
                $tempData[EmployeeTablesFieldsEnum::WORKGROUP_ID] = $descriptorRow->getWorkGroupId();
                $tempData[EmployeeTablesFieldsEnum::DAILY_WORKTIME] = $descriptorRow->getDailyWorkTime();
                $tempData[EmployeeTablesFieldsEnum::STATUS] = $descriptorRow->getStatus();

                $validityRow->setValidityRowArray($tempData);
                $validityRows[] = $validityRow;
            }
        }

        return $validityRows;
    }

    private function rowsToArray($rows)
    {
        $result = [];
        foreach ($rows as $row) {
            $result = $row->toArray();
        }
        return $result[0];
    }

    private function setEcValidFromAndEcValidTo(array $employeeValidity): array
    {
        $result = [];
        if (empty($employeeValidity)) {
            return $result;
        }

        $validFrom = array_column($employeeValidity, EmployeeTablesFieldsEnum::VALID_FROM);
        $validTo = array_column($employeeValidity, EmployeeTablesFieldsEnum::VALID_TO);
        $result = [];
        foreach ($employeeValidity as $row) {
            $row[EmployeeTablesFieldsEnum::EC_VALID_FROM] = min($validFrom);
            $row[EmployeeTablesFieldsEnum::EC_VALID_TO] = max($validTo);
            $result[] = $row;
        }
        return $result;
    }

    private function setEmployeeContractId(array $employeeValidity): array
    {
        $result = [];
        if (empty($employeeValidity)) {
            return $result;
        }
        $employeeContractIds = array_column($employeeValidity, EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_ID);
        if (empty($employeeContractIds)) {
            $employeeContractIds[0] = $employeeValidity[0][EmployeeTablesFieldsEnum::EMPLOYEE_ID] . $employeeValidity[0][EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_NUMBER];
        }

        foreach ($employeeValidity as $row) {
            $row[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_ID] = $employeeContractIds[0];
            $result[] = $row;
        }
        return $result;
    }

    private function annualLeaveDescriptors(array $annualLeavePerson, string|null $employeeContractId, array $baseAbsenceMappingArray): array
    {
        if (is_null($employeeContractId) || empty($employeeContractId)) {
            return [];
        }
        $annualLeaveDescriptors = [];
        foreach ($annualLeavePerson as $annualLeave) {
            if (!array_key_exists($annualLeave[EmployeeTablesFieldsEnum::BASE_ABSENCE_TYPE], $baseAbsenceMappingArray)) {
                continue;
            }
            $descriptor = new BaseAbsenceDescriptor();
            $descriptor->setEmployeeContractId($employeeContractId);
            $descriptor->setBaseAbsenceTypeId($baseAbsenceMappingArray[$annualLeave[EmployeeTablesFieldsEnum::BASE_ABSENCE_TYPE]] ?? null);
            $descriptor->setQuantity($annualLeave[EmployeeTablesFieldsEnum::CALCDAYS]);
            $descriptor->setValidFrom(new \Datetime($annualLeave[EmployeeTablesFieldsEnum::YEAR] . '-01-01'));
            $descriptor->setValidTo(new \Datetime($annualLeave[EmployeeTablesFieldsEnum::YEAR] . '-12-31'));
            $descriptor->setStatus(\Status::PUBLISHED);
            $annualLeaveDescriptors[] = $descriptor;
        }
        return $annualLeaveDescriptors;
    }

    private function baseAbsenceArrayHashKey(array $descriptors): array
    {
        $result = [];
        foreach ($descriptors as $descriptor) {
            $result[$descriptor->getBaseAbsenceOnlyStateTypeMD5Hash()] = $descriptor;
        }
        return $result;
    }

    private function baseAbsenceCreateDescriptors(string|null $employeeContractId, array $annualLeaveDescriptors, array $baseAbsenceArray): array
    {
        if (is_null($employeeContractId) || empty($employeeContractId)) {
            return [];
        }
        $result = [];
        if (!isset($baseAbsenceArray[$employeeContractId])) {
            array_push($result, ...$annualLeaveDescriptors);
            return $result;
        }

        $baseAbsenceArrayHashKey = $this->baseAbsenceArrayHashKey($baseAbsenceArray[$employeeContractId]);
        foreach ($annualLeaveDescriptors as $annualLeaveDescriptor) {
            if (!array_key_exists($annualLeaveDescriptor->getBaseAbsenceOnlyStateTypeMD5Hash(), $baseAbsenceArrayHashKey)) {
                $result[] = $annualLeaveDescriptor;
                continue;
            }

            $oldBaseAbsence = $baseAbsenceArrayHashKey[$annualLeaveDescriptor->getBaseAbsenceOnlyStateTypeMD5Hash()];
            if ($annualLeaveDescriptor->getBaseAbsenceMD5Hash() != $oldBaseAbsence->getBaseAbsenceMD5Hash()) {
                $oldBaseAbsence->setQuantity($annualLeaveDescriptor->getQuantity());
                $result[] = $oldBaseAbsence;
            }
        }
        return $result;
    }
}

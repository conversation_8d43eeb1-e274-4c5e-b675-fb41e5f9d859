<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Flow\ETL\Exception\RuntimeException;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\ETL\Enum\MathEnum;

/**
 * @implements Transformer<array{array_entry_name: string, left_value: string, right_value: int|float, operation: MathEnum|string, new_array_value_name: string}>
 */
final readonly class ArrayValueMathOperationTransformer implements Transformer
{
    public function __construct(
        private string    $arrayEntryName,
        private string    $leftValue,
        private int|float $rightValue,
        private string    $operation,
        private string    $newArrayValueName
    ) {
    }

    public function __serialize(): array
    {
        return [
            'array_entry_name' => $this->arrayEntryName,
            'left_value' => $this->leftValue,
            'right_value' => $this->rightValue,
            'operation' => $this->operation,
            'new_array_value_name' => $this->newArrayValueName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->arrayEntryName = $data['array_entry_name'];
        $this->leftValue = $data['left_value'];
        $this->rightValue = $data['right_value'];
        $this->operation = $data['operation'];
        $this->newArrayValueName = $data['new_array_value_name'];
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $transformer = function (Row $row): Row {
            if (!$row->entries()->has($this->arrayEntryName)) {
                throw new RuntimeException("\"{$this->arrayEntryName}\" not found");
            }

            if (!$row->entries()->get($this->arrayEntryName) instanceof Row\Entry\ArrayEntry) {
                throw new RuntimeException("\"{$this->arrayEntryName}\" is not ArrayEntry");
            }

            $arrayEntry = $row->get($this->arrayEntryName);

            /** @var array<mixed> $array */
            $array = $arrayEntry->value();
            foreach ($array as $arrayKey => $arrayValues) {
                foreach ($arrayValues as $key => $value) {
                    $operation = \is_string($this->operation)
                        ? MathEnum::from($this->operation)
                        : $this->operation;

                    $resultValue = match ($operation) {
                        MathEnum::add => $value[$this->leftValue] + $this->rightValue,
                        MathEnum::subtract => $value[$this->leftValue] - $this->rightValue,
                        MathEnum::multiply => $value[$this->leftValue] * $this->rightValue,
                        MathEnum::divide => $value[$this->leftValue] / $this->rightValue,
                        MathEnum::modulo => $value[$this->leftValue] % $this->rightValue,
                        MathEnum::power => $value[$this->leftValue] ** $this->rightValue,
                    };

                    $array[$arrayKey][$key][$this->newArrayValueName] = $resultValue;
                }
            }

            return $row->set(new Row\Entry\ArrayEntry(
                $arrayEntry->name(),
                $array
            ));
        };

        return $rows->map($transformer);
    }
}

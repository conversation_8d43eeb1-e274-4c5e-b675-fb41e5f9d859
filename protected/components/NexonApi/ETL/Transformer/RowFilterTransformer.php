<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\NexonApi\Interfaces\RowFilterRuleInterface;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;

final class RowFilterTransformer implements Transformer
{
    private RowFilterRuleInterface $rule;

    public function __construct(RowFilterRuleInterface $rule)
    {
        $this->rule = $rule;
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $rule = $this->rule;
        return $rows->filter(fn(Row $row) => $rule->matches($row));
    }

}
<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Components\NexonApi\Descriptor\EmployeeCostDescriptor;
use Components\NexonApi\Enum\EmployeeCostTableFieldsEnum;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;
use Components\NexonApi\Enum\NexonFieldsEnum;
use Components\Core\Descriptor\ValidityRowDescriptor;
use Components\Core\Transformer\ValidityRowDescriptorSortingToArrayTransformer;
use Components\NexonApi\Descriptor\EmployeeDescriptor;

final readonly class EmployeeCostCreateDescriptorTransformer implements Transformer
{
    public function __construct(
        private string $arrayEntryName
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $employeeCost = iterator_to_array($context->cache()->read('employeeCosts'));
        $costsRows = iterator_to_array($context->cache()->read('costs'));
        $costCentersRows = iterator_to_array($context->cache()->read('costCenters'));
        $transformer = function (Row $row) use ($employeeCost, $costsRows, $costCentersRows): Row {
            $descriptors = [];

            $employeeCostArray = $this->rowsToArray($employeeCost);
            $employeeCostHashs = $this->getEmployeeCostDescriptorDataToHash($employeeCostArray);
            $costs = $this->rowsToArray($costsRows);
            $costCenters = $this->rowsToArray($costCentersRows);
            $hrRelationsShipIds = $row->valueOf('hrRelationshipIdsToPerson');
            foreach ($hrRelationsShipIds as $hrRelationshipId) {
                $positionFulFillments = $row->valueOf('positionFulfillmentsToPerson');

                if (!isset($positionFulFillments[$hrRelationshipId])) {
                    continue;
                }
                $employeeContractDescriptors = $row->valueOf('employeeContractDescriptors');
                if (!isset($employeeContractDescriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_ID_AND_HR_RELATIONS_SHIP_ID])) {
                    continue;
                }
                $employeeContractAndHrRelationShipId = $employeeContractDescriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT_ID_AND_HR_RELATIONS_SHIP_ID];

                $validityRows = [];

                $employeeCostFromDb = $this->getEmployeeCostFromDb($employeeCostArray, $employeeContractAndHrRelationShipId->getEmployeeContractId());
                array_push($validityRows, ...$employeeCostFromDb);

                $employeeCostValidity = $this->setValidityCostCostCenter($row->valueOf('positions'), $row->valueOf('positionFulfillmentsToPerson'), (string)$hrRelationshipId, $costs, $costCenters, $employeeContractAndHrRelationShipId->getEmployeeContractId());
                array_push($validityRows, ...$employeeCostValidity);

                $employeeCostValidityArray = new ValidityRowDescriptorSortingToArrayTransformer();
                $employeeCostValidity = $employeeCostValidityArray->transform($validityRows);

                foreach ($employeeCostValidity as $key => $value) {
                    $descriptor = new EmployeeCostDescriptor();
                    $descriptor->setRowId($value[EmployeeCostTableFieldsEnum::ROW_ID] ?? null);
                    $descriptor->setEmployeeContractId((string)$value[EmployeeCostTableFieldsEnum::EMPLOYEE_CONTRACT_ID]);
                    $descriptor->setCostId((string)$value[EmployeeCostTableFieldsEnum::COST_ID]);
                    $descriptor->setCostCenterId((string)$value[EmployeeCostTableFieldsEnum::COST_CENTER_ID]);
                    $descriptor->setStatus(\Status::PUBLISHED);
                    $descriptor->setValidFrom($value[EmployeeTablesFieldsEnum::VALID_FROM] ?? null);
                    $descriptor->setValidTo($value[EmployeeTablesFieldsEnum::VALID_TO] ?? null);

                    if (!in_array($descriptor->getEmployeeCostDataMD5Hash(), $employeeCostHashs)) {
                        $descriptors[] = $descriptor;
                    }
                }
            }
            return $row->set(new Row\Entry\ArrayEntry(
                $this->arrayEntryName,
                $descriptors
            ));
        };

        return $rows->map($transformer);
    }

    public function __serialize(): array
    {
        return [
            'arrayEntryName' => $this->arrayEntryName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->arrayEntryName = $data['arrayEntryName'];
    }

    private function setValidityCostCostCenter(array $positions, array $positionFulFillments, string $hrRelationShipId, array $cost, array $costCenter, string $employeeContractId): array
    {
        $validityRows = [];

        foreach ($positionFulFillments[$hrRelationShipId] as $positionFulFillment) {
            $company = $positions[$hrRelationShipId][$positionFulFillment[NexonFieldsEnum::POSITION_ID]][NexonFieldsEnum::LEVEL][5] ?? [];
            if (empty($company)) {
                continue;
            }
            $validityRow = new ValidityRowDescriptor();
            $tempData = [];
            $tempData['cost_id'] = array_search($company[NexonFieldsEnum::CODE] . ' ' . $company[NexonFieldsEnum::NAME][0][NexonFieldsEnum::VALUE], $cost);
            $tempData['cost_center_id'] = array_search($company[NexonFieldsEnum::CODE] . ' ' . $company[NexonFieldsEnum::NAME][0][NexonFieldsEnum::VALUE], $costCenter);
            $tempData['employee_contract_id'] = $employeeContractId;
            $validityRow->setValidFrom($positionFulFillment[NexonFieldsEnum::VALID_FROM]);
            $validityRow->setValidTo($positionFulFillment[NexonFieldsEnum::VALID_TO]);
            $validityRow->setValidityRowArray($tempData);
            $validityRows[] = $validityRow;
        }
        return $validityRows;
    }

    private function getEmployeeCostFromDb(array $employeeCostDescriptorRows, string $employeeContractId) : array
    {
        $validityRows = [];
        if (empty($employeeContractDescriptorRows)) {
            return $validityRows;
        }

        foreach ($employeeCostDescriptorRows as $descriptorRow)
        {
            if ($descriptorRow->getEmployeeContractId() != $employeeContractId) {
                continue;
            }
            $validityRow = new ValidityRowDescriptor();
            $validityRow->setValidFrom($descriptorRow->getValidFrom());
            $validityRow->setValidTo($descriptorRow->getValidTo());
            $tempData = [];
            $tempData[EmployeeCostTableFieldsEnum::ROW_ID] = $descriptorRow->getRowId();
            $tempData[EmployeeCostTableFieldsEnum::EMPLOYEE_CONTRACT_ID] = $descriptorRow->getEmployeeContractId();
            $tempData[EmployeeCostTableFieldsEnum::COST_ID] = $descriptorRow->getCostId();
            $tempData[EmployeeCostTableFieldsEnum::COST_CENTER_ID] = $descriptorRow->getCostCenterId();
            $tempData[EmployeeTablesFieldsEnum::STATUS] = $descriptorRow->getStatus();
            $validityRow->setValidityRowArray($tempData);
            $validityRows[] = $validityRow;
        }

        return $validityRows;
    }

    private function getEmployeeCostDescriptorDataToHash(array $descriptorArray): array
    {
        $employeeDataMd5Hashs = [];
        foreach ($descriptorArray as $descriptorRows) {
            foreach ($descriptorRows as $descriptorRow) {
                $employeeDataMd5Hashs[] = $descriptorRow->getEmployeeCostDataMD5Hash();
            }
        }

        return $employeeDataMd5Hashs;
    }

    private function rowsToArray($rows)
    {
        $result = [];
        foreach ($rows as $row) {
            $result = $row->toArray();
        }
        return $result[0];
    }
}

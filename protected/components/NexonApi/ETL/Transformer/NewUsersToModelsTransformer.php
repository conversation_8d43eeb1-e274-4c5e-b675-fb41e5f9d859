<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Components\NexonApi\Descriptor\EmployeeContractDescriptor;
use Components\NexonApi\Guesser\UserApproverGuesser;
use Components\NexonApi\Guesser\UserRoleGroupRolesGuesser;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\UserTableFieldsEnum;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;
use Transliterator;


final readonly class NewUsersToModelsTransformer implements Transformer
{
    public function __construct(
        private string          $cacheName,
        private string          $newCacheName,
        private int          $roleNumber,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    )
    {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $rowsArray = iterator_to_array($context->cache()->read($this->cacheName));
        /** @var Rows $userRows */
        $userRows = reset($rowsArray);
        $userArrays = $userRows[0]->toArray();
        $models = [];
        $employeeIds = [];
        foreach ($userArrays as $user) {
            $employeeIds[$user->employee_id] = $user->username;
        }

        foreach ($rows as $row) {
            $descriptors = $row->valueOf('employeeContractDescriptors');
            if (empty($descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT])) {
                continue;
            }
            $employeeContractDescriptors = $descriptors[EmployeeTablesFieldsEnum::EMPLOYEE_CONTRACT];
            $lastEmployeeContract = array_key_last($employeeContractDescriptors);
            $descriptor = $employeeContractDescriptors[$lastEmployeeContract] ?? null;
            if ($descriptor === null) {
                continue;
            }
            $eId = $descriptor->getEmployeeId();
            if (isset($employeeIds[$eId])) {
                continue;
            }
            $roleGroupRoles = (new UserRoleGroupRolesGuesser())->guess($descriptor, $this->roleNumber);
            $model = new \User();
            $model->user_id = $row->valueOf('EmployeeNumber');
            $model->employee_id = $eId;
            $model->username = $this->createUsername($employeeIds, $row);
            $model->password = hash('sha512', $row->valueOf(UserTableFieldsEnum::DEFAULT_PASSWORD));
            $model->password_date = (new \DateTime(UserTableFieldsEnum::CREATED_ON))->format("Y-m-d H:i:s");
            $model->receive_email = 0;
            $model->rolegroup_id = $roleGroupRoles[0];
            $model->status = \Status::PUBLISHED;
            $model->valid_from = ($descriptor->getValidFrom())->format("Y-m-d");
            $model->valid_to = UserTableFieldsEnum::VALID_TO;
            $model->created_by = UserTableFieldsEnum::CREATED_BY;
            $model->created_on = (new \DateTime(UserTableFieldsEnum::CREATED_ON))->format("Y-m-d");
            $models[] = $model;
            if ($roleGroupRoles[1]) {
                $approver = (new UserApproverGuesser())->guess($row, $this->roleNumber);
                $models = array_merge($models, $approver);
            }
        }

        $rowLines[] = $this->rowFactory->create($models);
        $context->cache()->add($this->newCacheName, new Rows(...$rowLines));

        return $rows;
    }

    private function createUsername(array $employeeIds, Row $row): string
    {
        $name = $row->valueOf('FirstName') . '.' . $row->valueOf('LastName');
        $transliterator = Transliterator::createFromRules(UserTableFieldsEnum::USERNAME_STRING_RULE, Transliterator::FORWARD);
        $userName = $transliterator->transliterate($name);

        $originalUserName = $userName;
        $userNumber = 0;
        while (array_search($userName, $employeeIds)) {
            $userName = $originalUserName;
            $userNumber++;
            $userName .= (string)$userNumber;
        }
        return str_replace(' ', '.', $userName);
    }

    public function __serialize(): array
    {
        return [
            'cacheName' => $this->cacheName,
            'newCacheName' => $this->newCacheName,
            'roleNumber' => $this->roleNumber
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->cacheName = $data['cacheName'];
        $this->newCacheName = $data['newCacheName'];
        $this->roleNumber = $data['roleNumber'];
    }
}

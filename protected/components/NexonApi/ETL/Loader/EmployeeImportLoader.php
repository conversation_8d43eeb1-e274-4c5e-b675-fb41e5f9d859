<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Loader;

use Flow\ETL\FlowContext;
use Flow\ETL\Loader;
use Flow\ETL\Rows;

final class EmployeeImportLoader implements Loader
{
    public function load(Rows $rows, FlowContext $context): void
    {
        $employeeRowsArray = iterator_to_array($context->cache()->read('employees'));
        /** @var Rows $employeeRows */
        $employeeRows = reset($employeeRowsArray);
        $employees = $employeeRows->reduceToArray('employee');

    }

    public function __serialize(): array
    {
        throw new \Exception('Not implemented!');
    }

    public function __unserialize(array $data): void
    {
        throw new \Exception('Not implemented!');
    }
}

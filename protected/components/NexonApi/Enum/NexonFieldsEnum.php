<?php

declare(strict_types=1);

namespace Components\NexonApi\Enum;

final class NexonFieldsEnum
{
    public const POSITION_ID = 'PositionId';
    public const LEVEL = 'Level';
    public const CODE = 'Code';
    public const NAME = 'Name';
    public const VALUE = 'Value';
    public const VALID_FROM = 'ValidFrom';
    public const VALID_TO = 'ValidTo';
    public const HRRELATIONSHIPIDS = 'HrRelationshipIds';
    public const ORGANIZATIONUNITID = 'OrganizationUnitId';
    public const PARENTLEVEL = 'ParentLevel';
    public const PARENTID = 'ParentId';
    public const NEXON_LEVEL_6 = 6;
    public const NEXON_LEVEL_5 = 5;
    public const NEXON_LEVEL_4 = 4;
    public const NEXON_LEVEL_3 = 3;
    public const NEXON_LEVEL_2 = 2;
    public const NEXON_LEVEL_1 = 1;
    public const POSITION_NAME = 'PositionName';
    public const WORK_IN_HOUR = 'WorkInHour';
    public const FORENAME = 'Forename';
    public const FAMILYNAME = 'FamilyName';
    public const DISTINCTIVE_TAG = 'DistinctiveTag';
    public const POSITIONFULFILLMENTTYPE_DICTIONARYITEMID = 'PositionFulfillmentType_DictionaryItemId';

    
}
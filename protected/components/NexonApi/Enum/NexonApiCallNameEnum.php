<?php

declare(strict_types=1);

namespace Components\NexonApi\Enum;

final class NexonApiCallNameEnum
{
    public const COMPANY = 'company';
    public const HR_RELATIONSHIP = 'hr-relationship';
    public const HR_RELATIONSHIP_DICTIONARY = 'hr-relationship-dictionary';
    public const GET_WORK_SCHEDULES = 'get-work-schedules';
    public const GET_POSITIONS = 'get-positions';
    public const JOB_TYPE = 'job-type';
    public const ORGANIZATION_UNIT = 'organization-unit';
    public const PERSON_NAME = 'person-name';
    public const LANGUAGE_PROFICIENCY = 'language-proficiency';
    public const POSITION_FULFILLMENT = 'position-fulfillment';
    public const POSITION_FULFILLMENT_DICTIONARY = 'position-fulfillment-dictionary';
    public const MONTHLY_COMPANY_LEVEL_DATA = 'monthly-company-level-data';
    public const EMPLOYMENT_CONTRACT = 'employment-contract';
    public const EMPLOYMENT_CONTRACT_VERSION = 'employment-contract-version';
    public const USERS = 'users';
    public const EMPLOYEE_MANAGER = 'employee-manager';
    public const PERSON_COMPANY_GROUP_MEMBER_IDENTIFIER = 'person-company-group-member-identifier';
    public const FEOR_DATA = 'feor-data';
    public const DATE_OF_REACHING_RETIREMENT_AGE_LIMIT = 'date-of-reaching-retirement-age-limit';
    public const GET_ABSENCE_TYPES = 'get-absence-types';
    public const YEARLY_HR_RELATIONSHIP_DATA = 'yearly-hr-relationship-data';
    public const LONG_ABSENCES = 'long-absences';
    public const JOB = 'job';
    public const GET_PERSONS_2 = 'get-persons-2';
    public const PERSON = 'person';
    public const ADDRESS = 'address';
    public const GET_CONTACTS = 'get-contacts';
    public const HISZTSZOTARADATLEIR = 'hiszt-szotar-adat-leir';
    public const POSITION_NAME_DICTIONARY = 'position_name_dictionary';
    public const ANNUAL_LEAVE = 'annual_leave';
    public const EGYSEG_DICTIONARY = 'egyseg_dictionary';
    public const EGYSEG_HISZTSZOTARADAT = 'egyseg_hisztszotaradat';
    public const GET_ORG_UNITS3 = 'get-org-units3';
    public const EMPLOYEE_CONTRACT_TYPE_DICTIONARY = 'employee_contact_type_dictionary';
    public const GET_LONG_ABSENCE = 'get_long_absence';
}

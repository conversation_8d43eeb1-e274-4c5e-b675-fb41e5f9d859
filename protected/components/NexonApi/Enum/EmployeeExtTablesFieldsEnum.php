<?php

declare(strict_types=1);

namespace Components\NexonApi\Enum;

final class EmployeeExtTablesFieldsEnum
{
    public const ROW_ID = 'row_id';
    public const EMPLOYEE_ID = 'employee_id';
    public const PLACE_OF_BIRTH = 'place_of_birth';
    public const DATE_OF_BIRTH = 'date_of_birth';
    public const MOTHERS_NAME = 'mothers_name';
    public const SSN = 'ssn';
    public const PERSONAL_ID_CARD_NUMBER = 'personal_id_card_number';
    public const PASSPORT_NUMBER = 'passport_number';
    public const OPTION1 = 'option1';
    public const OPTION2 = 'option2';
    public const OPTION3 = 'option3';
    public const OPTION4 = 'option4';
    public const OPTION5 = 'option5';
    public const OPTION6 = 'option6';
    public const OPTION7 = 'option7';
    public const OPTION8 = 'option8';
    public const OPTION9 = 'option9';
    public const OPTION10 = 'option10';
    public const OPTION11 = 'option11';
    public const OPTION12 = 'option12';
    public const OPTION13 = 'option13';
    public const OPTION14 = 'option14';
    public const OPTION15 = 'option15';
    public const OPTION16 = 'option16';
    public const OPTION17 = 'option17';
    public const OPTION18 = 'option18';
    public const OPTION19 = 'option19';
    public const OPTION20 = 'option20';
    public const OPTION21 = 'option21';
    public const OPTION22 = 'option22';
    public const OPTION23 = 'option23';
    public const OPTION24 = 'option24';
    public const OPTION25 = 'option25';
    public const OPTION26 = 'option26';
    public const OPTION27 = 'option27';
    public const OPTION28 = 'option28';
    public const OPTION29 = 'option29';
    public const OPTION30 = 'option30';
    public const OPTION31 = 'option31';
    public const OPTION32 = 'option32';
    public const OPTION33 = 'option33';
    public const OPTION34 = 'option34';
    public const OPTION35 = 'option35';
    public const OPTION36 = 'option36';
    public const OPTION37 = 'option37';
    public const OPTION38 = 'option38';
    public const OPTION39 = 'option39';
    public const OPTION40 = 'option40';
    public const STATUS = 'status';
    public const VALID_FROM = 'valid_from';
    public const VALID_TO = 'valid_to';
    public const CREATED_BY = 'created_by';
    public const CREATED_ON = 'created_on';
}

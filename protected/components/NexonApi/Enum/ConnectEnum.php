<?php


namespace Components\NexonApi\Enum;

final class ConnectEnum
{
    public const REQUEST_TYPE = 'request_type';
    public const WORKFLOW = 'workflow';
    public const APICALLURL = 'apiCallUrl';
    public const BODY = 'body';
    public const PERSONALIDS = 'personalIds';
    public const HOSTNAME = 'hostname';
    public const APIKEY = 'apiKey';
    public const APIHOST = 'apiHost';
    public const PROCESS_ID = 'nexonapiprocessid';
}
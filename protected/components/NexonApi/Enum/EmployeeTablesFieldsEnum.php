<?php

declare(strict_types=1);

namespace Components\NexonApi\Enum;

final class EmployeeTablesFieldsEnum
{
    public const DEFAULT_CONTRACT_TYPE = 1;
    public const DEFAULT_CONTRACT_WAGE_TYPE = 'MoLo';
    public const DEFAULT_CONTRACT_DAILY_WORKTIME = '8';
    public const DEFAULT_CREATED_BY = 'NexonImport';
    public const DEFAULT_CREATED_ON = 'now';
    public const DEFAULT_DATE_FORMAT = 'Y-m-d';
    public const DEFAULT_VALID_TO = '2038-01-01';
    public const DEFAULT_INSERT_OR_UPDATE = 'insert';
    public const ROW_ID = 'row_id';
    public const EMPLOYEE_ID = 'employee_id';
    public const COMPANY_ID = 'company_id';
    public const COMPANY_NAME = 'company_name';
    public const COMPANY_ORG_GROUP1_ID = 'company_org_group1_id';
    public const COMPANY_ORG_GROUP2_ID = 'company_org_group2_id';
    public const COMPANY_ORG_GROUP3_ID = 'company_org_group3_id';
    public const UNIT_ID = 'unit_id';
    public const PAYROLL_ID = 'payroll_id';
    public const EMP_ID = 'emp_id';
    public const TITLE = 'title';
    public const FIRST_NAME = 'first_name';
    public const LAST_NAME = 'last_name';
    public const NAMEOFBIRTH = 'nameofbirth';
    public const TAX_NUMBER = 'tax_number';
    public const GENDER = 'gender';
    public const EMPLOYEE_IMAGE = 'employee_image';
    public const BLACKLIST_NOTE = 'blacklist_note';
    public const VALID_FROM = 'valid_from';
    public const VALID_TO = 'valid_to';
    public const STATUS = 'status';
    public const CREATED_BY = 'created_by';
    public const CREATED_ON = 'created_on';
    public const MODIFIED_BY = 'modified_by';
    public const MODIFIED_ON = 'modified_on';
    public const EMPLOYEE_CONTRACT_ID  = 'employee_contract_id';
    public const EMPLOYEE_CONTRACT_NUMBER = 'employee_contract_number';
    public const EC_VALID_FROM  = 'ec_valid_from';
    public const EC_VALID_TO = 'ec_valid_to';
    public const EMPLOYEE_CONTRACT_TYPE = 'employee_contract_type';
    public const EMPLOYEE_CONTRACT_POSITION_ID = 'employee_contact_position_id';
    public const WAGE_TYPE = 'wage_type';
    public const WORKGROUP_ID = 'workgroup_id';
    public const DAILY_WORKTIME = 'daily_worktime';
    public const CARD = 'card';
    public const BASE_ABSENCE_TYPE_ID = 'base_absence_type_id';
    public const PAYROLL_ROSSMAN_AKTIV_ID = 'b72660630d75b6113d144cbafe1d38ec';
    public const PAYROLL_ROSSMAN_INAKTIV_ID = '6700b9e8de3cf40e1abba264d0a25a98';
    public const WORKGROUPS_ROSSMAN = [8 => 'e77d7df30d7c74aed6e31274135bbed1', 6 => '6cb3d1a197de06c9b28c767cb4fa1222', 4 => 'e858781639d2615675f6915c29078f37'];
    public const BASE_ABSENCE_TYPE_MAPPING = 'baseAbsenceTypeMapping';
    public const BASE_ABSENCE_TYPE = 'SzabtpDictionaryItemId';
    public const LEAVEDAYS = 'LeaveDays';
    public const CALCDAYS = 'CalcDays';
    public const YEAR = 'Year';
    public const EMPLOYEE_CONTRACT = 'Employee_Contract';
    public const EMPLOYEE_BASE_ABSENCE = 'Employee_Base_Absence';
    public const QUANTITY  = 'quantity';
    public const QUANTITY_HOUR  = 'quantity_hour';
    public const EMPLOYEE_CONTRACT_ID_AND_HR_RELATIONS_SHIP_ID = 'employee_contract_id_and_hr_relations_ship_id';
    public const DEFAULT = 'default';
    
}

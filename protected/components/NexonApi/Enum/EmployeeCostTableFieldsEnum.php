<?php

declare(strict_types=1);

namespace Components\NexonApi\Enum;

final class EmployeeCostTableFieldsEnum
{
    public const CREATED_BY = 'NexonImport';
    public const CREATED_ON = 'now';
    public const ROW_ID = 'row_id';
    public const EMPLOYEE_CONTRACT_ID = 'employee_contract_id';
    public const COST_ID = 'cost_id';
    public const COST_NAME = 'cost_name';
    public const COST_CENTER_ID = 'cost_center_id';
    public const COST_CENTER_NAME = 'cost_center_name';
    public const PERCENT = 100;
    public const STATUS = 'status';
    public const VALID_TO = '2038-01-01';
    public const DEFAULT_INSERT_OR_UPDATE = 'insert';
}
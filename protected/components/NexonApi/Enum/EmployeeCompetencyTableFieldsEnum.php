<?php

declare(strict_types=1);

namespace Components\NexonApi\Enum;

final class EmployeeCompetencyTableFieldsEnum
{
    public const POSITION_ASSIGNMENT_COMPETENCY =
        [
            '0e63a5e8e7a45b77e87494e1d090e2aa' => ['1'], //Eladó
            '264cc43f496a7eea35a1c928265f964c' => ['4'], //Boltvezető helyettes 2
            'c9ebbe0a49e2165a0ff833dd9faee0ca' => ['3', '4'], //Boltvezető
            'b90e2cda14b9095a9694dcf0970f64dd' => ['9'], //Árufeltöltő
            'd99c9789f0ce6ee58e334cbae5a9e70a' => ['2', '4'], //Boltvezető helyettes
            'd4772d02651274c7ee8f2553ad0312be' => ['4'], //<PERSON>s<PERSON> eladó
            '5539' => ['1'], //<PERSON><PERSON> árufeltöltő
            'b97c60bd994e107897c823f8df772a75' => ['3', '4'] //Betanuló boltvezető
        ];

    public const VALID_FROM = 'valid_from';
    public const VALID_TO = 'valid_to';
    public const CREATED_BY = 'NexonImport';
    public const CREATED_ON = 'now';
    public const ROW_ID = 'row_id';
    public const EMPLOYEE_POSITION = 'EmployeePosition';
    public const EMPLOYEE_CONTRACT_ID = 'EmployeeContractID';
}
<?php


namespace Components\NexonApi\Enum;

final class WorkflowEnum
{
    public const HRRELATIONSHIP = 'hrrelationship';
    public const GETABSENCETYPES = 'getabsencetypes';
    public const GETCONTACTS = 'getcontacts';
    public const GETPERSONS2 = 'getpersons2';
    public const GETDATEOFREACHINGRETIREMENTAGELIMIT = 'getdateofreachingretirementagelimit';
    public const GETWORKSCHEDULES = 'getworkschedules';
    public const EMPLOYEEMANAGER = 'employeemanager';
    public const JOBTYPE = 'jobtype';
    public const COMPANY = 'company';
    public const EMPLOYMENTCONTRACT = 'employmentcontract';
    public const EMPLOYMENTCONTRACTVERSION = 'employmentcontractversion';
    public const ADDRESS = 'address';
    public const FEORDATA = 'feordata';
    public const LONGABSENCES = 'longabsences';
    public const PERSON = 'person';
    public const PERSONCOMPANYGROUPMEMBERIDENTIFIER = 'personcompanygroupmemberidentifier';
    public const PERSONNAME = 'personname';
    public const USERS = 'users';
    public const YEARLYHRRELATIONSHIPDATA = 'yearlyhrrelationshipdata';
    public const ORGANIZATIONUNIT = 'organizationunit';
    public const POSITIONFULFILLMENT = 'positionfulfillment';
    public const JOB = 'job';
    public const MONTHLYCOMPANYLEVELDATA = 'monthlycompanyleveldata';
    public const LANGUAGEPROFICIENCY = 'languageproficiency';
    public const EMPLOYEEALLDATA = 'employeeAllData';




}
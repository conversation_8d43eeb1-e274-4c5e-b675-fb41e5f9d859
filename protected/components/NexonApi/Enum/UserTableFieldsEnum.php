<?php

declare(strict_types=1);

namespace Components\NexonApi\Enum;

final class UserTableFieldsEnum
{
    public const ROW_ID = 'row_id';
    public const ROLEGROUP_ID = 'rolegroup_id';
    public const COLUMN_VALID_TO = 'valid_to';
    public const VALID_TO = '2038-01-01';
    public const CREATED_BY = 'NexonImport';
    public const CREATED_ON = 'now';
    public const USERNAME_STRING_RULE = ':: Any-Latin; :: Latin-ASCII; :: NFD; :: [:Nonspacing Mark:] Remove; :: Lower(); :: NFC;';
    public const DEFAULT_PASSWORD = 'TaxNumber';
    public const PLANNING_ROLEGROUP = '971a85f6ee292d1ff3cf75a73d09adb3';
    public const DEFAULT_ROLEGROUP = '96ce2d08e7ab66014893fcd9a159ee58';
    public const BOLTVEZETO_POSITION = 'c9ebbe0a49e2165a0ff833dd9faee0ca';
    public const BOLTVEZETO_HELYETTES_POSITION = 'd99c9789f0ce6ee58e334cbae5a9e70a';
    public const BOLTVEZETO_HELYETTES_2_POSITION = '264cc43f496a7eea35a1c928265f964c';
    public const BETANULO_BOLTVEZETO_POSITION = 'b97c60bd994e107897c823f8df772a75';
    public const BOLTVEZETO_POSITIONS = ['c9ebbe0a49e2165a0ff833dd9faee0ca', 'd99c9789f0ce6ee58e334cbae5a9e70a', '264cc43f496a7eea35a1c928265f964c', 'b97c60bd994e107897c823f8df772a75'];
    public const APPROVER_PROCESS_IDS = ['absenceApprover', 'absenceView', 'companyMainData', 'employeeManagement', 'overtimeApprover', 'workForce', 'workSchedule'];
    public const APPROVER_RELATED_MODELS = ['Unit' => 'unit_id', 'Company' => 'company_id'];
    public const PENNY_DEFAULT_AUTH_ROLEGROUP = '38fbadef0007e45eec24ff89af96d8b0';
    public const PENNY_APPROVER_RELATED_MODELS = ['Unit' => 'unit_id', 'CompanyOrgGroup1' => 'company_org_group_id', 'CompanyOrgGroup3' => 'company_org_group_id'];
}
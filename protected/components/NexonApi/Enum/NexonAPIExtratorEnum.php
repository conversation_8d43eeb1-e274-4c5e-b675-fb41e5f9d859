<?php


namespace Components\NexonApi\Enum;

final class NexonAPIExtratorEnum
{
    public const HR_RELATIONSHIP_ID = 'HrRelationshipId';
    public const HR_RELATIONSHIP_IDS = 'HrRelationshipIds';
    public const HR_RELATIONSHIP_ID_LOW = 'HrrelationshipId';
    public const PERSON_ID = 'PersonId';
    public const ORGANIZATION_UNIT_ID = 'OrganizationUnitId';
    public const EMPLOYMENT_CONTRACT_ID = 'EmploymentContractId';
    public const HISZTSZOTARADATLEIR_ID = 'HisztSzotarAdatLeirId';
    public const EGYSEG_DICTIONARYITEMID = 'EGYSEG_DictionaryItemId';
    public const POSITION_ID = 'PositionId';
    public const ID = 'Id';
    public const SOCIAL_SECURITY_NUMBER = 'SocialSecurityNumber';
    public const POSITIONFULFILLMENTTYPE_DICTIONARY = 'PositionFulfillmentType_DictionaryItemId';
    public const PAYROLL_TABLE_COLUMNS = ['id' => 'payroll_id', 'name' => 'payroll_name'];
    public const UNIT_TABLE_COLUMNS = ['id' => 'unit_id', 'name' => 'unit_name'];
    public const COMPANY_ORG_GROUP_TABLE_COLUMNS = ['id' => 'company_org_group_id', 'name' => 'company_org_group_name'];
    public const COMPANY_TABLE_COLUMNS = ['id' => 'company_id', 'name' => 'company_name'];
    public const COST_TABLE_COLUMNS = ['id' => 'cost_id', 'name' => 'cost_name'];
    public const COST_CENTER_TABLE_COLUMNS = ['id' => 'cost_center_id', 'name' => 'cost_center_name'];
    public const YIELD_MATCH_RULE_PATTERN = '/^0/';

}


<?php

namespace Components\NexonApi\Handler;

use Components\NexonApi\Interfaces\NexonApiDataWriterInterface;
use Components\NexonApi\Interfaces\NexonDataWritingInterface;
use Components\NexonApi\Descriptor\NexonRawDataDescriptor;
use Components\NexonApi\Writer\NexonApiDataWriter;

final class NexonDataWritingHandler implements NexonDataWritingInterface
{
    /**
     * @var NexonApiDataWriterInterface[]
     */
    private array $writers;

    public function __construct()
    {
        $this->writers[] = new NexonApiDataWriter();
    }

    public function handle(NexonRawDataDescriptor $descriptor) : void
    {
        foreach ($this->writers as $writer) {
            $writer->write($descriptor);
        }
    }
}

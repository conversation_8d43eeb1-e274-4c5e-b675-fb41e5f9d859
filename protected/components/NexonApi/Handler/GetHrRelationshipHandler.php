<?php

namespace Components\NexonApi\Handler;

use Components\NexonApi\Enum\ConnectEnum;
use Components\NexonApi\Enum\NexonApiCallNameEnum;
use Components\NexonApi\Interfaces\ApiCallHandlerInterface;
use Components\NexonApi\Provider\DataProvider;

final class GetHrRelationshipHandler implements ApiCallHandlerInterface
{
    private const REQUEST_CALL = '/OdataApi/v5.0/HrRelationship';
    private const REQUEST_CALL_TYPE = 'GET';
    private DataProvider $dataProvider;

    public function __construct()
    {
        $this->dataProvider = new DataProvider();
    }

    public function handle($params): string
    {
        $params[ConnectEnum::APICALLURL] = GetHrRelationshipHandler::REQUEST_CALL;
        $params[ConnectEnum::REQUEST_TYPE] = self::REQUEST_CALL_TYPE;
        $params[ConnectEnum::BODY] = '';
        $rawData = json_decode($this->dataProvider->provide($params));
        return json_encode($rawData);
    }

    public function getName(): string
    {
        return NexonApiCallNameEnum::HR_RELATIONSHIP;
    }
}

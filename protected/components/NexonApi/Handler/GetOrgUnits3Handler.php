<?php

namespace Components\NexonApi\Handler;

use Components\NexonApi\Enum\ConnectEnum;
use Components\NexonApi\Enum\NexonApiCallNameEnum;
use Components\NexonApi\Interfaces\ApiCallHandlerInterface;
use Components\NexonApi\Provider\DataProvider;

final class GetOrgUnits3Handler implements ApiCallHandlerInterface
{
    private const REQUEST_CALL = '/OdataApi/v5.0/GetOrgUnits3';
    private const REQUEST_CALL_TYPE = 'POST';
    private DataProvider $dataProvider;

    public function __construct()
    {
        $this->dataProvider = new DataProvider();
    }

    public function handle($params): string
    {
        $params[ConnectEnum::APICALLURL] = self::REQUEST_CALL;
        $params[ConnectEnum::REQUEST_TYPE] = self::REQUEST_CALL_TYPE;
        $params[ConnectEnum::BODY] = '{ "orgunitids": [] }';
        $rawData = $this->dataProvider->provide($params);
        return $rawData;
    }

    public function getName(): string
    {
        return NexonApiCallNameEnum::GET_ORG_UNITS3;
    }
}

<?php

declare(strict_types=1);

namespace Components\User\Descriptor;

final class UsersDataByUserIdsFilterDescriptor
{
    private array $userIds;
    private \DateTime $validFrom;
    private \DateTime $validTo;
    private int $status = \Status::PUBLISHED;

    public function __construct(array $userIds, \DateTime $validFrom, \DateTime $validTo)
    {
        $this->validFrom = $validFrom;
        $this->validTo = $validTo;
        $this->userIds = $userIds;
    }

    public function getUserIds(): array
    {
        return $this->userIds;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): void
    {
        $this->status = $status;
    }

    public function isUserIdsNotEmpty(): bool
    {
        return !empty($this->userIds);
    }

    public function isValidFromNotEmpty(): bool
    {
        return !empty($this->validFrom);
    }

    public function isValidToNotEmpty(): bool
    {
        return !empty($this->validTo);
    }
}
<?php

namespace Components\User\Provider;
use Components\User\Descriptor\UsersDataByUserIdsFilterDescriptor;
class UserByUserIdProvider
{
    public function provide(UsersDataByUserIdsFilterDescriptor $usersFilters): array
    {
        $defaultEnd  = \App::getSetting("defaultEnd");
        $criteria = new \CDbCriteria();
        $criteria->alias = 't';

        $criteria->addCondition("`status` = :status");
        $criteria->params[':status'] = $usersFilters->getStatus();

        if ($usersFilters->isUserIdsNotEmpty()) {
            $criteria->addCondition("t.employee_id IN ('" . implode("','", $usersFilters->getUserIds()) . "')");
        }

        if ($usersFilters->isValidFromNotEmpty()) {
            $criteria->addCondition('`valid_from` <= :valid_to');
            $criteria->params[':valid_to'] = $usersFilters->getValidFrom()->format('Y-m-d');
        }

        if ($usersFilters->isValidToNotEmpty()) {
            $criteria->addCondition("IFNULL(`valid_to`,'" . $defaultEnd . "') >= :valid_from");
            $criteria->params[':valid_from'] = $usersFilters->getValidTo()->format('Y-m-d');
        }

        return \User::model()->findAll($criteria);
    }
}
<?php #yii2: static-method-problem

'yii2-only`;

	namespace app\components;
	use DateTime;
	use app\components\App;
	use app\components\Dict;
	use app\components\Helpers\AnyCache;
	use app\controllers\LoginController;
	use app\models\Dictionary;
	use app\models\Status;
	use Yang;

`/yii2-only';


// Dict::getValue("error_password_minlength",array("minLength"=>$minLength))

class Dict
{
	public static function getFirstNotEmptyModuleValue($dict_id, $modules = ["ttwa-base"], $params = []) {
		foreach ($modules as $module) {
			if (!empty(self::getValue($dict_id, $params, $module))){
				return self::getValue($dict_id, $params, $module);
			};
		}
	}

	public static function getModuleValue($module,$dict_id,$params = array()) {
		return Dict::getValue($dict_id,$params,$module);
	}

    public static function getValue($dict_id, $params = array(), $module = null, $returnSameValue = false)
	{
		static $dict;
		if (session_status() == PHP_SESSION_NONE) {
			session_start();
		}

		if( 1 ) {

			$lang = "";

			if(userID()) {
				$userLang = AnyCache::key('components/Dict.php, lang for user',[userID()]);
				if(AnyCache::has($userLang)) {
					$lang = AnyCache::get($userLang);
				} else {
					$lang = LoginController::getUserLang(Yang::getUserName());
					AnyCache::set($userLang,$lang);
				}
			}
			if(!$lang) $lang = AnyCache::get("system.currentLanguage");
			if(!$lang) $lang = Yang::cookie('tiptime_language')->value ?? false;
			if(!$lang) $lang = Dict::getLang();
			$dictLoad = userID() != '' ? TRUE : FALSE;
			if ($dictLoad) { AnyCache::set("system.currentLanguage",$lang,"-"); }

			$ck = AnyCache::key("components/Dict.php:getValue",[userID(),$lang]); // language changing should be ok now!
			if(AnyCache::has($ck)) {

				$dict = AnyCache::get($ck,[]);

			}else{

				$sql = "

					select * from dictionary
					where 1
						and `valid`		= 1
						and `lang`		= '{$lang}'
					;

				";
				$dictList = dbFetchAll($sql);
				$dict = [];
				foreach($dictList as $r) {

					$lookupKey = sprintf("[modules.%s] %s",$r["module"],$r["dict_id"]);
					$lookupVal = $r["dict_value"];
					$dict[$lookupKey] = $lookupVal;

				}
				foreach($dictList as $r) {

					$lookupKey = sprintf("[global] %s",$r["dict_id"]);
					$lookupVal = $r["dict_value"];
					$dict[$lookupKey] = $lookupVal;

				}
				// if ($dictLoad) { AnyCache::set($ck,$dict,"-"); }
				AnyCache::set($ck,$dict,"-");

			}

			if( $module) $lookupKey = "[modules.$module] $dict_id";
			if(!$module) $lookupKey = "[global] $dict_id";
			$out = isset($dict[$lookupKey]) ? $dict[$lookupKey] : null;
			if(is_null($out)) return '';

			if(count($params)) {
				$xlat = [];
				foreach($params as $name=>$value) $xlat["{".$name."}"] = $value;
				$out = strtr($out,$xlat);
			}

			return $out;

		}


		if (!isset($_SESSION["tiptime"]["dict"][$dict_id]))
		{
			$lang = Dict::getLang();

			if (empty($module)) { // ROLLBACK
				$module_dictionary = new Dictionary;
				$criteria = new CDbCriteria();
				$criteria->condition = '`valid`=1 AND `lang`="'.$lang.'" AND `dict_id`="'.$dict_id.'" AND `module`!="ttwa-base"';
				$res_module_dictionary = $module_dictionary->find($criteria);

				$ret = null;
				if ($res_module_dictionary) {
					$ret = $res_module_dictionary->dict_value;
				} else {
					$base_dictionary = new Dictionary;
					$criteria = new CDbCriteria();
					$criteria->condition = '`valid`=1 AND `lang`="'.$lang.'" AND `dict_id`="'.$dict_id.'" AND `module`="ttwa-base"';
					$res_base_dictionary = $base_dictionary->find($criteria);

					if ($res_base_dictionary)
					{
						$ret = $res_base_dictionary->dict_value;
					}
				}
			} else {
				$module_dictionary = new Dictionary;
				$criteria = new CDbCriteria();
				$criteria->condition = '`valid`=1 AND `lang`="'.$lang.'" AND `dict_id`="'.$dict_id.'" AND `module`="'.$module.'"';
				$res_module_dictionary = $module_dictionary->find($criteria);

				$ret = null;
				if ($res_module_dictionary) {
					$ret = $res_module_dictionary->dict_value;
				}
			}

			$_SESSION["tiptime"]["dict"][$dict_id] = $ret;
		}

		$val = $_SESSION["tiptime"]["dict"][$dict_id];

		if (!empty($val) && count($params))
		{
			foreach ($params as $name => $value)
			{
				$val = str_replace("{".$name."}",$value,$val);
			}
		}
		// Localhost: make SQL file
		if (empty($val)
			&& (substr($_SERVER["SERVER_NAME"],-9)=='localhost')
			&& (!file_exists(Yang::getBasePath().'/data/db/'.$dict_id.'.sql'))
			&& (file_exists(Yang::getBasePath().'/data/db/BPM.sql'))
			)
		{
			error_log("## This is an automatic generated script file.

INSERT INTO `dictionary` (`lang`, `valid`, `module`, `dict_id`, `dict_value`) VALUES
	('hu', 1, '".Yang::getParam('module')."', '$dict_id', '???'),
	('en', 1, '".Yang::getParam('module')."', '$dict_id', '???');", 3, Yang::getBasePath().'/data/db/'.$dict_id.'.sql');
		}

		return !empty($val)?$val:($returnSameValue ? $dict_id : "");
	}

	public static function getValues($dict_ids)
	{
		if (is_array($dict_ids))
		{
			$ret = array();

			foreach ($dict_ids as $dict_id)
			{
				$ret[] = Dict::getValue($dict_id);
			}

			return $ret;
		}
		// Fallback
		else
		{
			return Dict::getValue($dict_ids);
		}
	}

	/**
	 * Lekérdezi a beállított nyelvet
	 * @return string
	 */
	public static function getLang()
	{

		$acKey = "system.currentLanguage";
		if(AnyCache::has($acKey)) {
			$out = AnyCache::get($acKey);
			if($out) return $out;
		}

		$loggedIn = userID();
		$lang = "";

		$loginform_array = requestParam('LoginForm');
		if($loginform_array) {
			$login_username = $loginform_array['ttwaun'];
			if(!$login_username) if($loggedIn) $login_username = Yang::getUserName();
			$lang = LoginController::getUserLang($login_username);
		}

		if(!$lang) {
			$uid = intval(userID());
			if($uid) {
				$userRecord = Yang::getUserRecordByID($uid);
				$lang = $userRecord["lang"];
			}
		}

		$out = $lang ?: App::getSetting('defaultLang');

		if($loggedIn) AnyCache::set($acKey,$out,"-");
		return $out;
	}

	public static function getDropdown($model,$col_id,$col_dict_id,$module = 'ttwa-base',$conditions = "",$getSql = false)
	{
		$lang = Dict::getLang();

		if (!$getSql)
		{
			$m = new $model;

			$criteria = new CDbCriteria();

			$criteria->condition = '
				1
				'.$conditions.'
			';

			$results = $m->findAll($criteria);

			$i = 0;
			$results_arr = array();

			foreach ($results as $result)
			{
				$col_id_val = $result->$col_id;

				$dict_id = $result->$col_dict_id;

				$module_dictionary = new Dictionary;
				$criteria = new CDbCriteria();
				$criteria->condition = '`valid`=1 AND `lang`="'.$lang.'" AND `dict_id`="'.$dict_id.'" AND `module`="'.Yang::getParam('module').'"';
				$res_module_dictionary = $module_dictionary->find($criteria);

				if (count($res_module_dictionary))
				{
					$results_arr[$i]['id'] = $col_id_val;
					$results_arr[$i]['value'] = $res_module_dictionary->dict_value;
					$i++;
				}
				else
				{
					$base_dictionary = new Dictionary;
					$criteria = new CDbCriteria();
					$criteria->condition = '`valid`=1 AND `lang`="'.$lang.'" AND `dict_id`="'.$dict_id.'" AND `module`="ttwa-base"';
					$res_base_dictionary = $base_dictionary->find($criteria);

					if (count($res_base_dictionary))
					{
						$results_arr[$i]['id'] = $col_id_val;
						$results_arr[$i]['value'] = $res_base_dictionary->dict_value;
						$i++;
					}
				}
			}

			return $results_arr;
		}
		else
		{
			$t = $model::model()->tableSchema->name;
			$sql = "
				SELECT
					t.`$col_id` AS id, d.`dict_value` AS value
				FROM
					`$t` t
				LEFT JOIN
					`dictionary` d ON t.`$col_dict_id` = d.`dict_id`
				WHERE
					d.`dict_id` IS NOT NULL AND d.`valid`=1 AND d.`lang`='$lang' AND d.`module`='$module'
					$conditions
			";

			return $sql;
		}
	}

	public static function getValueWithLang($dict_id, $lang, $params = array(), $module = null)
	{
		if (empty($module)) { // ROLLBACK
			$module_dictionary = new Dictionary;
			$criteria = new CDbCriteria();
			$criteria->condition = '`valid`=1 AND `lang`="'.$lang.'" AND `dict_id`="'.$dict_id.'" AND `module`!="ttwa-base"';
			$res_module_dictionary = $module_dictionary->find($criteria);

			$ret = null;
			if ($res_module_dictionary) {
				$ret = $res_module_dictionary->dict_value;
			} else {
				$base_dictionary = new Dictionary;
				$criteria = new CDbCriteria();
				$criteria->condition = '`valid`=1 AND `lang`="'.$lang.'" AND `dict_id`="'.$dict_id.'" AND `module`="ttwa-base"';
				$res_base_dictionary = $base_dictionary->find($criteria);

				if ($res_base_dictionary)
				{
					$ret = $res_base_dictionary->dict_value;
				}
			}
		} else {
			$module_dictionary = new Dictionary;
			$criteria = new CDbCriteria();
			$criteria->condition = '`valid`=1 AND `lang`="'.$lang.'" AND `dict_id`="'.$dict_id.'" AND `module`="'.$module.'"';
			$res_module_dictionary = $module_dictionary->find($criteria);

			$ret = null;
			if ($res_module_dictionary) {
				$ret = $res_module_dictionary->dict_value;
			}
		}

		if (!empty($ret) && count($params))
		{
			foreach ($params as $name => $value)
			{
				$insertedValue = $value;
				if(!is_numeric($value) && !DateTime::createFromFormat('Y-m-d', $value) && !empty($value)){
					$SQL = "SELECT dict_value FROM dictionary WHERE dict_id IN (SELECT dict_id FROM dictionary WHERE dict_value = '" . $value . "') AND lang = '" . $lang . "'";
					$dict_value = dbFetchValue($SQL);
					if(!empty($dict_value)){
						$insertedValue = $dict_value;
					}
				}
				$ret = str_replace("{".$name."}",$insertedValue,$ret);
			}
		}

		return !empty($ret)?$ret:"";
	}

	public static function getDictId($dictValue, $module = '')
	{
		$lang = self::getLang();
		$module_dictionary = new Dictionary;
		$criteria = new CDbCriteria();
		$criteria->condition = '`valid`=1 AND `lang`="'.$lang.'" AND `dict_value`="'.$dictValue . '"';
		if (!empty($module))
		{
			$criteria->condition .= ' AND `module` = "'.$module.'"';
		}
		$res_module_dictionary = $module_dictionary->find($criteria);
		$ret = $res_module_dictionary->dict_id;

		return !empty($ret)?$ret:"";
	}
}

?>

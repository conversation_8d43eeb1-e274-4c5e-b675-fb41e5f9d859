<?php #yii2: done

'yii2-only`;

	namespace app\components;
	use Yang;

`/yii2-only';


class Log
{
    const MAXTEXT = 65535; 
	public static function create($cObj, $log_message = '', $log_params = [], $auto_log = false) {
		self::storeLog([
			'ipv4_address' => $_SERVER['REMOTE_ADDR'],
			'event_time' => date('Y-m-d H:i:s'),
			'user_id' => $_SESSION["tiptime"]["userIdToSwitch"] ?? userID(),
			'username' => $_SESSION["tiptime"]["userNameToSwitch"] ?? userName(),

			'page_title' => self::getControllerPageTitle($cObj),
			'controller_id' => Yii::app()->controller->id,
			'action_id' => Yii::app()->controller->action->id ?? null,
			'class_name' => get_class($cObj),

			'log_message' => $log_message,
			'log_params' => $log_params,
			'auto_log' => $auto_log,

			'post_params' => $_POST,
			'get_params' => $_GET,
		]);
	}

	private static function storeLog($data) {
		$controllersToSkip = [
			'mainLog',
		];

		if ($data['auto_log'] && in_array($data['controller_id'], $controllersToSkip)) return false;

		$actionsToSkip = [
			'check',
			'dbUpgrade',
			'screenSession',

			'getStatusButtons',
			'autocomplete',
			'dropdown',
			'saveFormValue',

			'getColumnProperties',
			'getGridMultiHeaders',
			'setInitProperties',
			'setLoadInitProperties',
			'setPostInitProperties',
		];

		if ($data['auto_log'] && in_array($data['action_id'], $actionsToSkip)) return false;

		if (isset($data['post_params']['LoginForm']['ttwapw'])) {
			$data['post_params']['LoginForm']['ttwapw'] = '';
		}

		if (is_array($data['get_params'])) {
			$get = [];
			foreach ($data['get_params'] as $k => $v) { (self::isJson($v)) ? $get[$k] = json_decode($v, true) : $get[$k] = $v; }
		} else { $get = (self::isJson($data['get_params'])) ? json_decode($data['get_params'], true) : $data['get_params']; }
		if (is_array($data['post_params'])) {
			$post = [];
			foreach ($data['post_params'] as $k => $v) { (self::isJson($v)) ? $post[$k] = json_decode($v, true) : $post[$k] = $v; }
		} else { $post = (self::isJson($data['post_params'])) ? json_decode($data['post_params'], true) : $data['post_params']; }

		$paramsArr = [
			'GET' => $get,
			'POST' => $post,
		];

		$cAct = $data['controller_id'].'/'.$data['action_id'];

		$log_message = '';
		if (!empty($data['log_message'])) {
			$log_message = $data['log_message'];
		} else {
			$log_message = 'LOG_AUTO_'.$cAct;

			switch ($data['action_id']) {
				case 'index':
						$log_message = 'LOG_AUTO_site_open';
					break;
				case 'gridData':
						$log_message = 'LOG_AUTO_site_load';
					break;
				case 'save':
						$log_message = 'LOG_AUTO_site_save';
					break;
			}
		}
		$log_params = '';
		if (!empty($log_message)) {
			$logMsgParamsArr = [];

			if (is_array($data['log_params']) && count($data['log_params'])) {
				$logMsgParamsArr = $data['log_params'];
			}

			$logMsgParamsArr['username'] = $data['username'];
			$logMsgParamsArr['page_title'] = $data['page_title'];
			$log_params = json_encode($logMsgParamsArr, JSON_UNESCAPED_UNICODE);
            if (strlen($log_params) > Log::MAXTEXT) {
                //Yang::log("TOO MANY DATA", 'log', 'Log.php');
                //Yang::log($log_params, 'log', 'Log.php');
                $log_params = "";
            }
		}

		$params = self::json_encode(self::arrFlatten($paramsArr));

		$SQL = '
			INSERT INTO `main_log` (`ipv4_address`, `event_time`, `user_id`, `username`, `page_title`, `controller_id`, `action_id`, `class_name`, `log_message`, `log_params`, `params`) VALUES
				("'.$data['ipv4_address'].'", "'.$data['event_time'].'", "'.$data['user_id'].'", "'.$data["username"].'", "'.$data['page_title'].'", "'.$data['controller_id'].'", "'.$data['action_id'].'", "'.$data['class_name'].'", "'.$log_message.'", "'.str_replace('"', '\\"', $log_params).'", "'.str_replace('"', '\\"', $params).'")
		';

		self::exec($SQL);

		return true;
	}

	private static function arrFlatten($array, $prefix = '') {
		$result = [];

		foreach ($array as $key => $value) {
			$new_key = $prefix . (empty($prefix) ? '' : '.') . $key;

			if (is_array($value)) {
				$result = array_merge($result, self::arrFlatten($value, $new_key));
			} else {
				$result[$new_key] = $value;
			}
		}

		return $result;
	}

	private static function getControllerPageTitle($cObj) {
		if (method_exists($cObj, "getControllerPageTitleId"))
			return $cObj->getControllerPageTitleId();

		return '';
	}

	/**
	 * Yii 1.1
	 */
	private static function exec($SQL = '') {
		try {
			dbExecute($SQL);
		} catch (\Exception $e) {
			Yang::log('Failed to exec: '.$e, 'log', 'sys.MainLOG');
		}
	}

	/**
	 * Yii 1.1
	 */
	private static function json_encode($arr = []) {
		return json_encode($arr, JSON_UNESCAPED_UNICODE);
	}

	private static function isJson($string) {
        if (!is_string($string)) { return false;}
		json_decode($string);
		return json_last_error() === JSON_ERROR_NONE;
	}
}
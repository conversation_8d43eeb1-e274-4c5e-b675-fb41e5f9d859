<?php

#docs			[n-7bbhbbcw]	<PERSON><PERSON><PERSON> használni a Minert
#implementThis	[n-7cjqgz2q]	Fontos lenne majd ezt az SQL bind történetet megoldani

class Miner {

    public static function showRecords($list,$maxWidth=null) {
        $head = array_keys(reset($list)); foreach($head as &$x) $x=strtr($x,["."=>"<br>"]); unset($x);
        $size = ($maxWidth) ? "{$maxWidth}px" : "8vw";
        ?>
        <style>
            * {box-sizing:border-box;}
            show-records { --header-height:40px; --header-color:steelblue; --fonts:calibri,arial,helvetica; }
            show-records, scrolling-box, stable-header {display:block;}
            show-records {box-shadow:0px 5px 14px -8px black;height:80vh;position:relative;}
            show-records {background:linear-gradient(to bottom,var(--header-color) 40px,white 41px);}
            show-records stable-header {border-right:17px solid var(--header-color);}
            show-records stable-header table thead th {color:white;}
            show-records stable-header table thead th {background:var(--header-color);height:var(--header-height);}
            show-records scrolling-box table thead th {line-height:0 !important;padding-top:0;padding-bottom:0;}
            show-records scrolling-box table thead th {visibility:hidden;}
            show-records scrolling-box, show-records stable-header {position:absolute;left:0px;width:100%;}
            show-records scrolling-box {top:var(--header-height);height:calc(100% - var(--header-height));}
            show-records scrolling-box {overflow:scroll;z-index:1;}
            show-records stable-header {top:0px;height:var(--header-height);z-index:2;overflow:hidden;}
            show-records table {position:absolute;top:0px;left:0px;width:100%;border:none;border-collapse:collapse;}
            show-records table th, show-records table td {border:1px solid rgba(0,0,0,0.21);border-width:0 1px;}
            show-records table th, show-records table td {font:15px var(--fonts);padding:3px 8px;}
            show-records table th, show-records table td {white-space:nowrap;max-width:<?=$size?>;overflow:hidden;}
            show-records table tbody tr:nth-of-type(odd) {background-color:#f3f3f3;}
            show-records table tbody tr:hover {background-color:#ddd;}
        </style>
        <show-records>
            <stable-header>&nbsp;</stable-header>
            <scrolling-box onscroll="showRecords_header.style.transform = 'translateX('+(0-this.scrollLeft)+'px)';">
                <table>
                    <thead><?php print "<tr><th>".join("</th><th>",$head)."</th></tr>"; ?></thead>
                    <tbody><?php foreach($list as $r) {print "<tr><td>".join("</td><td>",$r)."</td></tr>";} ?></tbody>
                </table>
            </scrolling-box>
        </show-records>
        <script>
            setTimeout(function() {
                function find(x) {return document.querySelector(x);}
                find("show-records stable-header").innerHTML = find("show-records scrolling-box table").outerHTML;
                window.showRecords_header = find('stable-header table');
            },1);
        </script>
        <?php
    }

	public static function get($key) {

		$key = preg_replace("/\W/","_",$key);
		$key = preg_replace("/_+/","_",$key);
		$key = strtolower($key);
		$methodName = "getdata_$key";
		$otherArguments = func_get_args(); array_shift($otherArguments);
		return call_user_func_array(["Miner",$methodName],$otherArguments);

	}

	public static function getdata_auth_acl($controller,$operation,$column = null,$user_id=null) {

		$column		= $column		?: "ALL";
		$controller = $controller	?: "ALL";
		$user_id	= $user_id ?: userID(); if(!$user_id) return false;
		$isGuest	= Yang::isGuest();
		$published	= Status::PUBLISHED;
		$defEnd		= App::getSetting("defaultEnd");

		'Get user roleGroup';{

			$acKey = AnyCache::key("auth_acl","rolegroup of $user_id");
			$cached = (AnyCache::has($acKey));
			if(!$cached) {
				$roleGroup = "";
				if(!empty($user_id)) {
					$roleGroup = dbFetchValue("

						SELECT	rolegroup_id
						FROM	user
						WHERE	1
							AND `user_id`	= '$user_id'
							AND `status`	= $published
							AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`,'$defEnd')
					","rolegroup_id");
					AnyCache::set($acKey,$roleGroup,"user");
				}
			}
			$roleGroup = AnyCache::get($acKey);
		}

		$acKey = AnyCache::key("auth_acl",$user_id);
		$cached = (AnyCache::has($acKey));
		if (is_null($user_id)) { $userId = userID(); } else { $userId = $user_id; }
		if(!$cached) {

			$tb = dbFetchAll("
				SELECT t.* FROM
				(
					SELECT 			aa.`controller_id`, aa.`operation_id`, aa.`column_name`, aa.`access_right`, aa.`login_need`
					FROM			`auth_acl` AS aa
					LEFT JOIN		`auth_role_in_group` AS arig ON aa.`role_id` = arig.`role_id`
					WHERE 0			OR (arig.`rolegroup_id` = '{$roleGroup}')
									OR (`login_need` = 0)
					UNION
					SELECT			aa.`controller_id`, aa.`operation_id`, aa.`column_name`, aa.`access_right`, aa.`login_need`
					FROM			`user_report_rights` AS urr
					JOIN			`auth_acl` AS aa ON aa.`role_id` = urr.`role_id`
					WHERE 1			AND urr.`status` = " . Status::PUBLISHED . "
									AND urr.`user_id` = '{$userId}'
									AND CURDATE() BETWEEN urr.`valid_from` AND IFNULL(urr.`valid_to`, '" . App::getSetting("defaultEnd") . "')
					GROUP BY		urr.`role_id`
				) AS t

			");
            $map = [];
			foreach($tb as $r) {

				$ctl = $r["controller_id"	];
				$opr = $r["operation_id"	];
				$col = $r["column_name"		]?:"ALL"; #changeThis
				$acc = $r["access_right"	]?:"0";
				$lgn = $r["login_need"		]?:"0";
				$key = strtolower("$ctl,$opr,$col");

				$map[$key] = "$acc,$lgn";

			}
			AnyCache::set($acKey,$map,"auth_acl,auth_role_in_group");

		}

		$map = AnyCache::get($acKey);
		$key = strtolower("$controller,$operation,$column");
		$all = strtolower(        "ALL,$operation,$column");
		$inf = isset($map[$key]) ?: isset($map[$all]) ?: "~"; if($inf=="~") return false;

		list($allow,$loginRequired) = explode(",",$inf);

		if($isGuest) $allow = ($loginRequired)? 0:1;

		return $allow;

	}

	#docs	[n-7bkj2485]
	public static function getdata_employee_active_quicklist($searchValues) {

		#note	[n-7bmrwllj] okt.19, Miner queryk és az időbeliség
		#note	[n-7bwbcngk] okt.22, Azonos contract id, két cég, két emp_id?
		#note	[n-7c95btsf] okt.28, Hernádi Tibor nem bír magával
		#note	[n-7c95f9hx] okt.28, Megjavult májustól felfelé, de mi legyen a korábbiakkal?

		$fromDate	= $searchValues["valid_from"];
		$tillDate	= $searchValues["valid_to"];
		$days		= eachDayBetween($fromDate,$tillDate);

		$stPublished		= 2;	//	az employee_group-ban tényleg ezt jelenti a kettő
		$stMatchIndirect	= 2;	//	a configban azt jelenti: "ezt a mezőt kivezettük az employee_group-ba"
		$stMatchSimple		= 5;	//	ezt meg nem is használjuk, csak az érthetőséget fokozza drámaian. De.

		$moreFilters	= [];
		$lateFilters	= [];
		$isIndirect		= [];
		$indirectValues	= [];

		$groupConfig = dbFetchAll("select * from employee_group_config","group_id");
		foreach($groupConfig as $x) {
			$gid = $x["group_id"];
			$sts = $x["status"];
			if($sts == $stMatchIndirect) $isIndirect[$gid] = 1;
		}

		$andRelevant	= "and group_id in ('".join("','",array_keys($isIndirect))."')";
		$andValid		= "and valid_from<='$tillDate' and valid_to>='$fromDate' and status='$stPublished'";

		$groupRows = dbFetchAll("select * from employee_group where 1 $andValid $andRelevant");
		$groupRows;	//	~ 5-10 ezer sor a BOS-nél, nem vészes
		foreach($groupRows as $x) {									//	Tehát arról készül egy lookup, hogy:
			$ecid = $x["employee_contract_id"];						//	... ennek az embernek,
			$gpid = $x["group_id"];									//	... ebben az adatában,
			$gval = $x["group_value"];								//	... ez az érték,
			if($gval) $indirectValues[$ecid][$gpid][$gval] = 1;		//	... előfordult-e legalább egyszer.
		}

		#note	[n-7cjvr98m]	Itt van az employee_group tábla, nem olyan vészes

		unset($groupConfig);

		// printf("Number of rows in <b>groupRows</b>: %d\n",count($groupRows));
		// printf("Size of <b>indirectValues</b> lookup: %d\n",strlen(serialize($indirectValues)));

		$allMeansEmpty = flagmap(texplode(",","  company, payroll, workgroup, unit, company_org_group1  "));
		foreach($searchValues as $field=>$value) if($allMeansEmpty[$field]) if($value==="ALL") unset($searchValues[$field]);

		$conditions = [

			"em.company"				=> $searchValues["company"],
			"em.payroll"				=> $searchValues["payroll"],
			"ec.workgroup"				=> $searchValues["workgroup"],
			"em.unit"					=> $searchValues["unit"],
			"em.company_org_group1"		=> $searchValues["company_org_group1"],
			"em.employee"				=> $searchValues["employee_contract"],

		];

		$stMatchIndirect = 2; #question // ez ugye egy status mezőben van, a 2 azt jelentené, hogy published, és mégis, ha az employee_group_configban kettő van, akkor az azt jelenti, hogy "várjálbazmeg, ez nem ilyen egyszerű" - ezt próbáltuk ezzel a változónévvel kifejezni

		foreach($conditions as $field=>$value) if($value) {

			list($table,$field) = explode(".",$field);
			$gid = "{$field}_id";
			$cfg = [];
			$status = valueOrZero($cfg["status"]);
			$simpleMode = ($status<>$stMatchIndirect);

			if($field=="workgroup") $simpleMode = false;

			if( $simpleMode) {$moreFilters[] = "$table.$gid = '$value'";}
			if(!$simpleMode) {$lateFilters[$field] = $value;} // ezeket utólag fogjuk leszűrni

		}

		if($moreFilters) $moreFilters = join(" AND ",$moreFilters);

		$mainQuery = strtr('

			SELECT	ec.employee_id,
					ec.employee_contract_id,
					em.emp_id,
					LEAST    (em.valid_from, ec.valid_from, ec.ec_valid_from) AS employee_min_valid_from,
					GREATEST (em.valid_to,   ec.valid_to,   ec.ec_valid_to  ) AS employee_max_valid_to,
					[[sqlFullName]] AS fullname,
					cp.company_name,
					cp.company_id,
					cp.country

			FROM	employee_contract ec
					LEFT JOIN employee em			ON ec.employee_id			= em.employee_id
					LEFT JOIN company cp			ON cp.company_id			= em.company_id
					LEFT JOIN employee_position ep	ON ep.employee_position_id	= ec.employee_position_id

			WHERE	1

					AND (em.valid_from		<= "[[fMaxDate]]")	AND (em.valid_to		>= "[[fMinDate]]" OR em.valid_to	is null)
					AND (ec.valid_from		<= "[[fMaxDate]]")	AND (ec.valid_to		>= "[[fMinDate]]" OR ec.valid_to	is null)
					AND (ec.ec_valid_from	<= "[[fMaxDate]]")	AND (ec.ec_valid_to		>= "[[fMinDate]]" OR ec.ec_valid_to	is null)
					AND (cp.valid_from		<= "[[fMaxDate]]")	AND (cp.valid_to		>= "[[fMinDate]]" OR cp.valid_to	is null)

					AND em.status = [[stPublished]]
					AND ec.status = [[stPublished]]
					AND cp.status = [[stPublished]]
					AND [[moreFilters]]


			ORDER BY fullname asc, em.row_id desc, ec.row_id desc;

		',[

			"[[stPublished]]"		=> Status::STATUS_PUBLISHED,	// ismertebb nevén KETTŐ
			"[[fMinDate]]"			=> $fromDate,
			"[[fMaxDate]]"			=> $tillDate,
			"[[sqlFullName]]"		=> Employee::getParam('fullname','em'),
			"[[moreFilters]]"		=> $moreFilters ?: "1",

		]);

		$results = dbFetchAll($mainQuery,"employee_contract_id");

		// printf("%5d rows before lateFilters\n",count($results));

		if($lateFilters) {
			$peopleToRemove = [];
			foreach($results as $ecid=>$r) {
				foreach($lateFilters as $field=>$value) {
					$gpid = "{$field}_id";
					$valueFound = isset($indirectValues[$ecid][$gpid][$value]); #note [n-7clqkm5t]
					if(!$valueFound) {
						$peopleToRemove[$ecid] = 1;
						break;
					}
				}
			}
		}

		foreach($peopleToRemove as $ecid=>$whatever) unset($results[$ecid]);
		// printf("%5d rows left\n",count($results));


		$results = array_values($results);
		return $results;

	}

	public static function getdata_employee_absences_list($startDate, $endDate, $ecList=[], $statusList="1,2,3,4") {

		#note	[n-79lcgh9k]	Mire van használva a getEmployeeAbsences egyáltalán
		#todo	[n-7cjbqrwf]	Bent is kell majd egy index az employee_absence táblára (nálunk már van)

		$stPublished = Status::PUBLISHED;

		$absenceQuery = strtr("

			SELECT		state_type_id,
						day,
						status,
						note,
						score,
						absence_hour,
						full_day_absence,
						employee_contract_id

			FROM		employee_absence

			WHERE 1		AND status in ([[statusList]])
						AND day>='[[startDate]]'
						AND day<='[[endDate]]'

			",
			[
				"[[startDate]]"     => $startDate,
				"[[endDate]]"       => $endDate,
				"[[statusList]]"    => $statusList,
			]
		);

		$employeeQuery = strtr("

			SELECT		employee_contract_id,
						em.valid_from     as emvMin,
						em.valid_to       as emvMax,
						ec.valid_from     as ecvMin,
						ec.valid_to       as ecvMax,
						ec.ec_valid_from  as eccMin,
						ec.ec_valid_to    as eccMax

			FROM		employee_contract ec
			LEFT JOIN	employee em on em.employee_id = ec.employee_id

			WHERE 1		AND ec.status='[[stPublished]]'
						AND em.status='[[stPublished]]'
						AND (em.valid_from		<= '[[fMaxDate]]')
						AND (ec.valid_from		<= '[[fMaxDate]]')
						AND (ec.ec_valid_from	<= '[[fMaxDate]]')
						AND (em.valid_to		>= '[[fMinDate]]' OR em.valid_to	is null)
						AND (ec.valid_to		>= '[[fMinDate]]' OR ec.valid_to	is null)
						AND (ec.ec_valid_to		>= '[[fMinDate]]' OR ec.ec_valid_to	is null)

			",
			[
				"[[stPublished]]"	=> $stPublished,
				"[[fMinDate]]"		=> $startDate,
				"[[fMaxDate]]"		=> $endDate,
			]

		);

		$stateQuery = "select * from state_type where status=$stPublished";


		$absences	= dbFetchAll($absenceQuery);
		$employees	= dbFetchAll($employeeQuery	,"employee_contract_id");
		$states		= dbFetchAll($stateQuery	,"state_type_id");	// [n-79lmk7ys]

		foreach($absences as $i=>$r) {
			$state = $states[$r["state_type_id"]];
			$absences[$i][ "name_dict_id"	] = $state[ "name_dict_id"	];
			$absences[$i][ "bgcolor"		] = $state[ "bgcolor"		];
		}

		$fields = linesOf('

			state_type_id
			status
			absence_hour
			full_day_absence
			name_dict_id
			note
			bgcolor

		');

		$out = [];
		foreach($absences as $r) {
			$e = $r["employee_contract_id"];
			$d = $r["day"];
			if(!isset($employees[$e])) continue;

			$er = $employees[$e];
			$emvMin = $er["emvMin"]?:"1999-01-01";		$emvMax = $er["emvMax"]?:"2037-01-01";
			$ecvMin = $er["ecvMin"]?:"1999-01-01";		$ecvMax = $er["ecvMax"]?:"2037-01-01";
			$eccMin = $er["eccMin"]?:"1999-01-01";		$eccMax = $er["eccMax"]?:"2037-01-01";

			if($emvMin>$d) ;//continue;		//	Érthetetlen okból ezek itt nem kellenek, az eredeti
			if($ecvMin>$d) ;//continue;		//	query eredményével akkor lettünk azonosak, ha őket
			if($eccMin>$d) ;//continue;		//	kivettem. Vissza lehet tenni, nagy különbség nincs.

			if($emvMax<$d) continue;
			if($ecvMax<$d) continue;
			if($eccMax<$d) continue;

			$out[$e][$d] = $r;
		}

		return $out;

	}

	public static function getdata_summarysheet_searchform_dropdowns($startDate,$endDate) {

		// $validityRange = [
		// 	"valid_from"	=> $startDate,
		// 	"valid_to"		=> $endDate
		// ];

		$sqlFullname	= Employee::getParam('fullname', 'ee');
		$sqlWorkgroup	= EmployeeGroup::getActiveGroupSQL("workgroup_id","ec");
		$sqlUnit		= EmployeeGroup::getActiveGroupSQL("unit_id","ee");
		$sqlContract	= "ec.employee_contract_id";
		$sqlEmployee	= "ee.employee_id";
		$sqlCompany		= "ee.company_id";
		$joinWorkGroup	= EmployeeGroup::getLeftJoinSQL("workgroup_id","ec");
		$joinUnit		= EmployeeGroup::getLeftJoinSQL("unit_id","ec");

		$validDatesFor = function($field) use ($startDate,$endDate) {
			return "1
				AND $field.valid_from < '$endDate'
				AND $field.valid_to   > '$startDate'
			";
		};

		$valid_ee = $validDatesFor("ee");
		$valid_ec = $validDatesFor("ec");
		$valid_cp = $validDatesFor("cp");
		$valid_ex = $validDatesFor("ex");
		$valid_ec = $validDatesFor("ec");
		$valid_eo = $validDatesFor("eo");

		$sql = "

			SELECT
				$sqlContract as ecid,
				ee.employee_id,
				ee.emp_id,
				$sqlUnit,
				$sqlFullname AS fullname,
				ec.employee_contract_number,
				ee.company_id,
				ee.payroll_id,
				cp.country,
				$sqlWorkgroup as workgroup_id,
				cp.company_name as company_name

			FROM employee ee
				LEFT JOIN company            cp      ON $sqlCompany		= cp.company_id
				LEFT JOIN employee_ext       ex      ON $sqlEmployee	= ex.employee_id
				LEFT JOIN employee_contract  ec      ON $sqlEmployee	= ec.employee_id
				LEFT JOIN employee_cost      eo      ON $sqlContract	= eo.employee_contract_id
				$joinWorkGroup
				$joinUnit

			WHERE 1
				AND  ee.status = 2	AND $valid_ee
				AND  ec.status = 2	AND $valid_ec
				AND  cp.status = 2	AND $valid_cp
				AND  eo.status = 2	AND $valid_eo

			ORDER BY fullname

		";

		$sql = preg_replace('/AND cal.`date` BETWEEN [^\n]+\n/',"",$sql);
		$all = dbFetchAll($sql);
		return $all;

	}

	public static function getdata_flipdays($fromDay,$tillDay,$contractIds,$tablesToCheck) {

		global $Options;

		$ecList = $contractIds; if(is_array($ecList)) $ecList = idList($contractIds);
		$emList = "";

		'Get employee_group-ed values';{

			$egValues = [];
			$egFields = EmployeeGroupConfig::getActiveGroups();
			$egFields = idlist($egFields);
			$egRows = dbFetchAll("

				SELECT		*
				FROM		employee_group
				WHERE 1		AND employee_contract_id IN ($ecList)
							AND group_id in ($egFields)
							AND (valid_from<='$tillDay')
							AND ((valid_to>='$fromDay') OR (valid_to IS NULL))
				;

			");
			foreach($egRows as $r) {
				$ec = $r["employee_contract_id"];
				$gk = $r["group_id"];
				$gv = $r["group_value"];
				$d1 = $r["valid_from"];
				$d2 = $r["valid_to"];
				$egValues[$ec][$gk]["$d1 .. $d2"] = $gv;
			}

			print_r($egValues);
			print_r($egRows);
			exit;

		}

		'Get list of employee_id values';{
			$validByDate    = " valid_to>='$fromDay' and valid_from<='$tillDay'";
			$relevantById   = "employee_contract_id in ($ecList)";
			$ec = dbFetchAll("select * from employee_contract where $relevantById and $validByDate");
			$em = array_column($ec,"employee_id");
			$em = array_unique($em);
			$emList = idList($em);
		}

		$dates = [];

		$getDatesFor = function($tableName,$fieldCondition) use ($emList,$ecList,$fromDay,$tillDay) {

			#dontForget // employee.valid_xx határdátumok is kellenek
			#dontForget // employee_contract.valid_xx is
			#dontForget // employee_contract.ec_valid.xx is

			list($connectorTable,$field,$otherField) = explode(",","$fieldCondition,,,");
			if($connectorTable=="em") $relevantPeople = "c.employee_id in ($emList)";
			if($connectorTable=="ec") $relevantPeople = "c.employee_contract_id in ($ecList)";
			if($connectorTable=="em") $connectorTable = "employee";
			if($connectorTable=="ec") $connectorTable = "employee_contract";
			if(!$field) $field = "{$connectorTable}_id";
			if(!$otherField) $otherField = $field;
			$published = "t.status=2";
			$withinRange = "(0
				OR (t.valid_from >= '$fromDay' AND t.valid_from <= '$tillDay' AND t.valid_from IS NOT NULL)
				OR (t.valid_to   >= '$fromDay' AND t.valid_to   <= '$tillDay' AND t.valid_to   IS NOT NULL)
			)";
			$sql = "

				SELECT		t.valid_from, t.valid_to
				FROM		$connectorTable c
				LEFT JOIN	$tableName t ON t.$otherField = c.$field
				WHERE		$relevantPeople AND $withinRange AND $published

			";
			try     {$dates = dbFetchAll($sql);}
			catch   (Exception $e) {die("Problem getting dates for $tableName:\n".strtr($e->getMessage(),[":"=>":\n"]));}

			$out = [];
			foreach($dates as $d) {$x=$d["valid_from"];if($x<2038) $out[$x] = 1;}
			foreach($dates as $d) {$x=$d["valid_to"  ];if($x<2038) $out[$x] = 1;}
			ksort($out);

			$d1 = key($out);end($out);
			$d2 = key($out);
			$skips = dayDiff($fromDay,$tillDay) - count($out);
			$count = count($out);

			return $out;

		};

		// Ebből indultam ki: http://innote-dk.local/n-7wwls6b8

		$dates["company"            ] = $getDatesFor("company"              ,"em,company_id");
		$dates["employee_ext"       ] = $getDatesFor("employee_ext"         ,"em");
		$dates["employee_position"  ] = $getDatesFor("employee_position"    ,"ec,employee_position_id");
		$dates["employee_cost"      ] = $getDatesFor("employee_cost"        ,"ec");
		$dates["employee_card"      ] = $getDatesFor("employee_card"        ,"ec");
		$dates["employee_group"     ] = $getDatesFor("employee_group"       ,"ec");

		$relevantDays = [];foreach($dates as $table=>$days) foreach($days as $day=>$x) $relevantDays[$day] = 1;
		$relevantDays = array_merge($relevantDays,[$fromDay=>1,$tillDay=>1]);
		$relevantDays = array_keys($relevantDays); sort($relevantDays);
		$relevantDays = idList($relevantDays);

		return "cal.date in ($relevantDays)";

	}

}


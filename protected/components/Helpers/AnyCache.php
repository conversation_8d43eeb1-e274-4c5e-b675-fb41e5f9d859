<?php

'yii2-only`;

	namespace app\components\Helpers;
	use app\components\Helpers\Spector;
	use Yang;

`/yii2-only';


class AnyCache { #see AnyCache.overview

	public static $enabled = true;

	//	"d:/login/dk.switches/cache.off"

	public static function key($name,$anyDataToHash) { #see AnyCache.key
		$k = serialize($anyDataToHash);
		$k = md5($k);
		$k = "$name,[$k]";
		return $k;

	 }
	public static function set($key,$value,$keywords="") { #see AnyCache.set
		if(!is_array($keywords)) $keywords = texplode(",",$keywords);
		$keywords = array_fill_keys($keywords,1);
		$_SESSION["AnyCache"][$key]["value"] = $value;
		$_SESSION["AnyCache"][$key]["words"] = $keywords;
		return $value;
	 }
	public static function setOnce($key,$value,$keywords="") { #see AnyCache.setOnce
		if(isset($_SESSION["AnyCache"][$key])) return;
		self::set($key,$value,$keywords);
	 }
	public static function get($key,$defaultValue=false) { #see AnyCache.get
		if(!self::$enabled) return $defaultValue;
		if(!isset($_SESSION["AnyCache"][$key])) return $defaultValue;
		return $_SESSION["AnyCache"][$key]["value"];
	 }
	public static function has($key) { #see AnyCache.has
		if(!self::$enabled) return false;
		return isset($_SESSION["AnyCache"][$key]);
	 }
	public static function clear($key="*") { #see AnyCache.clear
		if($key=="*") $_SESSION["AnyCache"] = [];
		if($key<>"*") $_SESSION["AnyCache"][$key] = [];
	 }
	public static function invalidate($keyword) { #see AnyCache.invalidate
		if(!trim($keyword)) return;
		$out = 0; // vissza fogjuk adni, hogy hányat invalidáltunk ténylegesen
		$keysToClear = [];
		$ac = $_SESSION["AnyCache"];
		foreach($ac as $key=>$data) if(isset($data["words"][$keyword])) $keysToClear[] = $key;
		foreach($keysToClear as $k) {
			if(self::has($k)) ++$out;
			self::clear($k);
		}
		return $out;
	 }
	public static function invalidateBySQL($queryString) {

		$q = $queryString;
		$q = preg_replace('/\s+/'," ",trim($q));

		list($cmd,$rest) = explode(' ',$q,2);
		$cmd = strtolower($cmd);

		static $alteringCommands;
		if(!$alteringCommands) {
			$x = "replace,update,delete,insert";
			$x = explode(",",$x);
			$x = array_fill_keys($x,1);
			$alteringCommands = $x;
		}
		if(!$alteringCommands[$cmd]) return;

		static $skipWords;
		if(!$skipWords) {
			$possibleWordsBeforeTableName = "low_priority,high_priority,ignore,delayed,into,from,quick";
			$foreWords = explode(",",$possibleWordsBeforeTableName);
			$skipWords = array_fill_keys($foreWords,1);
		}

		$words = explode(' ',$rest);
		$table = "";
		for($i=0;$i<9;++$i) {
			$guess = array_shift($words);
			$lower = strtolower($guess);
			if(!$skipWords[$lower]) {$table=$guess;break;}
		}
		$table = trim($table,'\r\t\n `'); if(!$table) return;

		$c = self::invalidate($table);

		if(useExperimental("log query based cache invalidations")) if($c) {
			$now = date("Y-m-d H:i:s");
			$nqs = onespace($queryString,'nolf bitte');
			$msg = "$now\tinvalidated $c keys tagged '$table' because of this query: $nqs\n";
			file_put_contents('d:/debug/AnyCache invalidations.txt',$msg,FILE_APPEND);
		}

	 }
	public static function imagine($keyToCheck,$correctValue,$logTo="both",$file="",$line="") {

		do{

			if(!self::has($keyToCheck)) {$resp = "not stored";break;}
			$cachedValue = self::get($keyToCheck);
			if($cachedValue === $correctValue) $resp = "perfect";
			if($cachedValue !== $correctValue) $resp = "cached value differs";

		}while(0);

		if(oneof($logTo,"file,both")) {
			$time = date("H:i:s");
			$jkey = json_encode($keyToCheck);
			$jkey = fixlen($jkey,40);
			$ttwa = dirname(dirname(dirname(__DIR__))); // ne mondd, hogy nem szaladt le egy könnycsepp.
			$file = basename($file);
			$file = fixlen($file,30);
			$line = fixlen($line,5);
			$logf = "$ttwa/runtime/anycache.imagine.log";
			file_put_contents($logf,"$time | $jkey | $file | $line | $resp\n",FILE_APPEND);
			Spector::logOutput("imagine.correct",$correctValue);
			Spector::logOutput("imagine.cached" ,$cachedValue);

		}
		if(oneof($logTo,"mike,both")) {
			mDoing("imagine: <b>$resp</b>");
		}

		return $resp;

	 }

	public static function check($nicename,$keyToCheck,$correctValue,$cachedValue=null) {
		if(is_null($cachedValue)) $cachedValue = self::get($keyToCheck);
		$cr = niceValue($correctValue);
		$cc = niceValue($cachedValue);
		if($cachedValue === $correctValue)	$resp = "Cache hit";
		if($cachedValue !== $correctValue)	$resp = "CACHED VALUE DIFFERS ($cr vs $cc)";
		if(!self::has($keyToCheck))			$resp = "Not in cache";
		$time = date("Y-m-d H:i:s");
		$path = debugPath();
		file_put_contents("$path/anycache.$nicename.txt","$time\t$resp\n",FILE_APPEND);
	}

	public static function destroy($key="*") {
		if(isset($_SESSION["AnyCache"][$key])) unset($_SESSION["AnyCache"][$key]);
	}

	public static function destroyByName($name="") {
		if(!trim($name)) return;
		$out = 0; // vissza fogjuk adni, hogy hányat invalidáltunk ténylegesen
		$keysToClear = [];
		$ac = $_SESSION["AnyCache"] ?? [];
		foreach($ac as $key=>$data) if(strpos($key,$name)!==false) $keysToClear[] = $key;
		foreach($keysToClear as $k) {
			if(self::has($k)) ++$out;
			self::destroy($k);
		}
		return $out;
	}

}


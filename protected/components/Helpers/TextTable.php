<?php #yii2: done

'yii2-only`;

	namespace app\components\Helpers;
	use Yang;

`/yii2-only';


class TextTable {


	public static function expandTabs($s,$tabSize=4) {
	    $a = explode("\n",$s);
	    foreach((array) $a as $index=>$row) {
	        $e=&$a[$index];

	        $bits = explode("\t",$e);
	        $e="";
	        foreach($bits as $bit) {
	            $e.=$bit." ";
	            $n = strlen($e)%$tabSize;
	            if($n) $e.=str_repeat(" ",$tabSize-$n);
	        }
	        $e = rtrim($e);
	        $e = trim($e,"\r");

	    }
	    $s = join("\n",$a);
	    return $s;
	 }
	public static function parse($s,$indexBy="") {

		if(false!==strpos($s,"\t")) $s = self::expandTabs($s);

		$s = preg_replace('/^\s*\n/',"",$s); // keep leading spaces of first valuable line
		$s = rtrim($s);
		$lines = explode("\n",$s);
		$header = array_shift($lines);

		$spacePrefix = 3; $spfx=str_repeat(" ",$spacePrefix); // help regex find the first column
		preg_match_all('/\s\s+([^\s]+)/',"$spfx$header",$a,PREG_OFFSET_CAPTURE);
		$a; // now we have the positions and the field names

		$matches = $a[1];
		$positions = [];
		$lengths = []; $prevPos = 0;
		foreach($matches as $m) {
			$name = $m[0];
			$pos  = $m[1] - $spacePrefix; // we added some spaces at start, remember?
			$len  = 0; // gets filled later
			$positions[$name] = ["pos"=>$pos,"len"=>$len];
			$lengths[] = $pos - $prevPos;  $prevPos=$pos;
		}
		array_shift($lengths); foreach($positions as $name=>$x) $positions[$name]["len"]=array_shift($lengths)?:9999;
		$positions; // now we have an array of each fieldname => [pos,len]

		$data = [];
		foreach($lines as $nr=>$line) {
			if(substr(trim($line),0,3)=="---") continue; // separator
			foreach($positions as $key=>$p) {
				$value = trim(mb_substr($line,$p["pos"],$p["len"]));
				$data[$nr][$key] = $value;
			}
		}

		$out = $data;

		if($indexBy) {
			$out = [];
			foreach($data as $row) {
				$key = $row[$indexBy];
				if(!$key) continue;
				$out[$key] = $row;
			}
		}

		return $out;

	 }
	public static function linesOf($s) {
		$txt = trim($s);
		$out = preg_split('/\s*\n\s*/',$txt);
		return $out;
	 }


}







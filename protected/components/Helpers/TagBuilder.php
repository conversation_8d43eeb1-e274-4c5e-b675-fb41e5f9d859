<?php #yii2: done

'yii2-only`;

	namespace app\components\Helpers;
	use Yang;

`/yii2-only';


class TagBuilder {

	public $tag = "";
	public $props = [];
	public $style = [];
	public $class = [];
	public $inside = "";

	public function __construct($tagName) {$this->tag = $tagName;} // use slash at the end for self-closing
	public function prop ($name,$value='') {$this->props[$name] = $value;}
	public function addStyle($name,$value='') {$this->style[$name] = $value;}
	public function addClass($name,$add=1) {$this->class[$name] = $add;}
	public function addInside($html) {$this->inside = $html;}
	public function openingTag($glue=" ") {

		$tag = $this->tag;
		$isSelfClosing = (substr($tag,-1)=="/");
		if($isSelfClosing) $tag = rtrim($tag,"/");

		$classDef = "";foreach((array)$this->class as $x=>$c) if($c) $classDef.="$x ";
		$styleDef = "";foreach((array)$this->style as $k=>$v) $styleDef.="$k:$v;";
		$propsDef = "";foreach((array)$this->props as $k=>$v) {
			if( $v) $propsDef.=$glue.sprintf('%s="%s"',$k,strtr($v,['"'=>'&quot;']));
			if(!$v) $propsDef.=$glue.sprintf('%s',$k);
		}

		$classDef = rtrim($classDef);
		$styleDef = rtrim($styleDef,";");
		$propsDef = rtrim($propsDef);

		$s = "<$tag";
		if($classDef) $s.= $glue.'class="'.$classDef.'"';
		if($styleDef) $s.= $glue.'style="'.$styleDef.'"';
		if($propsDef) $s.= $propsDef;
		if($isSelfClosing) $s.=" /";
		$s.=">";

		return $s;

	}
	public function closingTag() {

		$tag = $this->tag;
		$isSelfClosing = (substr($tag,-1)=="/");
		if($isSelfClosing) return "";
		return "</$tag>";

	}
	public function nicehtml() {
		$o = $this->openingTag("\n\t");
		$c = $this->closingTag();
		$h = $c ? $this->inside : ""; // self-closing tags don't have inside
		$h = "$o\n\n\t$h\n\n$c";
		return $h;
	}

	public function html() {

		$o = $this->openingTag();
		$c = $this->closingTag();
		$h = $c ? $this->inside : ""; // self-closing tags don't have inside
		$h = "$o$h$c";

		return $h;

	}

	public function __toString() {
		return (string)$this->html();
	}

}







<?php #yii2: done

'yii2-only`;

	namespace app\components\Helpers;
	use app\components\Helpers\Spector;
	use Yang;

`/yii2-only';


class Spector {

	const Path = __DIR__."/../../../_spector";

	public static function codeFor($v) {

		$dstr = serialize($v);
		$code = substr(md5($dstr),0,9);

		if($v==="")		$code="empty";
		if($v===false)	$code="false";
		if(is_null($v))	$code="null";

		return $code;
	}

	public static function filenameFor($name,$v) {

		$code = Spector::codeFor($v);
		$filename = fileIn(Spector::Path,"$name.$code.out");
		mkdir(dirname($filename),0775,true);
		return $filename;

	}

	public static function json($v) {
		$json = json_encode($v,JSON_PRETTY_PRINT + JSON_UNESCAPED_SLASHES + JSON_UNESCAPED_UNICODE);
		return $json;
	}

	public static function look($name,$value) {
		if(is_array($value)) $value = self::json($value);
		$file = Spector::filenameFor($name,$value);
		$good = preg_replace("/\.out$/",".good",$file);
		if(file_exists($good)) $file = $good;
		file_put_contents($file,$value);
	}

	public static function logOutput($name,$outputValue,$raw=false) {
		$file = Spector::filenameFor($name,$outputValue);
		if(!$raw) file_put_contents($file,Spector::json($outputValue));
		if( $raw) file_put_contents($file,$outputValue);
	}

	public static function logOutputClean($name,$data,$cleaner=null) {

		$customCleaner = (is_callable($cleaner));
		if($customCleaner) {
			$data = $cleaner($data);
			$file = Spector::filenameFor($name,$data);
			$text = $data;
		}else{
			$data = serialize($data);
			$data = strtr($data,"\t\r\n","   ");
			$data = unserialize($data);
			$file = Spector::filenameFor($name,$data);
			$json = Spector::json($data);
			$json = preg_replace('/  +/'," ",$json);
			$text = $json;
		}
		file_put_contents($file,$text);
	}

	public static function assertEqual($name,$v1,$v2,$strict=false) {
		static $append=0;
		if( $strict) $ok = ($v1===$v2);
		if(!$strict) $ok = ($v1 ==$v2);
		$x1 = is_scalar($v1) ? $v1 : json_encode($v1);
		$x2 = is_scalar($v2) ? $v2 : json_encode($v2);
		$logLine = ($ok) ? "$name: ok			($x1)\n" : "$name: mismatch\n\t$x1\n\t$x2\n";
		$filename = fileIn(Spector::Path,"assert.$name.txt");
		file_put_contents($filename,$logLine,$append);
		$append = FILE_APPEND;
	}

	public static function watch($a) {
		static $append=0;
		$filename = fileIn(Spector::Path,"watch.txt");
		$jsonLine = json_encode($a,JSON_UNESCAPED_SLASHES + JSON_UNESCAPED_UNICODE); // but not pretty_print!!
		$jsonLine ="$jsonLine\n";
		file_put_contents($filename,$jsonLine,$append);
		$append = FILE_APPEND;
	}

	public static function error($msg) {
		$now = date("Y-m-d H:i:s");
		file_put_contents(fileIn(Spector::Path,"errors.txt"),"$now\t$msg\n");
		return false;
	}

	public static function deleteLogs($fileMask) {
		$path = Spector::Path;
		$list = glob("$path/$fileMask");
		foreach($list as $f) {
			if(!contains($f,"_spector")) continue; // extra safety
			unlink($f);
		}
	}

	public static function compare($name,$a,$b,$logIfEquals=1) {
		try{
			if(!$logIfEquals) if($a===$b) return;
			if(!$name) $name="unnamed";
			$ja = json_encode($a);
			$jb = json_encode($b);
			$path = self::Path; //if(is_dir("d:/debug")) $path = "d:/debug";
			$file = "$path/cmp.$name.log";
			$time = date("Y-m-d, H:i:s");
			$diff = ($a===$b) ? "          ":"|##########";
			$line = "".
				"$diff| $ja\n".
				"$diff| $jb\n".
			"\n";
			clearstatcache();
			if (is_file($file)) {
				$age = time()-filemtime($file);
				if($age>1) $line = "$time\n--------------------\n$line";
				file_put_contents($file,$line,FILE_APPEND);
			}
		}catch(\Exception $e) {
			die("Spector has a problem");
		}
	}

}


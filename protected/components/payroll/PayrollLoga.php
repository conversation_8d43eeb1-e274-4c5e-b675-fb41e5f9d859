<?php #yii2: done

'yii2-only`;

	namespace app\components\payroll;
	use app\components\API\WriteUserEvent;
	use app\components\wfm\API\GetWorktimePayrollFast;
	use Yang;

`/yii2-only';


trait PayrollLoga
{

	protected function getJelenletSQL()
	{
		$filter='';
		if (!is_null(Yang::session('payroll_report_filters')))
		{
			$filter = Yang::session('payroll_report_filters');
		}
		if(!empty($filter))
		{
			$valid_month=$filter['yearMonth'];
			$year = substr($valid_month,0,4);
			$month = substr($valid_month,5,2);
			if(substr($month,0,1) == '0')
			{
				$month = substr($month, 1, 1);
			}
			$monthBegin=$valid_month."-01";
			$monthEnd =  date('Y-m-t',mktime(0, 0, 0, $month, 1, $year));
			$sql= "SELECT date, e.*  FROM calendar"
				. " LEFT JOIN (SELECT company_id_ts, payroll_id_ts, emp_id_ts"
				. " FROM daily_datas_tmp GROUP BY company_id_ts, payroll_id_ts, emp_id_ts) e ON 1"
				. " where date>='$monthBegin' and date<='$monthEnd'"
				. " AND company_id_ts is not null AND payroll_id_ts is not null AND emp_id_ts is not null";
			$sqlb = "SELECT e.*, IF(IFNULL(type_of_daytype_saved_ts, '')='WORKDAY', 1, 0) AS daytype"
				. " FROM ($sql) e"
				. " LEFT JOIN daily_datas_tmp d ON"
				. "		d.date=e.date"
				. " AND d.company_id_ts=e.company_id_ts"
				. " AND d.payroll_id_ts=e.payroll_id_ts"
				. " AND d.emp_id_ts=e.emp_id_ts"
				. " ORDER BY e.company_id_ts, e.payroll_id_ts, e.emp_id_ts, e.date";
			$sql = "SELECT '[SOLLTG]', 'INSERT', company_id_ts, payroll_id_ts, emp_id_ts, 1, CONCAT(";
			$px='';
			for ($day = 1; date('Y-m-d',mktime(0, 0, 0, $month, $day, $year)) <= $monthEnd; $day++)
			{
				$sql .=$px."IF(SUM(IF(date='".date('Y-m-d',mktime(0, 0, 0, $month, $day, $year))."', daytype, 0))=0, '_', '1')";
				$px=', ';
			}
			$sql .=") FROM ($sqlb) e GROUP BY e.company_id_ts, e.payroll_id_ts, e.emp_id_ts";
			return $sql;
		}
		return "";
	}

	function getUserSQL($from="daily_datas_tmp", $tag='', $time=true)
	{
		$sql="";
		$select='SELECT ';
		foreach ($this->userFields as $key => $value) 
		{
			WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['Key' => $key]);
			WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, $value);
			if (($value['due'] == 1) && ($value['view']==1) && (empty($value['tag']) || $value['tag']==$tag))
			{
				$newkey=$key;
				$timeFunction=$this->timeFunction;
				if(!empty($value['time_function'])) $timeFunction=$value['time_function'];
				if ($value['formula'] == 1) $newkey=$value['lfield'];
				$sql .= $select."* FROM (";
				if ($tag=="tav")
				{
					$sql .= "SELECT '[ZEITENKAL]' AS teteltipus, 'INSERT' AS type, "
						. "tavazon AS azonosito, '' AS eredet, '' AS datum, '' AS felh, ceg, "
						. "payroll, torzsszam, keresztnev, nev, '1' AS szerzodesszam, "
						.$this->mysqlDateFormat('date')." AS kezdet, "
						.$this->mysqlDateFormat('date')." AS veg, '' AS kezdet2, '' AS foglalt1, '' AS veg2, '' AS foglalt2, "
						. "'".$value['code']."' AS jogcim, 'I' AS tipus, '' AS munkanapok, '' AS naptarinapok, "
						. "'' AS megjegyzes, ";
					$which_from=$value['which']."_from";
					$which_to=$value['which']."_to";
					{
						$sql .= "'' AS value";
						$sql .= ", '' AS koltseghely, '' AS terhelendo, '' AS koltsegviselo, '' AS manualis, '' AS betegazon, "
							. "'' AS szorzo, "
							. "'' AS szorzotip, '' AS gyerekazon, '' AS tartalek1, '' AS tartalek2, '' AS tartalek3"
						. " FROM $from where $newkey != 0) t";
					}
				}
				elseif ($tag=="val")
				{
					$sql .= "SELECT '[VARTAB]' AS teteltipus, 'INSERT' AS type, "
						. "ceg, payroll, torzsszam, keresztnev, nev, 1 AS szerzodesszam, "
						. "'".$value['code']."' AS jogcim, ";
					$which_from=$value['which']."_from";
					$which_to=$value['which']."_to";
					if($this->payrollUser=="eurocircuits")
					{
						if ($value['time'] == 1 && !empty($timeFunction))
						{
							$sql .= "'' AS napok, ";
							$sql .= $timeFunction."(IF(date BETWEEN $which_from AND $which_to, SUM($newkey), 0)) AS value";
						}
						else
						{
							$sql .= "IF('".$value['code']."' IN ('120','130'), IF(date BETWEEN $which_from AND $which_to, SUM($newkey), 0), '') AS napok";
							$sql .= ", '' AS value";
						}
					}
					else
					{
						$sql .= "'' AS napok, ";
					
						if ($value['time'] == 1 && !empty($timeFunction))
						{
							$sql .= $timeFunction."(IF(date BETWEEN $which_from AND $which_to, SUM($newkey), 0)) AS value";
						}
						else
						{
							$sql .= "IF('".$value['code']."' IN ('020','021','022'), '', IF(date BETWEEN $which_from AND $which_to, SUM($newkey), 0)) AS value";
						}
					}
					$sql .= ", '' AS szorzo, '' AS osszeg, '' AS koltseghely, '' AS koltsegnem, "
						. "'' AS koltsegviselo, '' AS datum, '".$this->actYearMonth."-01' AS honap, "
						. "'' AS eredet, '' AS eredetdatum, '' AS szazalek, '' AS terhelendo, '' AS felh, "
						. "IF('".$value['code']."' IN ('020','021','022'), IF($newkey>0, 1,''), '') AS darab, '' AS elteroceg, "
						. "'' AS elteroszk, '' AS kalkpoz, '' AS szoveg, '' AS tevkezd, '' AS tevveg, "
						. "'' AS atalanyado, '' AS forgalmiado, '' AS bizonylatszam, '' AS szolgcsop, "
						. "'' AS alcsoport1, '' AS alcsoport2, '' AS alcsoport3 "
						. "FROM $from GROUP BY ceg, torzsszam) s where (value != 0) OR (napok != 0) OR (darab = 1)";
					
				}
				$select=' UNION SELECT ';
			}
		}
		$sql = "SELECT * FROM ($sql) r ORDER BY ceg, torzsszam, jogcim";
		if ($tag=='tav')
		{
			$sql .= ", kezdet";
		}
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['User SQL' => $sql]);
		return $sql;
	}
	
	public function getCorrectionOrderBy($tag='')
	{
		if ($tag=='tav')
		{
			return "ceg, torzsszam, jogcim, kezdet";
		}
		else
		{
			return "ceg, torzsszam, jogcim";
		}
	}

	public function getFixFields($tag="")
	{
		$ret=array();
		if ($tag=="tav")
		{
			$ret['teteltipus']		="Satzart";
			$ret['type']			="Funktion";
			$ret['azonosito']		="ZK_UNIQID";
			$ret['eredet']			="ZK_HER";
			$ret['datum']			="ZK_HER_DATE";
			$ret['felh']			="ZK_USERID";
			$ret['ceg']				="MAN";
			$ret['payroll']			="AK";
			$ret['torzsszam']		="PNR";
			$ret['keresztnev']		="Name";
			$ret['nev']				="Vorname";
			$ret['szerzodesszam']	="VERTNR";
			$ret['kezdet']			="ZK_VON";
			$ret['veg']				="ZK_BIS";
			$ret['kezdet2']			="ZK_VONDAT2";
			$ret['foglalt1']		="reserviert";
			$ret['veg2']			="ZK_BISDAT2";
			$ret['foglalt2']		="reserviert";
			$ret['jogcim']			="ZK_SYMBOL";
			$ret['tipus']			="ZK_PLANAN";
			$ret['munkanapok']		="ZK_ANZARBTA";
			$ret['naptarinapok']	="ZK_ANZKALT";
			$ret['megjegyzes']		="ZK_BEMERK";
			$ret['value']			="ZK_STUND";
			$ret['koltseghely']		="ZK_KST";
			$ret['terhelendo']		="ZK_KSTB";
			$ret['koltsegviselo']	="ZK_KTR";
			$ret['manualis']		="ZK_MANU";
			$ret['betegazon']		="ZK_BEZDAT";
			$ret['szorzo']			="ZK_FAKT";
			$ret['szorzotip']		="ZK_FART";
			$ret['gyerekazon']		="FAM_COUNT";
			$ret['tartalek1']		="Tartalék";
			$ret['tartalek2']		="Tartalék";
			$ret['tartalek3']		="Tartalék";
		}
		else
		{
			$ret['teteltipus']		="Satzart";
			$ret['type']			="Funktion";
			$ret['ceg']				="MAN";
			$ret['payroll']			="AK";
			$ret['torzsszam']		="PNR";
			$ret['keresztnev']		="Name";
			$ret['nev']				="Vorname";
			$ret['szerzodesszam']	="VERTNR";
			$ret['jogcim']			="LA";
			$ret['napok']			="VD_ETAGE";
			$ret['value']			="VD_ESTD";
			$ret['szorzo']			="VD_EFAKT";
			$ret['osszeg']			="VD_EBETRAG";
			$ret['koltseghely']		="KST";
			$ret['koltsegnem']		="KOSTART";
			$ret['koltsegviselo']	="VD_KTR";
			$ret['datum']			="VD_DAT";
			$ret['honap']			="VD_ZDAT";
			$ret['eredet']			="VD_HER";
			$ret['eredetdatum']		="VD_HER_DATE";
			$ret['szazalek']		="VD_PROZ";
			$ret['terhelendo']		="VD_KSTB";
			$ret['felh']			="VD_USERID";
			$ret['darab']			="VD_WERT";
			$ret['elteroceg']		="KST2_MAN";
			$ret['elteroszk']		="KST2_AK";
			$ret['kalkpoz']			="VD_KALK";
			$ret['szoveg']			="ABR_TEXT";
			$ret['tevkezd']			="TAET_VON";
			$ret['tevveg']			="TAET_BIS";
			$ret['atalanyado']		="PRZ_AUS_PST";
			$ret['forgalmiado']		="PRZ_AUS_UST";
			$ret['bizonylatszam']	="VD_BELEG";
			$ret['szolgcsop']		="DIENGR";
			$ret['alcsoport1']		="VD_LAUA1";
			$ret['alcsoport2']		="VD_LAUA2";
			$ret['alcsoport3']		="VD_LAUA3";
		}
		$this->fixField=$ret;
		return $ret;
	}

	public function getBaseTimesheet($month, $payroll=0, $exit=0, $employee=0, $company='ALL', $workgroup=0) 
	{
		$gwt = new GetWorktimePayrollFast($month, $payroll, $exit, $employee, $company, false, false);
		return $gwt->getWorktimeSql();		
	}

	
//SELECT at.absenceid, at.name, stt.* FROM `absence_tologa` at left join states st ON st.absenceid=at.absenceid left join old_new_state ns on ns.old_id=st.id left join state_type stt on stt.state_type_id=ns.new_id order by at.absenceid
//SELECT s.`state_type_id`, d.dict_value from state_type s left join dictionary d on d.dict_id=s.`name_dict_id` WHERE lang='hu'
	
}

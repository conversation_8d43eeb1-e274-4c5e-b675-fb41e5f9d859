<?php #yii2: done

'yii2-only`;

	namespace app\components\payroll;
	use app\components\App;
	use <PERSON>;

`/yii2-only';


if (file_exists(Yang::getBasePath()."/controllers/customers/".App::getSetting("controllersCustomersName")."/TimeSheet.php"))
{
	Yang::import("application.controllers.customers.".App::getSetting("controllersCustomersName").".TimeSheet");
}
else
{
	Yang::import("application.controllers.customers.common.TimeSheet");
}

Class TSCustomerController extends TSController
{
	use TimeSheet;
	
	public function __construct($construct="wfm/tSCustomer")
	{
		parent::__construct($construct);
	}
	
}


<?php #yii2: done

'yii2-only`;

	namespace app\components\payroll;
	use app\components\API\WriteUserEvent;
	use app\components\App;
	use app\components\Dict;
	use Yang;

`/yii2-only';


trait GetTSCommonFields
{

	protected $tsParams=[];

	protected function setTSParams()
	{
		if (count($this->tsParams)>0) return;

		$this->tsParams['ktgColumn']			= (App::getSetting("timesheet_ktg")==0)?false:true;
		$this->tsParams['absenceWorktime']		= App::getSetting("absenceWorktime");
		$this->tsParams['balanceFrameFrom']		= App::getSetting("balanceFrameFrom");
		$this->tsParams['weekendRestday']		= App::getSetting("weekendRestday");
		$this->tsParams['weekWorktimeCount']	= 1;
		/*weekWorktimeCount
		 * 0: semmit nem arányo<PERSON>ítunk
		 * 1: 40-et ar<PERSON>yo<PERSON><PERSON><PERSON><PERSON><PERSON>, 8-at nem
		 * 2: 40-et és 8-at is arányosítjuk
		 */

	}

	protected function getAllSuffixTS()
	{
				$sql  = $this->getBaseSuffixTS();
				$sql .= "
			) a
				";
				$sql .= $this->getOtherBaseSuffixTS();
				$sql .= "
		)r
				";
				$sql .= $this->getFrameBaseSuffixTS();
				$sql .= "
		";
		return $sql;
	}

	protected function getAllSuffixPayroll()
	{
				$sql  = $this->getBaseSuffixPayroll();
				$sql .= "
			) a
				";
				$sql .= $this->getOtherBaseSuffixPayroll();
				$sql .= "
		)r
				";
				$sql .= $this->getFrameBaseSuffixPayroll();
				$sql .= "
		";
		return $sql;
	}

	protected function getAllPrefixTS()
	{
		$sql = "
		SELECT
			";
			$sql .= $this->getFrameBasePrefixTS();
			$sql .= "
		FROM
		(
			SELECT
				";
			$sql .= $this->getOtherBasePrefixTS();
			$sql .= "
			FROM
			(
				SELECT
					";
			$sql .= $this->getBasePrefixTS();
		return $sql;
	}

	protected function getAllPrefixPayroll()
	{
		$sql = "
		SELECT
			";
			$sql .= $this->getFrameBasePrefixPayroll();
			$sql .= "
		FROM
		(
			SELECT
				";
			$sql .= $this->getOtherBasePrefixPayroll();
			$sql .= "
			FROM
			(
				SELECT
					";
			$sql .= $this->getBasePrefixPayroll();
		return $sql;
	}

	protected function getPrefixCserel($hol,$mire)
	{
		return str_replace('}', $mire, $hol);
	}

	protected function getBasePrefixPayroll()
	{
		return $this->getPrefixCserel($this->getBasePrefix(false),'_ts');
	}

	protected function getBasePrefixTS()
	{
		return $this->getPrefixCserel($this->getBasePrefix(true),'');
	}

	protected function getOtherBasePrefixPayroll()
	{
		return $this->getPrefixCserel($this->getOtherBasePrefix(),'_ts');
	}

	protected function getOtherBasePrefixTS()
	{
		return $this->getPrefixCserel($this->getOtherBasePrefix(),'');
	}

	protected function getFrameBasePrefixPayroll()
	{
		return $this->getPrefixCserel($this->getFrameBasePrefix(false),'_ts');
	}

	protected function getFrameBasePrefixTS()
	{
		return $this->getPrefixCserel($this->getFrameBasePrefix(true),'');
	}

	protected function getFrameBaseSuffixTS()
	{
		return $this->getFrameBaseSuffix();
	}

	protected function getFrameBaseSuffixPayroll()
	{
		return $this->getFrameBaseSuffix('_ts');
	}

	protected function getOtherBaseSuffixPayroll()
	{
		return $this->getOtherBaseSuffix('_ts');
	}

	protected function getOtherBaseSuffixTS()
	{
		return $this->getOtherBaseSuffix();
	}

	protected function getBaseSuffixPayroll()
	{
		return $this->getBaseSuffix('_ts');
	}

	protected function getBaseSuffixTS()
	{
		return $this->getBaseSuffix();
	}

	protected function getFrameBaseSuffix($suffix='')
	{
		return $this->getPrefixCserel("LEFT JOIN (SELECT @date:='2038-12-31', @eci:='', @munkaido:=0, @tulora_nem:=0, @balance:=0, @tulora:=0, @adatnapw:=0, @adatnapwe:=0, @adatora:=0) v ON 1", $suffix);
	}

	protected function getOtherBaseSuffix($suffix='')
	{
		return "";
	}

	protected function getBaseSuffix($suffix='')
	{
		return $this->getPrefixCserel("GROUP BY `employee_contract_id}`, `date}`", $suffix);
	}

	protected function getFrameBasePrefix($ts=false)
	{
		$this->setTSParams();
		$absenceWorktime	= $this->tsParams['absenceWorktime'];
		$weekendRestday		= $this->tsParams['weekendRestday'];
		$weekWorktimeCount	= $this->tsParams['weekWorktimeCount'];
		$balanceFrameFrom	= $this->tsParams['balanceFrameFrom'];
		$ktgColumn			= $this->tsParams['ktgColumn'];
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['balanceFrameFrom' => $balanceFrameFrom]);

		$sql = "
			r.*,
			IF(paid_holiday_count} != 0,ot_de_saved}, 0)									AS ot_holiday_de},
			IF(paid_holiday_count} != 0,ot_du_saved}, 0)									AS ot_holiday_du},
			IF(paid_holiday_count} != 0,ot_du1_saved}, 0)									AS ot_holiday_du1},
			IF(paid_holiday_count} != 0,ot_du2_saved}, 0)									AS ot_holiday_du2},
			IF(paid_holiday_count} != 0,ot_ej_saved}, 0)									AS ot_holiday_ej},
			$balanceFrameFrom}																AS balance_frame},
			@munkaido:=IF(date}<@date OR employee_contract_id}!=@eci COLLATE utf8_unicode_ci, 0, @munkaido)
																							AS seged1,
			@adatnapw:=IF(date}<@date OR employee_contract_id}!=@eci COLLATE utf8_unicode_ci, 0, @adatnapw)
																							AS seged2,
			@tulora_nem:=IF(date}<@date OR employee_contract_id}!=@eci COLLATE utf8_unicode_ci, 0, @tulora_nem)
																							AS seged3,
			@eci:=employee_contract_id}														AS seged4,
			@date:=date}																	AS seged5,
			@adatnapw	:= @adatnapw + IF(weekend} = 1, 0, 1)								AS seged6,
			@adatora	:= @adatnapw * 8													AS seged7,
			/*
			*/
			@munkaido	:= @munkaido + munkaido_car}										AS munkaido_sum},
			@tulora_nem	:= @tulora_nem + tulora_nem_car}									AS tulora_nem_sum},
			@tulora:=IF(day_of_week}=7, GREATEST(@munkaido-
			( 40 * IF($weekWorktimeCount = 0, 40, @adatora) / 40 +
			   8 * IF($weekWorktimeCount < 2,  5, @adatnapw) /  5
			)*3600-@tulora_nem,0), 0)														AS tulora},
			@balance:=IF(day_of_week}=7, @munkaido-
			(40 * IF($weekWorktimeCount = 0, 40, @adatora) / 40)*3600-@tulora, 0)			AS egyenleg},
			IF(day_of_week}=7, @adatora, 0) * 3600											AS bazisido},
			IF(day_of_week}=7, @munkaido, 0)												AS elszido},
			@munkaido:=IF(day_of_week}=7, 0, @munkaido)										AS seged8,
			@adatnapw:=IF(day_of_week}=7, 0, @adatnapw)										AS seged9,
			@tulora_nem:=IF(day_of_week}=7, 0, @tulora_nem)									AS seged0,
				";
		if($ts && $ktgColumn)
		{
			$sql .= "
					IF(option1}='', 0, IF(paytime}+balance}>0, 2*15*option1}, 0))
																							AS ktg_terites},
					";
		}
		else
		{
			$sql .= "
					0																		AS ktg_terites},
					";
		}
		$sql .= "
			ot_workday_de}+ot_workday_du1}+ot_workday_du2}+ot_workday_ej}					AS ot_workday},
			ot_restday_de}+ot_restday_du1}+ot_restday_du2}+ot_restday_ej}					AS ot_restday},
			IF(m_kezdet2} !='', m_kezdet2}, m_veg1})										AS mveg1},
			IF(m_kezdet2} !='', LEFT(SEC_TO_TIME(TIME_TO_SEC(CONCAT(m_kezdet2}, ':00'))+break_time_duration_1}), 5), '')
																							AS mkezdet2},
			IF(m_kezdet3} !='', m_kezdet3}, IF(m_kezdet2} !='', m_veg1}, ''))				AS mveg2},
			IF(m_kezdet3} !='', LEFT(SEC_TO_TIME(TIME_TO_SEC(CONCAT(m_kezdet3}, ':00'))+break_time_duration_2}), 5), '')
																							AS mkezdet3},
			IF(m_kezdet3} !='', m_veg1}, '')												AS mveg3}
			";
			return $sql;
	}

	protected function getOtherBasePrefix()
	{
		$this->setTSParams();
		$absenceWorktime	= $this->tsParams['absenceWorktime'];
		$weekendRestday		= $this->tsParams['weekendRestday'];
		$weekWorktimeCount	= $this->tsParams['weekWorktimeCount'];
		$balanceFrameFrom	= $this->tsParams['balanceFrameFrom'];

		$sql = "
				*,
				CONCAT(day}, '/', IF(type_of_daytype_saved}='RESTDAY','P',IF(type_of_daytype_saved}='WORKDAY', 'M', 'Cs')))
																							AS daytype},
				balance_saved}-csusztat}-csusztat_admin}-csuszonap}							AS balance},
				";
		$sql .= $this->getCompanyAbsenceCorinthia();
		$sql .= $this->getTavkod();
		$sql .= $this->getNapkod();

		$sql .= "
				TIME_TO_SEC(CONCAT(pause_end}, ':00'))-TIME_TO_SEC(CONCAT(pause_start}, ':00'))
																							AS pause_time},
				work_start}																	AS mkezdet1},
				work_end}																	AS m_veg1},
				IF(break_time_duration_1}>0 AND paid_break_time_1}=0, break_time_from_1}, '')
																							AS m_kezdet2},
				''																			AS m_veg2},
				IF(break_time_duration_2}>0 AND paid_break_time_2}=0, break_time_from_2}, '')
																							AS m_kezdet3},
				''																			AS m_veg3},
/*				'NK'																		AS napkod},*/
				ot_de_saved}+wsu_overtime_de}												AS ot_de},
				ot_du_saved}+wsu_overtime_du}												AS ot_du},
				ot_du1_saved}+wsu_overtime_du1}												AS ot_du1},
				ot_du2_saved}+wsu_overtime_du2}												AS ot_du2},
				ot_ej_saved}+wsu_overtime_ej}												AS ot_ej},

				ot_de_saved} - ot_restday_de_saved}											AS ot_workday_de},
				ot_du1_saved} - ot_restday_du1_saved}										AS ot_workday_du1},
				ot_du2_saved} - ot_restday_du2_saved}										AS ot_workday_du2},
				ot_ej_saved} - ot_restday_ej_saved}											AS ot_workday_ej},

				ot_restday_de_saved}														AS ot_restday_de},
				ot_restday_du1_saved}+ot_restday_du2_saved}									AS ot_restday_du},
				ot_restday_du1_saved}														AS ot_restday_du1},
				ot_restday_du2_saved}														AS ot_restday_du2},
				ot_restday_ej_saved}														AS ot_restday_ej},

				wt_only_saved} + ot_saved} + wsu_overtime}									AS paytime},
				wt_only_saved} + ot_saved} + wsu_overtime} + balance_saved}					AS workedtime},
				IF(szab}!=0 OR betegszab}!=0 OR tappenz}!=0 OR gyes}!=0 OR gyed}!=0 OR unnepnemjar}!=0 OR ot_saved}+wsu_overtime}!=0, 0, paid_holiday})
																							AS paid_holiday_count},
				balance_saved_minusz}+balance_saved_plusz}-csusztat}-csusztat_admin}-csuszonap}
																							AS egyenleg_tr_nelkul},
				wt_saved}																	AS wt_car},
				szab}																		AS szabi_car},
				betegszab}+tappenz}															AS beteg_car},
				absence_real_saved}-szab}-betegszab}-tappenz}								AS egyeb_car},
				IF(wt_saved} != 0, 1, 0)													AS worked_day},
				IF(szab}!=0 OR betegszab}!=0 OR tappenz}!=0 OR gyes}!=0 OR gyed}!=0 OR unnepnemjar}!=0 OR ot_saved}+wsu_overtime}!=0, 0, IF(weekend}!=1,paid_holiday},0))
																							AS paid_holiday_weekdays},
				IF(workgroup_work_type}!='FLEXIBLE',
					IF(type_of_daytype_saved} = 'RESTDAY' OR
					(weekend}=1 AND (szab}=0) AND betegszab}=0 AND igazolt_nemfizetett}=0 AND tappenz}=0 AND gyes}=0 AND gyed}=0 AND unnepnemjar}=0 AND ot_saved}+wsu_overtime}=0 AND paid_holiday}!=0)
					OR (absence_saved}-(szab}-egyeb_potszabi}-fizetes_nelkuli_szabi}-rendkivuli_szabadsag}-rendkivuli_szabadsag_egyeb}-tanulmanyi_szabadsag}+betegszab}+igazolt_nemfizetett}+hivatalostav}+torvtav}+IF(weekend}!=1,tappenz}+gyes}+gyed},0)) != 0), 0,
						wt_saved}+szab}-egyeb_potszabi}-fizetes_nelkuli_szabi}-rendkivuli_szabadsag}-rendkivuli_szabadsag_egyeb}-tanulmanyi_szabadsag}+betegszab}+igazolt_nemfizetett}+hivatalostav}+torvtav}+IF(weekend}!=1,tappenz}+gyes}+gyed}, 0)+
						IF(szab}!=0 OR betegszab}!=0 OR igazolt_nemfizetett}!=0 OR tappenz}!=0 OR gyes}!=0 OR gyed}!=0 OR unnepnemjar}!=0 OR ot_saved}+wsu_overtime}!=0, 0, paid_holiday})
					),
				0)																			AS munkaido_car},
				IF(workgroup_work_type}!='FLEXIBLE', betegszab}+IF(weekend}!=1,tappenz}+gyes}+gyed},0), 0)
																							AS tulora_nem_car},
				";
			if ($absenceWorktime==1)
			{
				$sql .= "
					@absence_name:=IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0),absence_name_saved}, absence_name_saved})
																								AS absence_name},
					IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), absence_real_saved}, absence_real_saved})
																								AS absence},
					IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), 0, wt_saved})
																								AS wt},
					IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), 0, ot_saved}+wsu_overtime})
																								AS ot},
					";
			}
			elseif ($absenceWorktime==2)
			{
				$sql .= "
					@absence_name:=IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0),'' , absence_name_saved})
																								AS absence_name},
					IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), '', absence_real_saved})
																								AS absence},
					IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), wt_saved}, wt_saved})
																								AS wt},
					IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), ot_saved}+wsu_overtime}, ot_saved}+wsu_overtime})
																								AS ot},
					";
			}
			else
			{
				$sql .= "
					@absence_name:=IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), '".Dict::getModuleValue("ttwa-base","ERROR!!!")."', absence_name_saved})
																								AS absence_name},
					IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), 0, absence_real_saved})
																								AS absence},
					IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), 0, wt_saved})
																								AS wt},
					IF(absence_real_saved}>0 AND (wt_saved}>0 OR ot_saved}+wsu_overtime}>0), 0, ot_saved}+wsu_overtime})
																								AS ot},
					";
			}

			$sql.= "IF(standby_saved}-ot_saved}+IF(wt_only_saved} =0 OR holiday_name} IS NULL,0,dwws})<0,
					0, standby_saved}-ot_saved}+IF(wt_only_saved} =0 OR holiday_name} IS NULL,0,dwws}))	AS standby
			";
			return $sql;

	}

	protected function getBasePrefix($ts=false)
	{
		$this->setTSParams();
		$absenceWorktime	= $this->tsParams['absenceWorktime'];
		$weekendRestday		= $this->tsParams['weekendRestday'];
		$weekWorktimeCount	= $this->tsParams['weekWorktimeCount'];

		if ($ts)
		{
			$sql = "
					name_with_emp_id_ec_id														AS name_with_emp_id_ec_id},
					employee_contract_id														AS employee_contract_id},
					";
			$sql .= "
					IFNULL(option1,'')															AS option1},
					IFNULL(option2,'')															AS option2},
					IFNULL(option3,'')															AS option3},
					IFNULL(option4,'')															AS option4},
					IFNULL(option5,'')															AS option5},
					IFNULL(option6,'')															AS option6},
					IFNULL(option7,'')															AS option7},
					IFNULL(option8,'')															AS option8},
					IFNULL(option9,'')															AS option9},
					IFNULL(option10,'')															AS option10},
					";
		}
		else
		{
			$sql = "
					CONCAT(employee_contract_id, '')											AS employee_contract_id},
					";
		}

			$sql .= $this->getRegistrations('frame');

			$sql .= "frame_from																	AS frame_from},
					frame_to																	AS frame_to},
					";
			if (!$ts)
			{
				$sql .= "
					frame_closed																AS frame_closed},
					";
			}
		$sql .= "
					date																		AS date},
					company_name																AS company_name},
					company_org_group1_name														AS company_org_group1_name},
					company_org_group2_name														AS company_org_group2_name},
					company_org_group3_name														AS company_org_group3_name},
					unit_name																	AS unit_name},
					payroll_name																AS payroll_name},
					workgroup_name																AS workgroup_name},
                    employee_position_id                                                        AS employee_position_id},
                    employee_position_name                                                      AS employee_position_name},
					cost_name																	AS cost_name},
					cost_center_name															AS cost_center_name},
					card																		AS card},
					CONCAT(company_id, '')														AS company_id},
					CONCAT(company_org_group1_id, '')											AS company_org_group1_id},
					CONCAT(company_org_group2_id, '')											AS company_org_group2_id},
					CONCAT(company_org_group3_id, '')											AS company_org_group3_id},
					CONCAT(unit_id, '')															AS unit_id},
					CONCAT(workgroup_id, '')													AS workgroup_id},
					CONCAT(payroll_id, '')														AS payroll_id},
					CONCAT(cost_id, '')															AS cost_id},
					CONCAT(cost_center_id, '')													AS cost_center_id},
					CONCAT(emp_id,'')															AS emp_id},
					CONCAT(employee_id,'')														AS employee_id},
					weekend																		AS weekend},
					day_of_week																	AS day_of_week},
					workgroup_work_type															AS workgroup_work_type},
					workgroup_work_order														AS workgroup_work_order},
					workgroup_work_order														AS muszakszam},
					type_of_daytype																AS type_of_daytype_saved},
                    IFNULL(pause_start, '00:00')												AS pause_start},
                    IFNULL(pause_end, '00:00')													AS pause_end},
                    break_time_from_1                                                           AS break_time_from_1},
                    IFNULL(break_time_duration_1, 0)                                            AS break_time_duration_1},
					IFNULL(paid_break_time_1,0)													AS paid_break_time_1},
                    break_time_from_2                                                           AS break_time_from_2},
                    IFNULL(break_time_duration_2, 0)                                            AS break_time_duration_2},
					IFNULL(paid_break_time_2, 0)												AS paid_break_time_2},
					break_time_from_3                                                           AS break_time_from_3},
                    IFNULL(break_time_duration_3, 0)                                            AS break_time_duration_3},
					IFNULL(paid_break_time_3,0)													AS paid_break_time_3},
                    break_time_from_4                                                           AS break_time_from_4},
                    IFNULL(break_time_duration_4, 0)                                            AS break_time_duration_4},
					IFNULL(paid_break_time_4, 0)												AS paid_break_time_4},
					break_time_from_5                                                           AS break_time_from_5},
                    IFNULL(break_time_duration_5, 0)                                            AS break_time_duration_5},
					IFNULL(paid_break_time_5, 0)												AS paid_break_time_5},

					wsu_overtime																AS wsu_overtime},
					wsu_overtime_de																AS wsu_overtime_de},
					wsu_overtime_du1															AS wsu_overtime_du1},
					wsu_overtime_du2															AS wsu_overtime_du2},
					wsu_overtime_du1+wsu_overtime_du2											AS wsu_overtime_du},
					wsu_overtime_ej																AS wsu_overtime_ej},
					concat(last_name, ' ', first_name)											AS fullname},
					DAYOFMONTH(date)															AS day},
					weekday_name																AS day_name},
					IF(holiday_name != '', 'absence_type_paidholiday', concat('daytype_type_of_day_', type_of_daytype))
																								AS type_of_daytype},
					";
		$sql .= $this->getWorkStartEnd();
		$sql .= "
					SUM( IF( `inside_type_id` like 'wt%', `value`,
							 0 ) )
																								AS wt_only_saved},
					SUM( IF( `inside_type_id` like 'lostTime%', `value`,
							 0 ) )
																								AS lostTime},
					SUM( IF( `inside_type_id` = 'justifiedBloodGiving', `value`,
							 0 ) )
																								AS justifiedBloodGiving},
					SUM( IF( `inside_type_id` like 'wt%', `value`,
						IF( (".  $this->getCalcStatetypeWorktime().") AND ((`calc_state_type_status`=0) OR (`calc_state_type_status`=1)), `value`, 0 ) ) )
																								AS wt_saved},
					SUM( IF(shift_type_id='de', IF( `inside_type_id` like 'wt%', `value`,
						IF( (".  $this->getCalcStatetypeWorktime().") AND ((`calc_state_type_status`=0) OR (`calc_state_type_status`=1)), `value`, 0 ) ), 0) )
																								AS wt_de},
					SUM( IF(shift_type_id in ('du1', 'du2'), IF( `inside_type_id` like 'wt%', `value`,
						IF( (".  $this->getCalcStatetypeWorktime().") AND ((`calc_state_type_status`=0) OR (`calc_state_type_status`=1)), `value`, 0 ) ), 0) )
																								AS wt_du},
					SUM( IF(shift_type_id='du1', IF( `inside_type_id` like 'wt%', `value`,
						IF( (".  $this->getCalcStatetypeWorktime().") AND ((`calc_state_type_status`=0) OR (`calc_state_type_status`=1)), `value`, 0 ) ), 0) )
																								AS wt_du1},
					SUM( IF(shift_type_id='du2', IF( `inside_type_id` like 'wt%', `value`,
						IF( (".  $this->getCalcStatetypeWorktime().") AND ((`calc_state_type_status`=0) OR (`calc_state_type_status`=1)), `value`, 0 ) ), 0) )
																								AS wt_du2},
					SUM( IF(shift_type_id='ej', IF( `inside_type_id` like 'wt%', `value`,
						IF( (".  $this->getCalcStatetypeWorktime().") AND ((`calc_state_type_status`=0) OR (`calc_state_type_status`=1)), `value`, 0 ) ), 0) )
																								AS wt_ej},

					SUM(if(left(inside_type_id, 2) NOT IN ('ot', 'wt') AND inside_type_id NOT IN ('balance', 'balancetr', 'paidot', 'paidot_mf', 'paidot_ef', 'paidot_50', 'paidot_100'), value, 0)
						)																		AS nemmunka},
					SUM(if(left(inside_type_id, 2)='ot', value, 0)
						)																		AS ot_saved},
					SUM(if(left(inside_type_id, 2)='ot' AND shift_type_id in ('de'), value, 0)
						)																		AS ot_de_saved},
					SUM(if(left(inside_type_id, 2)='ot' AND shift_type_id in ('du1', 'du2'), value, 0)
						)																		AS ot_du_saved},
					SUM(if(left(inside_type_id, 2)='ot' AND shift_type_id in ('du1'), value, 0)
						)																		AS ot_du1_saved},
					SUM(if(left(inside_type_id, 2)='ot' AND shift_type_id in ('du2'), value, 0)
						)																		AS ot_du2_saved},
					SUM(if(left(inside_type_id, 2)='ot' AND shift_type_id in ('ej'), value, 0)
						)																		AS ot_ej_saved},
					SUM(if(left(inside_type_id, 3)='otw', value, 0)
						)																		AS ot_restday_saved},
					SUM(if(left(inside_type_id, 3)='otw' AND shift_type_id in ('de'), value, 0)
						)																		AS ot_restday_de_saved},
					SUM(if(left(inside_type_id, 3)='otw' AND shift_type_id in ('du1', 'du2'), value, 0)
						)																		AS ot_restday_du_saved},
					SUM(if(left(inside_type_id, 3)='otw' AND shift_type_id in ('du1'), value, 0)
						)																		AS ot_restday_du1_saved},
					SUM(if(left(inside_type_id, 3)='otw' AND shift_type_id in ('du2'), value, 0)
						)																		AS ot_restday_du2_saved},
					SUM(if(left(inside_type_id, 3)='otw' AND shift_type_id in ('ej'), value, 0)
						)																		AS ot_restday_ej_saved},
					SUM(if(left(inside_type_id, 3)='otw', value, 0)
						)	+ IF(type_of_daytype = 'RESTDAY', wsu_overtime, 0)					AS ot_weekend},

					SUM(if(shift_type_id in ('du2','ej'), value, 0)
						)	+ wsu_overtime_du2 + wsu_overtime_ej								AS shift_bonus},
					SUM(if(inside_type_id='balance', value, 0))									AS balance_saved},
					IFNULL(standby, 0)															AS standby_saved},
					IFNULL(daily_worktime_work_scheduled, 0)									AS dwws},
					IFNULL(daily_worktime_workgroup, 0)											AS dwwg},
					IFNULL(absence_time, 0)														AS absence_saved},
					IFNULL(absence_real_time, 0)												AS absence_real_saved},
					holiday_name																AS holiday_name},
					absence_name																AS absence_name_original},
					absence_type_id																AS absence_type},
					IF(holiday_name != '', holiday_name, absence_name)							AS absence_name_saved},
					IFNULL(daily_worktime_5_2, 0)												AS daily_worktime_5_2},
					IF(holiday_name != '', IFNULL(holiday_time, 0), 0)							AS paid_holiday},

					IF(absence_type_id = '5b70cb93b84b05e1803b7825c55c8be1', `absence_real_time`, 0)	AS gyermek_utan_jaro_tappenz},
					IF(absence_type_id = '1ecfd16854c92211d6278a918038903d', `absence_real_time`, 0)	AS allasido},
					IF(absence_type_id = 'ebf4ac30e7dc238fd2f4bc86332e0675', `absence_real_time`, 0)	AS apaszabi},
					IF(absence_type_id = '8cb3945a0a8b1afa9d064d064584e5d4', `absence_real_time`, 0)	AS athozott_szabi},
					IF(absence_type_id = '879e1d423b540a25f8c152948bb5066a', `absence_real_time`, 0)	AS baleseti_tappenz},
					IF(absence_type_id = '1970bfb1118bb3c58a8fc785af3a2d4d', `absence_real_time`, 0)	AS belfoldi_kikuldetes_partnerhez},
					IF(absence_type_id = '51fde0fb62e4b83f25a5255b345c0ffc', `absence_real_time`, 0)	AS bent_dolgozik},
					IF(absence_type_id = '3ad610cfce362896dbb4b11858dfae40', `absence_real_time`, 0)	AS betegszabadsag},
					IF(absence_type_id = '15233901ca7c40994aa5cd05459007dd', `absence_real_time`, 0)	AS business_trip},
					IF(absence_type_id = '8b50795de6e24249a7986f05a579476c', `absence_real_time`, 0)	AS cegen_beluli_kikuldetes},
					IF(absence_type_id = '29337d9204baca9588942e162d229087', `absence_real_time`, 0)	AS csusztat},
					IF(absence_type_id = '0b9134075313a0c6b27f7df2e394a14b', `absence_real_time`, 0)	AS csusztat_admin},
					IF(absence_type_id = 'fa2a8d7fc73f1558640e69587daf687c', `absence_real_time`, 0)	AS egeszseg_karosodott_potszabi},
					IF(absence_type_id = '082ef210ec31b1a7741c869043916347', `absence_real_time`, 0)	AS egyeb_bentlet},
					IF(absence_type_id = '5a576d510ee9f200653c966c3e33e8ee', `absence_real_time`, 0)	AS egyeb_kulfoldi_kikuldetes},
					IF(absence_type_id = 'bfa221167d9944ce0ffcc8dffbbaf589', `absence_real_time`, 0)	AS egyeb_potszabi},
					IF(absence_type_id = '8e00cdd76ae240d0b1466d01dbe5a19e', `absence_real_time`, 0)	AS eletkor_szerinti_potszabi},
					IF(absence_type_id = 'b859c8b809b6b30ae5d7208dff841dec', `absence_real_time`, 0)	AS elozo_evi_potszabi},
					IF(absence_type_id = '90b9dd9dcbdc86ec8acaad81fd6add2b', `absence_real_time`, 0)	AS felmentes_mv_alol},
					IF(absence_type_id = 'ad0e81399d06033ad7be1aae70100bcc', `absence_real_time`, 0)	AS felmentesi_ido},
					IF(absence_type_id = '59c3e3db07f06c43cf5bbf440be6b553', `absence_real_time`, 0)	AS fiatal_mv_potszabi},
					IF(absence_type_id = '628834878c5bc72f60283e37865678b6', `absence_real_time`, 0)	AS fizetes_nelkuli_szabi},
					IF(absence_type_id = 'f4f63ae4dd65cd97ee1f409d8b620806', `absence_real_time`, 0)	AS fizetett_igazolt},
					IF(absence_type_id = 'eb49097c829f3f5140e41e92eae91202', `absence_real_time`, 0)	AS fizetett_unnep},
					IF(absence_type_id = 'a8fd428dd18c27257aceb1629c75d637', `absence_real_time`, 0)	AS gyermek_apolasi_tappenz},
					IF(absence_type_id = 'c9c203ce94830523b07616850d43172d', `absence_real_time`, 0)	AS gyermek_utan_jaro_potszabi},
					IF(absence_type_id IN ('ddc0e01ba27be8926cc66424a88865c5', '07b2b655fe5b399002843a14b17904c5'), `absence_real_time`, 0)
																										AS gyermek_gondozasi_dij},
					IF(absence_type_id IN ('ddc0e01ba27be8926cc66424a88865c5'), `absence_real_time`, 0)
																										AS gyermek_gondozasi_dij_elso_ev},
					IF(absence_type_id IN ('07b2b655fe5b399002843a14b17904c5'), `absence_real_time`, 0)
																										AS gyermek_gondozasi_dij_masodik_evtol},
					IF(absence_type_id IN ('db8d6192d34f21a4b779e0afb1120c42', 'db8d6192d34f21a4b779e0afb1120c42'), `absence_real_time`, 0)
																										AS gyermek_gondozasi_segely},
					IF(absence_type_id IN ('380b630d61430cf7e48c73440d2545a0', '380b630d61430cf7e48c73440d2545a0'), `absence_real_time`, 0)
																										AS gyermek_gondozasi_segely_dolg},
					IF(absence_type_id = 'GONEHOME', `absence_real_time`, 0)							AS hazament},
					IF(absence_type_id = '5bc11a1ad8876542d892ba9851b92884', `absence_real_time`, 0)	AS hivatalos_tavollet},
					IF(absence_type_id IN ('def32968390fb987c823da0cbf7d3bd8', '987b2e6b362cae3e1f638dd85dbd5abe'), `absence_real_time`, 0)	AS igazolatlan_tavollet},
					IF(absence_type_id IN ('269b59b4efbbb4ef1d527492dc06cb60', '7a5509adc83c2fff97b3979928206c81'), `absence_real_time`, 0)	AS igazolt_nemfizetett},
					IF(absence_type_id = 'b897aa087e13ac27ad4a04d9d4718229', `absence_real_time`, 0)	AS igazolt_jogfolytonossag},
					IF(absence_type_id = '64af4774b72418f8e11d2eb770a2a7e4', `absence_real_time`, 0)	AS igazolt_tavollet},
					IF(absence_type_id = 'd8fe7db7b99500f7abd159cdfb4a47e6', `absence_real_time`, 0)	AS iskolanapon},
					IF(absence_type_id = 'aad4833e34da5d4a6eb00a13088aeab5', `absence_real_time`, 0)	AS kikuldetesen},
					IF(absence_type_id = 'bac18782b00e0921f91817826d517b70', `absence_real_time`, 0)	AS korhazi_tappenz},
					IF(absence_type_id = '5fe1f0f34f1d6f743a69fa02832f1956', `absence_real_time`, 0)	AS kulfoldi_kikuldetes_partnerhez},
					IF(absence_type_id = 'e87aaaf6b0cb4274baa3f68e00fa237c', `absence_real_time`, 0)	AS kulfoldi_telephely},
					IF(absence_type_id = '7829307df38f9d1795a9bfe7e2fb7e25', `absence_real_time`, 0)	AS kulso_munkan},
					IF(absence_type_id = '551fe3abbada9c264c9d90dad4a93921', `absence_real_time`, 0)	AS kutatonapon},
					IF(absence_type_id = '8e18ea259a7d4e9b6e6b5041bbb1bd08', `absence_real_time`, 0)	AS maganugyben_tavozott},
					IF(absence_type_id = 'fb73306fa12c1a6ab07c28d1f13fccf2', `absence_real_time`, 0)	AS masik_telephely},
					IF(absence_type_id = '945e3618f487efc4325204521e49c5f3', `absence_real_time`, 0)	AS munkahelyi_baleset},
					IF(absence_type_id = '8d68bfa2e5db8a60df706799d9fe4ad5', `absence_real_time`, 0)	AS mendeni_kikuldetes},
					IF(absence_type_id = '41ee669974a1d7c184b5b0c9d10ccbe4', `absence_real_time`, 0)	AS munkanelkuli_ellatas},
					IF(absence_type_id = 'b0aaaf1a1c06491c945a5a34810fc5f9', `absence_real_time`, 0)	AS nem_mendeni_kikuldetes},
					IF(absence_type_id = '17c82fbc41e2abf10dff3a857222ec14', `absence_real_time`, 0)	AS otthoni_munka},
					IF(absence_type_id = '12dd1237b950bfa8ece94c120b5d4f8f', `absence_real_time`, 0)	AS passziv_baleseti_tappenz},
					IF(absence_type_id = '8f6615bc77b4099011f7e9e5cbce6023', `absence_real_time`, 0)	AS passziv_gyermekapolasi_tappenz},
					IF(absence_type_id = '8a6624ca64db4cca0397fdae787180fa', `absence_real_time`, 0)	AS passziv_gyermekgondozasi_dij},
					IF(absence_type_id = '7477f10be8bf995d7a48c104f1d8fe1c', `absence_real_time`, 0)	AS passziv_korhazi_tappenz},
					IF(absence_type_id = 'a96243e2f8a43604d30e9da33e411cd1', `absence_real_time`, 0)	AS passziv_terhessegi_gyermekagyi_segely},
					IF(absence_type_id = 'c6818d7c127a0011e92f558b56ccc8eb', `absence_real_time`, 0)	AS pihenonap},
					IF(absence_type_id = '8c3708a21ae32e17af7da36babb301be', `absence_real_time`, 0)	AS rendkivuli_halaleseti_potszabi},
					IF(absence_type_id = 'fceedab36bb56f3a59665972fa0a7d54', `absence_real_time`, 0)	AS rendkivuli_szabadsag},
					IF(absence_type_id = '4db72bbbea57daf5aaee1af7e388af55', `absence_real_time`, 0)	AS rendkivuli_szabadsag_egyeb},
					IF(absence_type_id = 'e4b569e812f9f398d0a1255ffc8f9af7', `absence_real_time`, 0)	AS sorkatonai_szolgalat},
					IF(absence_type_id = 'a272f564576d443e7832587126b070aa', `absence_real_time`, 0)	AS szabadsag},
					IF(absence_type_id = '5382dfe7cad1651991076b4e0af903ba', `absence_real_time`, 0)	AS tanulmanyi_szabadsag},
					IF(absence_type_id = '7b3cf78f9fd2a404567fe572fcb0eaf9', `absence_real_time`, 0)	AS tappenz_rendes_1},
					IF(absence_type_id = '11d5ce859c686c0dc3c9727d19cfbf5f', `absence_real_time`, 0)	AS tappenz_rendes_2},
					IF(absence_type_id = 'ab518692163bdcc355dace7d919a47b5', `absence_real_time`, 0)	AS tappenzre_nem_jogosito_kereso_keptelen},
					IF(absence_type_id = '0fd0fced4df907e2a7deec5eb9736891', `absence_real_time`, 0)	AS tavmunka},
					IF(absence_type_id = 'cffaabff749860f85840377da9f315cf', `absence_real_time`, 0)	AS temetesi_szabadsag},
					IF(absence_type_id = '2b37402e46f96f408cd2a99ae13868b5', `absence_real_time`, 0)	AS tgyas},
					IF(absence_type_id = '99e73fa84e8c735ef7f464ce1153e321', `absence_real_time`, 0)	AS torvenyes_tavolleten},
					IF(absence_type_id = '8e6c3df6f348188521abcd8a4494db6a', `absence_real_time`, 0)	AS trening},
					IF(absence_type_id = 'aef6ebc4edde58fe4bf17402e6f16925', `absence_real_time`, 0)	AS trening_fizetett},
					IF(absence_type_id = 'e6f26f8c2419a88f306d727b79dec80c', `absence_real_time`, 0)	AS trening_nem_fizetett},
					IF(absence_type_id = 'overtime________________20161018', `absence_real_time`, 0)	AS tulora_mint_tavollet},
					IF(absence_type_id = 'f2ce95d852f831a7c45a4ff79f955ffd', `absence_real_time`, 0)	AS veszelyes_munkahely_miatti_potszabadsag},
					IF(absence_type_id = '524f08d3ba0b934bbe656f82389531a2', `absence_real_time`, 0)	AS modoff},
					IF(absence_type_id = 'b52e481d3a8710010cad6e5e3108f0a4', `absence_real_time`, 0)	AS recupday},

					IF(inside_type_id = '29337d9204baca9588942e162d229087', `value`, 0)			AS csusztat_bent},
					IF(inside_type_id = '0b9134075313a0c6b27f7df2e394a14b', `value`, 0)			AS csusztat_admin_bent},
					IF(inside_type_id = '83b2789f74438d800a261f5ccb21b2fb', `value`, 0)			AS ebedel},
					IF(inside_type_id = '1ecfd16854c92211d6278a918038903d', `value`, 0)			AS allasido_bent},
					IF(inside_type_id = 'GONEHOME', `value`, 0)									AS hazament_bent},
					IF(inside_type_id = '7829307df38f9d1795a9bfe7e2fb7e25', `value`, 0)			AS kulso_munkan_bent},
					IF(inside_type_id = '8e18ea259a7d4e9b6e6b5041bbb1bd08', `value`, 0)			AS maganugyben_tavozott_bent},
					IF(inside_type_id = 'fb73306fa12c1a6ab07c28d1f13fccf2', `value`, 0)			AS masik_telephely_bent},
					IF(inside_type_id = '17c82fbc41e2abf10dff3a857222ec14', `value`, 0)			AS otthoni_munka_bent},
					IF(inside_type_id = '0fd0fced4df907e2a7deec5eb9736891', `value`, 0)			AS tavmunka_bent},
					IF(inside_type_id = '99e73fa84e8c735ef7f464ce1153e321', `value`, 0)			AS torvenyes_tavolleten_bent},
					IF(inside_type_id = '8e6c3df6f348188521abcd8a4494db6a', `value`, 0)			AS trening_bent},

					IF(absence_type_id = 'a272f564576d443e7832587126b070aa', `absence_real_time`, 0)	AS absence_onleave},
					IF(absence_type_id = '3ad610cfce362896dbb4b11858dfae40', `absence_real_time`, 0)	AS absence_disease},
					IF(absence_type_id = '29337d9204baca9588942e162d229087', `absence_real_time`, 0)	AS absence_slide},
					IF(absence_type_id IN ('ddc0e01ba27be8926cc66424a88865c5', '07b2b655fe5b399002843a14b17904c5'), `absence_real_time`, 0)
																								AS gyed},
					IF(absence_type_id IN ('db8d6192d34f21a4b779e0afb1120c42', '380b630d61430cf7e48c73440d2545a0'), `absence_real_time`, 0)
																								AS gyes},
					";
			if ($ts)
			{
				$sql .= "
					eloido																		AS eloido},
					";
			}
			$dailyWorktimePlace = App::getSetting('dailyWorktimePlace');
			$employeeDailyWorktime = "`daily_worktime_contract`";
			switch ($dailyWorktimePlace) {
				case 'CONTRACT':
					$employeeDailyWorktime = "`daily_worktime_contract`";
					break;
				case 'SCHEDULE':
					$employeeDailyWorktime = "`daily_worktime_work_scheduled`";
					break;
				case 'WORKGROUP':
					$employeeDailyWorktime = "`daily_worktime_workgroup`";
					break;
				
				default:
					$employeeDailyWorktime = "`daily_worktime_contract`";
					break;
			}
			$sql .= "
					IF(type_of_daytype IN ('COMPENSATORYDAY'),
						{$employeeDailyWorktime}, 0)																AS csuszonap},
					IF(1,
						{$employeeDailyWorktime}, 0)																AS daily_worktime},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE'),
							IF(inside_type_id='balance',
								IF(value>=30*60, value, 0),
							0),
						0)
						)																		AS balance_saved_plusz},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE'),
							IF(inside_type_id='balance',
								IF(value<0, value, 0),
							0),
						0)
						)																		AS balance_saved_minusz},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE'), IF(inside_type_id='balancetr',IFNULL(value,0),0), 0))
																								AS balancetr},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE'), IF(inside_type_id='paidot',IFNULL(value,0),0), 0))
																								AS balanceot},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE', 'FRAMEWORK', 'FRAMEWORK_BALANCE'), IF(inside_type_id='paidot',IFNULL(value,0),0), 0))
																								AS paidot},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE', 'FRAMEWORK', 'FRAMEWORK_BALANCE'), IF(inside_type_id='next_balancetr',IFNULL(value,0),0), 0))
																								AS nextbalance},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE', 'FRAMEWORK', 'FRAMEWORK_BALANCE'), IF(inside_type_id='paidot_mf',IFNULL(value,0),0), 0))
																								AS paidot_mf},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE', 'FRAMEWORK', 'FRAMEWORK_BALANCE'), IF(inside_type_id='paidot_ef',IFNULL(value,0),0), 0))
																								AS paidot_ef},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE', 'FRAMEWORK', 'FRAMEWORK_BALANCE'), IF(inside_type_id='paidot_50',IFNULL(value,0),0), 0))
																								AS paidot_50},
					SUM(IF(workgroup_work_type IN ('FLEXIBLE', 'ONLYWORKSCHEDULE', 'FRAMEWORK', 'FRAMEWORK_BALANCE'), IF(inside_type_id='paidot_100',IFNULL(value,0),0), 0))
																								AS paidot_100},
					";
			$sql .= $this->getOfficalAbsence();
			$sql .= $this->getCompanyAbsence(true);
			$sql .= "
					IF(absence_worktime = 1, `absence_time`, 0)									AS absence_paid},
				";
			$compensatoryday_sum_to_restday	= App::getSetting("compensatoryday_sum_to_restday");
			if ($weekendRestday==1)
			{
				$sql .= "
					IF(weekend = 1, 1, 0)														AS legal_restday},
					IF(type_of_daytype IN ('RESTDAY','COMPENSATORYDAY'), 1, 0)										as used_restday},
				";
				if($compensatoryday_sum_to_restday === '1'){
					$sql .= "
					IF(type_of_daytype IN ('RESTDAY','COMPENSATORYDAY'), 1, 0)										as used_restday},
					";
				}else{
					$sql .= "
					IF(type_of_daytype IN ('RESTDAY'), 1, 0)										as used_restday},
					";
				}
			}
			else
			{
				$sql .= "
					IF(`daily_real_worktime_5_2` = 0, 1, 0)										as legal_restday},
				";
				if($compensatoryday_sum_to_restday === '1'){
					$sql .= "
					IF(type_of_daytype IN ('RESTDAY','COMPENSATORYDAY') OR IFNULL(holiday_time, 0)!=0, 1, 0)			as used_restday},
					";
				}else{
					$sql .= "
					IF(type_of_daytype IN ('RESTDAY') OR IFNULL(holiday_time, 0)!=0, 1, 0)			as used_restday},
					";
				}
			}
			$sql .= "
					IF(type_of_daytype IN ('WORKDAY') AND (IFNULL(absence_real_time, 0)=0 AND holiday_name IS NULL), 1, 0)
																								AS workday},
					workgroup_work_type															AS work_type}
				";
			return $sql;
	}

	protected function getRegistrations($where)
	{
		return "SUBSTRING(receipt_dt, 12, 5) AS receipt}, SUBSTRING(getaway_dt, 12, 5) AS getaway}, receipt_dt, getaway_dt, ";
	}

	protected function getCalcStatetypeWorktime()
	{
		return "calc_worktime=1";
	}

	protected function getOfficalAbsence()
	{
		return "0 AS absence_offical},";
	}

	protected function getWorkStartEnd()
	{
		return "	IF( (holiday_name != '') OR (type_of_daytype = 'RESTDAY' AND IFNULL(daily_worktime_work_scheduled, 0)=0) OR (type_of_daytype = 'COMPENSATORYDAY'), '', work_start )
																								AS work_start},
					IF( (holiday_name != '') OR (type_of_daytype = 'RESTDAY' AND IFNULL(daily_worktime_work_scheduled, 0)=0) OR (type_of_daytype = 'COMPENSATORYDAY'), '', work_end )
																								AS work_end},
				";
	}

	protected function getCompanyAbsenceCorinthia()
	{
		return "0 AS off}, 0 as h}, 0 as tp}, 0 as fn}, 0 as ig}, 0 as ni},";
	}

	protected function getTavkod() //xylem
	{
		return "
			'' AS tavkod},
			";
	}

	protected function getNapkod() //novitax: patikaplus
	{
		return "
			'' AS napkod},
			";
	}

	protected function getPihenonap()
	{
		return "(pihenonap}>0) OR (type_of_daytype_saved} = 'RESTDAY')";
	}

	protected function getMunkaszunetinap()
	{
		return "(fizetett_unnep}>0) OR (paid_holiday}>0)";
	}

	protected function getCompanyAbsence($frame)
	{
		return "0 AS szab}, 0 as betegszab}, 0 as tappenz}, 0 as egyebtav}, 0 as unnepnemjar}, 0 as hivatalostav}, 0 as torvtav}, 0 as magantav},";
	}

}
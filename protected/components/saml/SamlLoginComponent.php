<?php #yii2: done

'yii2-only`;

	namespace app\components\saml;
	use <PERSON>;

`/yii2-only';


$path = Yang::getAlias('application.extensions.saml');
include_once($path . "/_toolkit_loader.php");


class SamlLoginComponent
{
	public static function generateMetaData ($settingsInfo){
		$samlBaseUrl = Yang::getParam('samlBaseUrl');
		if( stristr($samlBaseUrl, 'stage') ){
			return 'prod-stage-ease';
		}else{
			return 'prod-ease';
		}
	}
}

?>
<?php #yii2: done

'yii2-only`;

	namespace app\components\ahpCore\TimeSheetBase\controllers;
	use app\components\API\GetActiveEmployeeData;
	use app\components\App;
	use app\components\Dict;
	use app\components\FS;
	use app\models\Employee;
	use app\models\Status;
	use Yang;

`/yii2-only';


class TimesheetBaseController extends Grid2Controller
{
	protected $registrationTable="timesheet_registration";
	protected $activeTables=array();
	protected $columns=array();
	protected $tableNames=array();

	public function __construct($controllerId="wfm/timesheetBase")
	{
		parent::__construct($controllerId);
	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_timesheet");

		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("modify",			false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		parent::enableReportMode();
		parent::setExportFileName(Dict::getValue("export_file_timesheet"));
		parent::G2BInit();
	}

	protected function search()
	{
		$submit = ['submit'		=> ['col_type' => 'searchBarReloadReportGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => ''], ];

		return Yang::arrayMerge($this->getPreDefinedSearchFromDb("workForce"), $submit);
	}

	public function actionReportGridContent()
	{
		ini_set('max_execution_time', 600);
		ini_set('memory_limit', '1024M');

		$this->G2BInit();

		$this->layout = "//layouts/ajax";

		$forceLoad = requestParam('forceLoad');

		if (empty($forceLoad)) {
			return false;
		}

		$edFilter=array('controllerId' => $this->getControllerID(),'search' => requestParam('searchInput'));
		$approverParams=array('processId' => "workForce");
		$GetActiveEmployeeData =new GetActiveEmployeeData($edFilter,$approverParams,$this->activeTables, "cal.`date`, employee.`employee_id`, employee_contract.`employee_contract_id`", $this->getControllerID());

		$table=$GetActiveEmployeeData->createTempTable();

		// az összes fizetetlen szünet és ezek összege
		$breakTimeFields = '';
		$unpaidBreaks = '';
		for($i = 1; $i <= 5; $i++){
			$breakTimeFields .= "@break_time_duration_{$i} := IFNULL(wsu.`break_time_duration_{$i}`, dt.`break_time_duration_{$i}`),
								@paid_break_time_{$i} := IFNULL(wsu.`paid_break_time_{$i}`, dt.`paid_break_time_{$i}`), ";
			$unpaidBreaks .= "IF(@paid_break_time_{$i} = 0, @break_time_duration_{$i}, 0)";
			if($i <5){
				$unpaidBreaks .= " + ";
			}
		}

		$SQL="
			SELECT
				{$breakTimeFields}
				@total_unpaid_break_time := {$unpaidBreaks}, ";
				foreach($this->columns as $alias => $column)
				{
					$SQL.="$column	AS $alias,
					";
				}
				$SQL.="all_data.`date`,
				all_data.`employee_contract_id`,
				".Employee::getParam('fullname',"all_data","all_data")." AS fullname
			FROM $table all_data
			";
			if(in_array('workgroup', $this->activeTables))
			{
				$alias=isset($this->tableNames['workgroup'])?$this->tableNames['workgroup']:"workgroup";
				$SQL.="LEFT JOIN `workgroup` $alias ON
						$alias.workgroup_id=all_data.workgroup_id
					AND $alias.`status` =  ".Status::PUBLISHED."
					AND all_data.`date` BETWEEN $alias.`valid_from` AND IFNULL($alias.`valid_to`,'".App::getSetting("defaultEnd")."')
				";
			}
			if(in_array('employee_calc', $this->activeTables))
			{
				$alias=isset($this->tableNames['employee_calc'])?$this->tableNames['employee_calc']:"employee_calc";
				$SQL.="LEFT JOIN `employee_calc` $alias ON
						$alias.`employee_contract_id` = all_data.`employee_contract_id`
					AND $alias.`day` = all_data.`date`
					AND $alias.`status` = ".Status::LOCKED."
				";
			}
			if(in_array('employee_calc_used_daytype', $this->activeTables))
			{
				$alias=isset($this->tableNames['employee_calc_used_daytype'])?$this->tableNames['employee_calc_used_daytype']:"employee_calc_used_daytype";
				$SQL.="LEFT JOIN `employee_calc_used_daytype` $alias ON
						$alias.`employee_contract_id` = all_data.`employee_contract_id`
					AND $alias.`day` = all_data.`date`
					AND ($alias.`status` = ".Status::LOCKED." OR ($alias.`day` > NOW() AND $alias.`status` = ".Status::PUBLISHED."))
				";
			}
			if(in_array('employee_absence', $this->activeTables))
			{
				$alias=isset($this->tableNames['employee_absence'])?$this->tableNames['employee_absence']:"employee_absence";
				$SQL.="LEFT JOIN `employee_absence` $alias ON
						$alias.`employee_contract_id` = all_data.`employee_contract_id`
					AND $alias.`day` = all_data.`date`
					AND $alias.`status` = ".Status::PUBLISHED."
				";
			}
			if(in_array('calendar', $this->activeTables))
			{
				$alias=isset($this->tableNames['calendar'])?$this->tableNames['calendar']:"calendar";
				$SQL.="LEFT JOIN `calendar` $alias ON
						$alias.`date`=all_data.`date`
				";
			}
			if(in_array('work_schedule_used', $this->activeTables))
			{
				$alias=isset($this->tableNames['work_schedule_used'])?$this->tableNames['work_schedule_used']:"work_schedule_used";
				$SQL.="LEFT JOIN `work_schedule_used` $alias ON
						$alias.`employee_contract_id` = all_data.`employee_contract_id`
					AND $alias.`day` = all_data.`date`
					AND $alias.`status` =  ".Status::PUBLISHED."
				";
			}
			if(in_array('daytype', $this->activeTables))
			{
				$alias=isset($this->tableNames['daytype'])?$this->tableNames['daytype']:"daytype";
				$SQL.="LEFT JOIN `daytype` $alias ON
						$alias.`daytype_id` = ecud.`daytype_id`
					AND $alias.`status` = ".Status::PUBLISHED."
					AND	all_data.`date` BETWEEN $alias.`valid_from` AND IFNULL($alias.`valid_to`,'".App::getSetting("defaultEnd")."')
				";
			}
			if(in_array('registration', $this->activeTables))
			{
				$regTable=$this->getRegistrationTable($table);

				$alias=isset($this->tableNames['registration'])?$this->tableNames['registration']:"registration";
				$SQL.="LEFT JOIN $regTable $alias ON
						$alias.`employee_contract_id` = all_data.`employee_contract_id`
					AND $alias.`date` = all_data.`date`
				";
			}
			$SQL.="GROUP BY `employee_contract_id`, `date`
			ORDER BY fullname, `date`
		";

		$results = dbFetchAll($SQL);

		$this->render($this->getRenderView(), array("employees" => $results, "filterDate" => $edFilter["search"]["valid_month"]));
	}

	protected function getRegistrationTable($employeeTable)
	{
		$SQL="
			CREATE TEMPORARY TABLE IF NOT EXISTS `".$this->registrationTable."`
			(
				KEY IDX_employee_contract_id_date (`employee_contract_id`,`date`)
			)
			ENGINE=MyISAM
			SELECT
				all_data.`date`,
				all_data.`employee_contract_id`,
				MIN(reg.`time`) AS first_reg,
				MAX(reg.`time`) AS last_reg
			FROM $employeeTable all_data
			LEFT JOIN `employee_card` ecard ON
					ecard.`employee_contract_id` = all_data.`employee_contract_id`
				AND ecard.`status` = ".Status::PUBLISHED."
				AND all_data.`date` BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `employee_calc_used_daytype` ecud ON
					ecud.`employee_contract_id` = all_data.`employee_contract_id`
				AND ecud.`day` = all_data.`date`
				AND ecud.`status` = ".Status::LOCKED."
			LEFT JOIN `registration` reg ON
					reg.`card`=ecard.`card`
				AND reg.`status` = ".Status::PUBLISHED."
				AND reg.`time` BETWEEN ecud.`reg_filter_from` AND ecud.`reg_filter_to`
			WHERE
					reg.`row_id` IS NOT NULL
			GROUP BY `employee_contract_id`, `date`
		";

		dbExecute($SQL);

		return $this->registrationTable;
	}

	protected function setParams($params)
	{
		$this->columns=$params['columns'];
		$this->tableNames=$params['alias'];
		$this->activeTables=$params['tables'];

		if(!in_array('employee', $this->activeTables))
		{
			$this->activeTables[]='employee';
		}
		if(!in_array('employee_contract', $this->activeTables))
		{
			$this->activeTables[]='employee_contract';
		}
	}

	protected function getRenderView()
	{
		return "application.components.wfm.TimeSheetBase.views.reportGridContent";
	}

	protected function setHtmltoPdf($html)
	{
		$content = '
			<html>
			<head>
				<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
			</head>
			<body>
				'.$html.'
			</body>
			</html>
		';

		$fs = new FS;
		$fs->disableMySQLStore();
		$fs->setFileGroupID("NodePDF_".$this->getControllerID());
		$fs->deleteFilesByUserAndFileGroupID();
		$fs->uploadTextFileFromContent($this->getExportFileName().".html", $content, "text/html");
		$fileID = $fs->getFileID();

		$this->setExportFSFileID($fileID);
	}

	protected function secondToHourMinuteFormat($second)
	{
		$hour=(int)($second/3600);
		$min=($second-$hour*3600)/60;

		$hour=$hour<10?"0".$hour:$hour;
		$min=$min<10?"0".$min:$min;

		return $hour.":".$min;
	}

	protected function secondToHours($second)
	{
		$hour=(int)($second/3600);
		$min=($second-$hour*3600)/60;

		$hour=$hour<10?"0".$hour:$hour;
		$min=$min<10?"0".$min:$min;

		return round($second/3600,2);
	}
}

?>
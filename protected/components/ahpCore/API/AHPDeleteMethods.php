<?php #yii2: done

'yii2-only`;

	namespace app\components\ahpCore\API;
	use app\components\App;
	use app\components\Dict;
	use app\components\MyActiveForm;
	use app\components\ahpCore\API\AHPSaveMethods;
	use app\components\wfm\API\AttendanceSheetStatusAction;
	use app\models\EmployeeAbsence;
	use app\models\StateTypeRight;
	use Yang;

`/yii2-only';


class AHPDeleteMethods
{
	public static function deleteAbsences($absenceBegin, $absenceEnd, $employee_contract_id, $note, $rights, &$deleted_ids, &$msg = '', &$status = 'success') {
		$allDaysEditable		= $rights["allDaysEditable"];
		$allEmployeesEditable	= $rights["allEmployeesEditable"];
		$approvalRight			= $rights["approvalRight"];
		$approvalHimselfRight	= $rights["approvalHimselfRight"];
		$error 					= FALSE;
		
		$absenceBeginCheck = false;
		if (!empty($absenceBegin)) {
			$absenceBeginArr = explode("-",$absenceBegin);
			$year = isset($absenceBeginArr[0])?$absenceBeginArr[0]:0;
			$month = isset($absenceBeginArr[1])?$absenceBeginArr[1]:0;
			$day = isset($absenceBeginArr[2])?$absenceBeginArr[2]:0;
			$absenceBeginCheck = checkdate($month, $day, $year);
		}

		$absenceEndCheck = false;
		if (!empty($absenceEnd)) {
			$absenceEndArr = explode("-",$absenceEnd);
			$year = isset($absenceEndArr[0])?$absenceEndArr[0]:0;
			$month = isset($absenceEndArr[1])?$absenceEndArr[1]:0;
			$day = isset($absenceEndArr[2])?$absenceEndArr[2]:0;
			$absenceEndCheck = checkdate($month, $day, $year);
		}

		$clickable = Yang::session('ahpplannerdata_clickableyear');
		$deleted_ids = array();

		$log = "params: '$absenceBegin', '$absenceEnd', '$employee_contract_id'";
		Yang::log($log, 'log', 'system.AHPDeleteMethods.deleteAbsences');

		if (!Yang::isGuest() && !empty($employee_contract_id) && $absenceBeginCheck && $absenceEndCheck) {
			$earlier = new DateTime($absenceBegin);
			$later = new DateTime($absenceEnd);
			$datediff = $later->diff($earlier)->format("%a") + 1;

			$daysToDelete = [];

			for ($i = 0; $i < $datediff; $i++) {
				$aktDate = date("Y-m-d", strtotime($absenceBegin . ' + ' . $i . 'day'));
				if (!empty($clickable) 
					&& isset($clickable[$employee_contract_id])
					&& isset($clickable[$employee_contract_id][$aktDate])
					&& $clickable[$employee_contract_id][$aktDate] === false
					)
				{
					continue;
				}
				$daysToDelete[] = $aktDate;
			}

			$log = '$daysToDelete'.": ".implode(",", $daysToDelete);
			Yang::log($log, 'log', 'system.AHPDeleteMethods.deleteAbsences');

			if (!count($daysToDelete)) {
				return false;
			}
			//Zárolt nap esetén nem engedi törölni a távollétet
			if (App::getSetting('summarySheetLockAbsences') == 1) 
			{
				$result = AHPDeleteMethods::getEmployeeCalcUsedDaytype($employee_contract_id, $daysToDelete);
				if ($result) {
					$msg = Dict::getModuleValue("ttwa-ahp", "there_is_locked_day_between_selected_days") . " - " . implode(", ", $daysToDelete);
					return false;
				}
			}
			$statuses = [
				Status::STATUS_DRAFT,
				Status::PUBLISHED,
				Status::STOCK_REJECTED
			];

			$ea = new EmployeeAbsence;
			$criteria = new CDbCriteria();
			$criteria->condition = "`employee_contract_id` = '$employee_contract_id' AND `day` IN ('". implode("', '", $daysToDelete)."') AND `status` IN ('". implode("', '", $statuses)."')";
			$criteria->group = 'employee_absence_id';
			$eas = $ea->findAll($criteria);
			$message = "";

			// Limitation check
			$wdRejectedIdsNoDelete = [];
			foreach ($eas as $ea)
			{
				$limitationCheck = true;
				
				if (Yang::customerDbPatchName() == "flex" && $ea->status == Status::STOCK_REJECTED && strpos($ea->created_by, "XMLAutoImport") !== false) {
					$empAbsences = new EmployeeAbsence;
					$criteria = new CDbCriteria();
					$criteria->condition = "`employee_contract_id` = '$employee_contract_id' AND `day` IN ('". implode("', '", $daysToDelete)."') AND `status` IN ('". implode("', '", $statuses)."')";
					$absences = $empAbsences->findAll($criteria);
					foreach ($absences as $absence) {
						if ($absence->day == $ea->day && $ea->row_id != $absence->row_id) {
							$limitationCheck = false;
							$stateTypeLimitations["error"] = false;
							$wdRejectedIdsNoDelete[] = $ea->row_id;
						}
					}
				}

				if ($limitationCheck) {
					$stateTypeLimitations = AHPSaveMethods::getStateTypeLimitations($ea->state_type_id, $ea->employee_contract_id, $ea->day);
				}

				if ($stateTypeLimitations["error"]) {
					$error = true;
					$message .= $ea->day . " " . Dict::getValue("stateTypeLimitedByEmployeeDataDel") . '<div style="display:none;">stateTypeLimitedByEmployeeDataDel</div>'."<br/>";
				}
				
				$rightToHandleStateType = StateTypeRight::hasUserRightToHandleStateType($ea->state_type_id);
				
				if (!$rightToHandleStateType) {
					$error = true;
					$message .= Dict::getModuleValue("ttwa-ahp", "error_no_right_to_handle_state_type") . '<div style="display:none;">error_no_right_to_handle_state_type</div>'."<br/>";
				}

				if ((int)App::getSetting("limitAbsencesByAbsenceDays") && $ea->state_type_id != "a272f564576d443e7832587126b070aa" && $ea->employee_contract_id != Yang::getUserECID())
				{
					$oneDay = AHPSaveMethods::getIsApproveAble($ea->day, $ea->employee_contract_id, "absenceApproverOneDay");
					$moreDays = AHPSaveMethods::getIsApproveAble($ea->day, $ea->employee_contract_id, "absenceApproverMoreDays");
					if (!$moreDays && $datediff > 1) {
						$error = true;
						$message .= $ea->day . " - " . Dict::getModuleValue("ttwa-ahp", "error_only_one_day_approve") . '<div style="display:none;">error_only_one_day_approve</div>'."<br/>";
					} else if (!$moreDays && AHPSaveMethods::getPreAndPostDayAbsences($ea->day, $ea->employee_contract_id, $ea->state_type_id) > 0) {
						$error = true;
						$message .= $ea->day . " - " . Dict::getModuleValue("ttwa-ahp", "error_only_one_day_approve") . '<div style="display:none;">error_only_one_day_approve</div>'."<br/>";
					} else if (!$oneDay && !$moreDays) {
						$error = true;
						$message .= $ea->day . " - " . Dict::getModuleValue("ttwa-ahp", "error_no_oneday_or_multiday_approve_right") . '<div style="display:none;">error_no_oneday_or_multiday_approve_right</div>'."<br/>";
					}
				}
			}

			if ($error){
                $status = 'error';
				$msg = $message;
			}else{ 
				$log = 'found absences: '.count($eas);
				Yang::log($log, 'log', 'system.AHPDeleteMethods.deleteAbsences');

				$log = '$approvalRight: '.$approvalRight;
				Yang::log($log, 'log', 'system.AHPDeleteMethods.deleteAbsences');

				foreach ($eas as $ea) {
	
					$ea_row = new EmployeeAbsence;
					$criteria = new CDbCriteria();
					$criteria->condition = "`employee_contract_id` = '$employee_contract_id' AND `day` IN ('". implode("', '", $daysToDelete)."') AND `status` IN ('". implode("', '", $statuses)."') AND `employee_absence_id` = '{$ea->employee_absence_id}' AND `row_id` NOT IN ('". implode("', '", $wdRejectedIdsNoDelete)."')";
					$ea_rows = $ea_row->findAll($criteria);

					foreach ($ea_rows as $del) {
						if ((int)$del->status === 1) { // required
							$del->status = 5;
							$del->del_employee_absence_id = $del->employee_absence_id;
							$del->employee_absence_id = md5('delete'.$employee_contract_id.$absenceBegin.$absenceEnd.date('YmdHis'));
						} else if ((int)$del->status === 2) { // accepted
							$del->status = 4;
							$del->del_employee_absence_id = $del->employee_absence_id;
							$del->employee_absence_id = md5('delete'.$employee_contract_id.$absenceBegin.$absenceEnd.date('YmdHis'));
						} else if ((int)$del->status === 3) { // rejected
							$del->status = 5;
							$del->del_employee_absence_id = $del->employee_absence_id;
							$del->employee_absence_id = md5('delete'.$employee_contract_id.$absenceBegin.$absenceEnd.date('YmdHis'));
						}
	          
	                    $auto_absence=StateTypeRight::getAutoAbsence($del->state_type_id);	
	                                        
						if (($approvalRight && (Yang::getUserECID() !== $employee_contract_id) || $approvalHimselfRight || $auto_absence)) {
							$del->status = 5;
							$del->employee_absence_id = md5('delete'.$employee_contract_id.$absenceBegin.$absenceEnd.date('YmdHis'));
						}

						$deleted_ids[] = $del->row_id;

						$valid = $del->validate();

						if ($valid) {
							$del->save();

							$log = 'delete saved - row_id: '.$del->row_id;
							Yang::log($log, 'log', 'system.AHPDeleteMethods.deleteAbsences');
							//TODO valid itt gyűjteni a contractot és napot
							
						} else {
							$error = MyActiveForm::_validate($del);

							$log = 'delete error - row_id: '.$del->row_id.' - error: '.$error;
							Yang::log($log, 'log', 'system.AHPDeleteMethods.deleteAbsences');
						}
					}
					if (App::getSetting('showAttendanceSheetNotifictionAfterLogin') == '1')
					{
						$aasFilter = [
							'valid_from'		=> $absenceBegin,
							'valid_to'			=> $absenceEnd,
							'employee_contract'	=> $employee_contract_id
							];

						$aas = new AttendanceSheetStatusAction($aasFilter);
						$aas->attendanceSheetStatusUnlock();
					}
				}

				
			}
		}
	}
	
	public function getStateTypeIdForAbsence($absenceDay, $employee_contract_id){
		$SQL = "SELECT state_type_id FROM employee_absence WHERE employee_contract_id='{$employee_contract_id}' and day='{$absenceDay}';";
		$RES = dbFetchAll($SQL);		
		$RET = '';
		if (isset($RES[0])){
			$RET = $RES[0][state_type_id];
		}
		return $RET;
	}

	public static function getEmployeeCalcUsedDaytype($employee_contract_id, $daysToDelete)
	{
		$result = false;
		$ecud = new EmployeeCalcUsedDaytype;
		$criteria = new CDbCriteria();
		$criteria->select = "count(`employee_contract_id`) AS employee_contract_id";
		$criteria->condition = "`employee_contract_id` = '$employee_contract_id' 
								AND `day` IN ('". implode("', '", $daysToDelete)."') 
								AND `status` = ". Status::LOCKED;
		$ecudResult = $ecud->find($criteria);
		if ($ecudResult->employee_contract_id > 0) {
			$result = true;
		}

		return $result;
	}

}




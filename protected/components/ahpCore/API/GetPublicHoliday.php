<?php 
class GetPublicHoliday
{
	private $publicHolidays;
	
	public function __construct($startDate, $endDate)
	{
		$this->publicHolidays = [];
		
		$ph = "
			SELECT
				*
			FROM
				`public_holiday`
			WHERE
				`holidaydate` BETWEEN '".$startDate."' AND '".$endDate."'
					AND `status` = 2
		";
		
		$res_ph = dbFetchAll($ph);
		
		foreach ($res_ph as $holiday) {
			$this->publicHolidays[$holiday["country"]][$holiday["holidaydate"]] = array("type"=>$holiday["type"],"name_dict_id"=>$holiday["name_dict_id"],"chdate"=>$holiday["chdate"],);
		}
	}

	public function getPublicHolidays()
	{
		return isset($this->publicHolidays)?$this->publicHolidays:null;
	}

	public function getPublicHolidaysByEmpCountry($employee_contract_id) {
		if (empty($employee_contract_id)) return [];

		$countryCode = $this->getCountryByEcId($employee_contract_id);
		if (!$countryCode) return [];

		return isset($this->publicHolidays[$countryCode]) ? $this->publicHolidays[$countryCode] : [];
	}

	public function getPublicHolidaysByCountry($countryCode)
	{
		if (!$countryCode) return [];
		return isset($this->publicHolidays[$countryCode]) ? $this->publicHolidays[$countryCode] : [];
	}

	public function get($dayToProcess = "ALL", $types = "ALL", $country = "hu")
	{
		if($dayToProcess === "ALL"){
			return isset($this->publicHolidays)?$this->publicHolidays:null;
		}
		if($types === "ALL"){
			return isset($this->publicHolidays[$country][$dayToProcess])?$this->publicHolidays[$country][$dayToProcess]:null;
		}
		if(isset($this->publicHolidays[$country][$dayToProcess])){
			if(is_array($types)){
				foreach ($types as $type) {
					if($type == $this->publicHolidays[$country][$dayToProcess]["type"]){
						return $this->publicHolidays[$country][$dayToProcess];
					}
				}
			}elseif($types == $this->publicHolidays[$country][$dayToProcess]["type"]){
				return $this->publicHolidays[$country][$dayToProcess];
			}
		}
		return null;
	}

	public static function getCountryByEcId($employee_contract_id) {
		$publishedStatus = Status::PUBLISHED;
		$SQL = "SELECT
					company.`country`
				FROM
					`employee_contract`
				LEFT JOIN `employee` ON
					employee.`employee_id` = employee_contract.`employee_id`
					AND employee.`status` = $publishedStatus
					AND (CURDATE() BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, '".App::getSetting("defaultEnd")."'))
				LEFT JOIN `company` ON
					company.`company_id` = employee.`company_id`
					AND (CURDATE() BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, '".App::getSetting("defaultEnd")."'))
					AND company.`status` = $publishedStatus
				WHERE
					employee_contract.`status` = $publishedStatus
					AND (CURDATE() BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '".App::getSetting("defaultEnd")."'))
					AND (CURDATE() BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '".App::getSetting("defaultEnd")."'))
					AND employee_contract.`employee_contract_id` = '$employee_contract_id'
				LIMIT
					1
			";

		$res = dbFetchAll($SQL);

		if (is_array($res) && count($res)) {
			return $res[0]["country"];
		}

		return false;
	}
}

?>
<?php #yii2: done

'yii2-only`;

	namespace app\components\ahpCore\API;
	use app\components\App;
	use app\components\Dict;
	use app\components\ahpCore\API\AHPAbsenceFunctions;
	use app\models\ApproverRelatedGroup;
	use app\models\Employee;
	use app\models\EmployeeAbsence;
	use app\models\EmployeeGroup;
	use app\models\EmployeeGroupConfig;
	use app\models\EmployeePosition;
	use app\models\StateType;
	use app\models\Status;
	use app\models\Team;
	use Yang;

`/yii2-only';


class AHPAbsenceFunctions
{
	const G2AHP_YEAR_MODE		= 0;
	const G2AHP_MONTH_MODE		= 1;

	public static function getBaseAbsencesByStateType($stateType = null) {
		/**
		 * find base absence types for state
		 */
		$latb = "
			SELECT
				`base_absence_type_id`
			FROM
				`link_at_to_bat`
			";
		if (!empty($stateType))
		{
			$latb .= "
				WHERE
					`state_type_id` IN ('" . str_replace(";", "', '", $stateType) . "')";
		}

		$latbResults = dbFetchAll($latb);

		$base_absences = array();

		foreach ($latbResults as $result) {
			$base_absences[] = $result["base_absence_type_id"];
		}

		return $base_absences;
	}

	public static function getAbsenceDataByID($employee_absence_id, $status = null, $absenceDay = null) {
		$absenceData = array();

		$ea = new EmployeeAbsence;
		$crit = new CDbCriteria();
		$crit->condition = "`employee_absence_id` = '$employee_absence_id'";
		if (!empty($status)) {
			$crit->condition .= " AND `status` IN ($status)";
		}
		if (!empty($absenceDay)) {
			$crit->condition .= " AND `day` = '{$absenceDay}'";
		}

		$crit->order = 'day ASC';
		$re_ea = $ea->findAll($crit);

		if (count($re_ea)) {
            $defaultEnd = \App::getSetting("defaultEnd");
            $day = $re_ea[0]->day;
            $contractId = $re_ea[0]->employee_contract_id;
			$ep= new EmployeePosition;
			$crit->alias="ep";
            if (EmployeeGroupConfig::isActiveGroup('employee_position_id')) {
                $crit->join="LEFT JOIN `employee_contract` ON
					    employee_contract.`employee_contract_id` = '{$contractId}'
                    AND employee_contract.`status` = ".STATUS::PUBLISHED."
                    AND '".$day."' BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'".$defaultEnd."')
                    AND '".$day."' BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`,'".$defaultEnd."')
                    " . EmployeeGroup::getLeftJoinSQL("employee_position_id","employee_contract",$day);
                $crit->condition = "ep.`status` = ".STATUS::PUBLISHED."
                    AND '".$day."' BETWEEN ep.`valid_from` AND IFNULL(ep.`valid_to`,'".$defaultEnd."')
                    AND employee_group_employee_position_id.`employee_contract_id`='".$contractId."'
                    AND employee_group_employee_position_id.`group_value`=ep.`employee_position_id`";
            } else {
                $crit->join="LEFT JOIN `employee_contract` ec ON
                        ep.employee_position_id=ec.employee_position_id
                    AND ec.`status` = ".STATUS::PUBLISHED."
                    AND '".$day."' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`,'".$defaultEnd."')
                    AND '".$day."' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`,'".$defaultEnd."')";
                $crit->condition = "ep.`status` = ".STATUS::PUBLISHED."
                    AND '".$day."' BETWEEN ep.`valid_from` AND IFNULL(ep.`valid_to`,'".$defaultEnd."')
                    AND ec.employee_contract_id='".$contractId."'";
            }
            $crit->order = '';
			$re_ep = $ep->findAll($crit);

			$absenceData["employee_absence_id"]		= $re_ea[0]->employee_absence_id;
			$absenceData["begin"]					= date('Y.m.d', strtotime($day));
			$absenceData["end"]						= date('Y.m.d', strtotime($re_ea[count($re_ea)-1]->day));
			$absenceData["days"]					= count($re_ea);
			$absenceData["score"]					= $re_ea[0]->score;
			$absenceData["status"]					= $re_ea[0]->status;
			$absenceData["state_type_id"]			= $re_ea[0]->state_type_id;
			$absenceData["absence_name"]			= AHPAbsenceFunctions::getAbsenceNameByID($absenceData["state_type_id"]);
			$absenceData["employee_name"]			= Employee::getEmployeeFullnameByEcID($contractId);
			$absenceData["employee_name_eng"]		= Employee::getEmployeeFullnameByEcID($contractId, "en");
			$absenceData["employee_position_name"] 	= count($re_ep)?$re_ep[0]->employee_position_name:"";
			$absenceData["user_id"] 				= Employee::getEmployeeUserIDByEcIDWithoutReceiveEmailCheck($contractId);
		}

		return $absenceData;
	}

	public static function getAbsenceDataByPK($row_ids = array()) {
		$absenceData = [];

		$ea = new EmployeeAbsence;
		$crit = new CDbCriteria();
		if (count($row_ids)) {
			$crit->condition = "`row_id` IN ('".implode("','", $row_ids)."')";
		}
		$crit->group = 'employee_absence_id';
		$re_ea = $ea->findAll($crit);

		foreach ($re_ea as $abs) {
			$ret = AHPAbsenceFunctions::getAbsenceDataByID($abs->employee_absence_id, $abs->status);

			if (count($ret)) {
				$absenceData[] = $ret;
			}
		}

		return $absenceData;
	}

//	public function getOneAbsenceDataByPK($row_ids = array()) {
//		$absenceData = [];
//
//		$ea = new EmployeeAbsence;
//		$crit = new CDbCriteria();
//		if (count($row_ids)) {
//			$crit->condition = "`row_id` IN ('".implode("','", $row_ids)."')";
//		}
//		$crit->group = 'employee_absence_id';
//
//		$re_ea = $ea->findAll($crit);
//
//		foreach ($re_ea as $abs) {
//			$data = [];
//
//			$data["employee_absence_id"] = $abs->employee_absence_id;
//			$data["begin"]			= $abs->day;
//			$data["end"]			= $abs->day;
//			$data["state_type_id"]	= $abs->state_type_id;
//			$data["absence_name"]	= AHPAbsenceFunctions::getAbsenceNameByID($abs->state_type_id);
//			$data["days"]			= 1;
//			$data["status"]			= $abs->status;
//			$data["employee_name"]	= Employee::getEmployeeFullnameByEcID($abs->employee_contract_id);
//
//			$absenceData[] = $data;
//		}
//
//		return $absenceData;
//	}

	public static function getAbsenceNameByID($state_type_id) {
		$at = new StateType;
		$crit = new CDbCriteria();
		$crit->condition = "`state_type_id` = '$state_type_id'";
		$res_at = $at->find($crit);

		if ($res_at) {
			return Dict::getModuleValue("ttwa-ahp",$res_at->name_dict_id);
		}
		return null;
	}

	/**
	 * A megadott szerződésszám alapján kikeresi az ugyanabban a groupban ($usedGroup szerint)
	 * található embereket, és róluk generál heatmap-et
	 */
	public static function getHeatmapData($startDate, $endDate, $mode = "ec", $data = null, $usedGroup = null, $status = "1,2,4",$ahpMode = AHPAbsenceFunctions::G2AHP_YEAR_MODE, $approverProcessId = ["absenceApprover","absenceView"]) {
		if ($usedGroup === null) {
			$usedGroup = AHPAbsenceFunctions::getCurrentHeatmapGroup($ahpMode);
		}
		$heatmapGroups = App::getSetting("heatmapGroups");

		if (empty($usedGroup)) {
			return array();
		}

		$MAIN_SELECT = $SELECT = $JOIN = $WHERE = "";

		$activeHeatmap2 = null;

		$filterWorkTypeAbsence = "AND NOT EXISTS(
									SELECT * FROM `state_type`
									WHERE `state_type_id` = ea.`state_type_id`
									AND `status` = " . Status::PUBLISHED . "
									AND (`type` = 'work')
								)";

		if ($mode === "ec") {
			$employeeContract = $data;

			$WHERE = "1";

			switch ($usedGroup) {
				case "company":
					$MAIN_SELECT = " SUM( IF(ea_row_id IS NOT NULL,1,0) ) AS used ";
					$SELECT = "
						company.`{$usedGroup}_id` AS usedgroup,
						company.`{$usedGroup}_name` AS usedgroup_name
					";

					if (EmployeeGroupConfig::isActiveGroup('company_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("company_id","employee_contract","c.`date`");
					}
                    if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("workgroup_id","employee_contract","c.`date`");
					}


					$JOIN .= "LEFT JOIN `company` ON company.`company_id` = ".EmployeeGroup::getActiveGroupSQL("company_id","employee")."
							AND (c.`date` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company.`status` = ".Status::PUBLISHED."

						LEFT JOIN `employee` e ON e.`status` = ".Status::PUBLISHED." AND e.`{$usedGroup}_id` = employee.`{$usedGroup}_id`
								AND c.`date` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
                                                LEFT JOIN `employee_contract` ec_group ON ec_group.`status` = ".Status::PUBLISHED." AND ec_group.employee_id = e.employee_id
                                                                AND c.`date` BETWEEN ec_group.`valid_from` AND IFNULL(ec_group.`valid_to`, '".App::getSetting("defaultEnd")."')
						LEFT JOIN `employee_absence` ea ON
									ea.`employee_contract_id` = ec_group.`employee_contract_id`
								AND ea.`day` = c.`date`
								AND ea.`status` IN ($status)
								{$filterWorkTypeAbsence}
                       	LEFT JOIN `workgroup` ON
								workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
								AND workgroup.`status`=".Status::PUBLISHED."
								AND c.`date` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '".App::getSetting("defaultEnd")."')

					";
					if(weHaveModule('ttwa-csm')){
						$JOIN .= "LEFT JOIN `employee_competency` ON
								employee_competency.`employee_contract_id` = employee_contract.`employee_contract_id`
								AND employee_competency.`status`=".Status::PUBLISHED."
								AND c.`date` BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`,'".App::getSetting("defaultEnd")."')
							";
					}
					break;

				case "unit":
                    $MAIN_SELECT = " SUM( IF(ea_row_id IS NOT NULL,1,0) ) AS used ";
					$SELECT = "
						unit.`{$usedGroup}_id` AS usedgroup,
						unit.`{$usedGroup}_name` AS usedgroup_name
					";

					if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("unit_id","employee_contract","c.`date`");
					}
					if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("workgroup_id","employee_contract","c.`date`");
					}

					$JOIN .= "LEFT JOIN `unit` ON unit.`unit_id` = ".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
							AND (c.`date` BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND unit.`status` = ".Status::PUBLISHED."
							";
					if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
						$JOIN .= "LEFT JOIN `employee_group` employee_group_unit_id2 ON
										employee_group_unit_id2.`group_value`=employee_group_unit_id.`group_value`
									AND employee_group_unit_id2.`group_id`='unit_id'
									AND employee_group_unit_id2.`status`=".Status::PUBLISHED."
									AND c.`date` BETWEEN employee_group_unit_id2.`valid_from` AND IFNULL(employee_group_unit_id2.`valid_to`, '".App::getSetting("defaultEnd")."')

								LEFT JOIN `employee_contract` ec_group ON ec_group.`status` = ".Status::PUBLISHED."
									AND ec_group.employee_contract_id = employee_group_unit_id2.employee_contract_id
									AND c.`date` BETWEEN ec_group.`valid_from` AND IFNULL(ec_group.`valid_to`, '".App::getSetting("defaultEnd")."')
						";
					}
					else {
						$JOIN .= "LEFT JOIN `employee` e ON e.`status` = ".Status::PUBLISHED." AND e.`{$usedGroup}_id` = employee.`{$usedGroup}_id`
									AND c.`date` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
								LEFT JOIN `employee_contract` ec_group ON ec_group.`status` = ".Status::PUBLISHED." AND ec_group.employee_id = e.employee_id
									AND c.`date` BETWEEN ec_group.`valid_from` AND IFNULL(ec_group.`valid_to`, '".App::getSetting("defaultEnd")."')
								";
					}

					$JOIN .= "LEFT JOIN `employee_absence` ea ON
									ea.`employee_contract_id` = ec_group.`employee_contract_id`
								AND ea.`day` = c.`date`
								AND ea.`status` IN ($status)
								{$filterWorkTypeAbsence}
                        LEFT JOIN `workgroup` ON
								workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
								AND workgroup.`status`=".Status::PUBLISHED."
								AND c.`date` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '".App::getSetting("defaultEnd")."')

					";
					if(weHaveModule('ttwa-csm')){
						$JOIN .= "LEFT JOIN `employee_competency` ON
								employee_competency.`employee_contract_id` = employee_contract.`employee_contract_id`
								AND employee_competency.`status`=".Status::PUBLISHED."
								AND c.`date` BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`,'".App::getSetting("defaultEnd")."')
							";
					}
					break;
				case "company_org_group1":
				case "company_org_group2":
				case "company_org_group3":
                    $MAIN_SELECT = " SUM( IF(ea_row_id IS NOT NULL,1,0) ) AS used ";
					$SELECT = "
						{$usedGroup}.`company_org_group_id` AS usedgroup,
						{$usedGroup}.`company_org_group_name` AS usedgroup_name
					";



					if (EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("company_org_group1_id","employee_contract","c.`date`");
					}
					if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("company_org_group2_id","employee_contract","c.`date`");
					}
					if (EmployeeGroupConfig::isActiveGroup('company_org_group3_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("company_org_group3_id","employee_contract","c.`date`");
					}
					if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("unit_id","employee_contract","c.`date`");
					}
					if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("workgroup_id","employee_contract","c.`date`");
					}

					$JOIN .= "
						LEFT JOIN `company_org_group1` company_org_group1 ON
								company_org_group1.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group1_id","employee")."
							AND (c.`date` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company_org_group1.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company_org_group2` company_org_group2 ON
								company_org_group2.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group2_id","employee")."
							AND (c.`date` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company_org_group2.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company_org_group3` company_org_group3 ON
								company_org_group3.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group3_id","employee")."
							AND (c.`date` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company_org_group3.`status` = ".Status::PUBLISHED."
						LEFT JOIN `unit` ON
								unit.`unit_id` = ".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
							AND (c.`date` BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND unit.`status` = ".Status::PUBLISHED."
						LEFT JOIN `workgroup` ON
								workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
								AND workgroup.`status`=".Status::PUBLISHED."
								AND c.`date` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '".App::getSetting("defaultEnd")."')

					";

					$JOIN .= "
						LEFT JOIN `employee` e_group ON e_group.`status` = 2
								AND (c.`date` BETWEEN e_group.`valid_from` AND IFNULL(e_group.`valid_to`, '".App::getSetting("defaultEnd")."'))
								/*AND e_group.`{$usedGroup}_id` = employee.`{$usedGroup}_id`*/
						LEFT JOIN `employee_contract` ec_group ON
								ec_group.`status` = ".Status::PUBLISHED."
							AND ec_group.`employee_id` = e_group.`employee_id`
							AND c.`date` BETWEEN ec_group.`valid_from` AND IFNULL(ec_group.`valid_to`, '".App::getSetting("defaultEnd")."')
							AND c.`date` BETWEEN ec_group.`ec_valid_from` AND IFNULL(ec_group.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
					";

					if (EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("company_org_group1_id","ec_group","c.`date`","_2");
					}
					if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("company_org_group2_id","ec_group","c.`date`","_2");
					}
					if (EmployeeGroupConfig::isActiveGroup('company_org_group3_id')) {
						$JOIN .= EmployeeGroup::getLeftJoinSQL("company_org_group3_id","ec_group","c.`date`","_2");
					}

					$JOIN .= "
						LEFT JOIN `company_org_group1` e_group_company_org_group1 ON
								e_group_company_org_group1.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group1_id","e_group","_2")."
							AND (c.`date` BETWEEN e_group_company_org_group1.`valid_from` AND IFNULL(e_group_company_org_group1.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND e_group_company_org_group1.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company_org_group2` e_group_company_org_group2 ON
								e_group_company_org_group2.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group2_id","e_group","_2")."
							AND (c.`date` BETWEEN e_group_company_org_group2.`valid_from` AND IFNULL(e_group_company_org_group2.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND e_group_company_org_group2.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company_org_group3` e_group_company_org_group3 ON
								e_group_company_org_group3.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group3_id","e_group","_2")."
							AND (c.`date` BETWEEN e_group_company_org_group3.`valid_from` AND IFNULL(e_group_company_org_group3.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND e_group_company_org_group3.`status` = ".Status::PUBLISHED."
					";

					$JOIN .= "
						LEFT JOIN `employee_absence` ea ON
									ea.`employee_contract_id` = ec_group.`employee_contract_id`
							AND ea.`day` = c.`date`
							AND ea.`status` IN ($status)
							{$filterWorkTypeAbsence}
					";

					if(weHaveModule('ttwa-csm')){
						$JOIN .= "LEFT JOIN `employee_competency` ON
								employee_competency.`employee_contract_id` = employee_contract.`employee_contract_id`
								AND employee_competency.`status`=".Status::PUBLISHED."
								AND c.`date` BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`,'".App::getSetting("defaultEnd")."')
							";
					}

					$WHERE .= "
						AND {$usedGroup}.`company_org_group_id` = e_group_{$usedGroup}.`company_org_group_id`
					";

					break;
				case "workgroup":
					$MAIN_SELECT = "
						SUM( IF(ea_row_id IS NOT NULL,1,0) ) AS used
					";
					$SELECT = "
						{$usedGroup}.`{$usedGroup}_id` AS usedgroup,
						{$usedGroup}.`{$usedGroup}_name` AS usedgroup_name
					";
					$JOIN = "
						LEFT JOIN
							`employee_contract` ec_group ON ec_group.`status` = 2 AND ec_group.`{$usedGroup}_id` = employee_contract.`{$usedGroup}_id`
								AND c.`date` BETWEEN ec_group.`valid_from` AND IFNULL(ec_group.`valid_to`, '".App::getSetting("defaultEnd")."')
								AND c.`date` BETWEEN ec_group.`ec_valid_from` AND IFNULL(ec_group.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
						LEFT JOIN `workgroup` ON
								workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
								AND workgroup.`status`=".Status::PUBLISHED."
								AND c.`date` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '".App::getSetting("defaultEnd")."')
						LEFT JOIN `employee_absence` ea ON
									ea.`employee_contract_id` = ec_group.`employee_contract_id`
							AND ea.`day` = c.`date`
							AND ea.`status` IN ($status)
							{$filterWorkTypeAbsence}
					";
					if(weHaveModule('ttwa-csm')){
						$JOIN .= "LEFT JOIN `employee_competency` ON
								employee_competency.`employee_contract_id` = employee_contract.`employee_contract_id`
								AND employee_competency.`status`=".Status::PUBLISHED."
								AND cal.`date` BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`,'".App::getSetting("defaultEnd")."')
							";
					}
					break;
			}

			if (!empty($employeeContract)) {
				$WHERE .= "
					AND employee_contract.`employee_contract_id` = '$employeeContract'
				";
			}
		} else if ($mode === "group") {
			$group = $data;

			$activeHeatmap2=Team::isActiveHeatmap2();
			$teamtype=substr($group,0,3);

			if($activeHeatmap2)
			{
				$group=substr($group,3);
			}


			switch ($usedGroup) {
				case "company":
				case "unit":
				case "company_org_group1":
				case "company_org_group2":
				case "company_org_group3":
					if(!$activeHeatmap2 || $teamtype==="H1-")
					{
						$MAIN_SELECT = "
							SUM( IF(ea_row_id IS NOT NULL,1,0) ) AS used
						";
						$SELECT = "
							employee.`{$usedGroup}_id` AS usedgroup,
						";
						if ($usedGroup === "unit") {
							$SELECT .= "
								{$usedGroup}.`{$usedGroup}_name` AS usedgroup_name
							";
						}
						else if ($usedGroup === "company")
						{
							$SELECT .= "
								{$usedGroup}.`{$usedGroup}_name` AS usedgroup_name
							";
						}
						else {
							$SELECT .= "
								{$usedGroup}.`company_org_group_name` AS usedgroup_name
							";
						}
					}
					$JOIN = "
						LEFT JOIN `employee_absence` ea ON
								ea.`employee_contract_id` = employee_contract.`employee_contract_id`
							AND ea.`day` = c.`date`
							AND ea.`status` IN ($status)
							{$filterWorkTypeAbsence}
						";

						if(EmployeeGroupConfig::isActiveGroup('company_org_group1_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("company_org_group1_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('company_org_group2_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("company_org_group2_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('company_org_group3_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("company_org_group3_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('unit_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("unit_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('company_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("company_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('workgroup_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("workgroup_id","employee_contract","c.`date`");
						}
					$JOIN.="LEFT JOIN `company_org_group1` company_org_group1 ON
								company_org_group1.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group1_id","employee")."
							AND (c.`date` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company_org_group1.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company_org_group2` company_org_group2 ON
								company_org_group2.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group2_id","employee")."
							AND (c.`date` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company_org_group2.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company_org_group3` company_org_group3 ON
								company_org_group3.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group3_id","employee")."
							AND (c.`date` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company_org_group3.`status` = ".Status::PUBLISHED."
						LEFT JOIN `unit` ON
								unit.`unit_id` = ".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
							AND (c.`date` BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND unit.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company` ON
								company.`company_id` = ".EmployeeGroup::getActiveGroupSQL("company_id","employee")."
							AND (c.`date` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company.`status` = ".Status::PUBLISHED."
						LEFT JOIN `workgroup` ON
								workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
							AND (c.`date` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND workgroup.`status` = ".Status::PUBLISHED."
					";
					if(weHaveModule('ttwa-csm')){
						$JOIN .= "LEFT JOIN `employee_competency` ON
								employee_competency.`employee_contract_id` = employee_contract.`employee_contract_id`
								AND employee_competency.`status`=".Status::PUBLISHED."
								AND c.`date` BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`,'".App::getSetting("defaultEnd")."')
							";
					}
					if (!empty($group)) {
						if($usedGroup=='company_org_group1' || $usedGroup=='company_org_group2' || $usedGroup=='company_org_group3')
						{
							$WHERE = "
								{$usedGroup}.`company_org_group_id` = '{$group}'
							";
						}
						else
						{
							$WHERE = "
								{$usedGroup}.`{$usedGroup}_id` = '{$group}'
							";
						}
					}
					break;
				case "workgroup":
					$MAIN_SELECT = "
						SUM( IF(ea_row_id IS NOT NULL,1,0) ) AS used
					";
					$SELECT = "
						employee_contract.`{$usedGroup}_id` AS usedgroup,
						{$usedGroup}.`{$usedGroup}_name` AS usedgroup_name
					";

					$JOIN = "
						LEFT JOIN `employee_absence` ea ON
								ea.`employee_contract_id` = employee_contract.`employee_contract_id`
							AND ea.`day` = c.`date`
							AND ea.`status` IN ($status)
							{$filterWorkTypeAbsence}
					";

					if(EmployeeGroupConfig::isActiveGroup('workgroup_id'))
					{
						$JOIN .= EmployeeGroup::getLeftJoinSQL("workgroup_id","employee_contract","c.`date`");
					}
					if(EmployeeGroupConfig::isActiveGroup('unit_id'))
					{
						$JOIN.=EmployeeGroup::getLeftJoinSQL("unit_id","employee_contract","c.`date`");
					}

					$JOIN .= "
						LEFT JOIN `workgroup` ON
								workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
							AND (c.`date` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND workgroup.`status` = ".Status::PUBLISHED."
						LEFT JOIN `unit` ON
							unit.`unit_id` = ".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
						AND (c.`date` BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '".App::getSetting("defaultEnd")."'))
						AND unit.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company` ON
								company.`company_id` = ".EmployeeGroup::getActiveGroupSQL("company_id","employee")."
							AND (c.`date` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company.`status` = ".Status::PUBLISHED."

					";
					if(weHaveModule('ttwa-csm')){
						$JOIN .= "LEFT JOIN `employee_competency` ON
								employee_competency.`employee_contract_id` = employee_contract.`employee_contract_id`
								AND employee_competency.`status`=".Status::PUBLISHED."
								AND c.`date` BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`,'".App::getSetting("defaultEnd")."')
							";
					}
					if (!empty($group)) {
						$WHERE = "
							{$usedGroup}.`{$usedGroup}_id` = '{$group}'
						";
					}
					break;
			}

			if($teamtype==="H2-")
			{
				$MAIN_SELECT = "
					SUM( IF(ea_row_id IS NOT NULL,1,0) ) AS used
				";
				$SELECT = "
							'$teamtype$group' AS usedgroup,
							team.`team_name` AS usedgroup_name
						";
				$JOIN .= "
						LEFT JOIN `employee_team` ON
								employee_team.`employee_contract_id` = employee_contract.`employee_contract_id`
							AND employee_team.`status`=".Status::PUBLISHED."
						LEFT JOIN `team` ON
								team.`team_type` = employee_team.`team_type`
							AND team.`team_id` = employee_team.`team_id`
							AND team.`status`=".Status::PUBLISHED."
					";
				if(!App::hasRight('team','use_every_team'))
				{
					$JOIN.="AND team.`owner_user_id`='".userID()."'
					";
				}
				$WHERE = "
								team.`team_type` = ".App::getSetting("heatmapTeam")."
							AND team.`team_id` = '$group'
						";

			}
		} else if ($mode === "competency") {
			$MAIN_SELECT = "
						SUM( IF(ea_row_id IS NOT NULL,1,0) ) AS used
					";
					$SELECT = "
						employee_competency.`competency_id` AS usedgroup,
						competency.`competency_name` AS usedgroup_name
					";
					$JOIN = "
						LEFT JOIN `employee_absence` ea ON
								ea.`employee_contract_id` = employee_contract.`employee_contract_id`
							AND ea.`day` = c.`date`
							AND ea.`status` IN ($status)
							{$filterWorkTypeAbsence}
						LEFT JOIN `employee_competency` ON
								employee_competency.`employee_contract_id` = employee_contract.employee_contract_id
							AND (c.`date` BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND employee_competency.`status` = ".Status::PUBLISHED."
						LEFT JOIN `competency` ON
								competency.`competency_id` = employee_competency.competency_id
							AND competency.`status` = ".Status::PUBLISHED."
					";
					if(EmployeeGroupConfig::isActiveGroup('company_org_group1_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("company_org_group1_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('company_org_group2_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("company_org_group2_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('company_org_group3_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("company_org_group3_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('unit_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("unit_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('company_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("company_id","employee_contract","c.`date`");
						}
						if(EmployeeGroupConfig::isActiveGroup('workgroup_id'))
						{
							$JOIN.=EmployeeGroup::getLeftJoinSQL("workgroup_id","employee_contract","c.`date`");
						}
					$JOIN.="LEFT JOIN `company_org_group1` company_org_group1 ON
								company_org_group1.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group1_id","employee")."
							AND (c.`date` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company_org_group1.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company_org_group2` company_org_group2 ON
								company_org_group2.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group2_id","employee")."
							AND (c.`date` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company_org_group2.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company_org_group3` company_org_group3 ON
								company_org_group3.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group3_id","employee")."
							AND (c.`date` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company_org_group3.`status` = ".Status::PUBLISHED."
						LEFT JOIN `unit` ON
								unit.`unit_id` = ".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
							AND (c.`date` BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND unit.`status` = ".Status::PUBLISHED."
						LEFT JOIN `company` ON
								company.`company_id` = ".EmployeeGroup::getActiveGroupSQL("company_id","employee")."
							AND (c.`date` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND company.`status` = ".Status::PUBLISHED."
						LEFT JOIN `workgroup` ON
								workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
							AND (c.`date` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '".App::getSetting("defaultEnd")."'))
							AND workgroup.`status` = ".Status::PUBLISHED;
					if (!empty($data)) {
						$WHERE = "
							employee_competency.`competency_id` = '{$data}'
						";
					}
		}

		if (Yang::customerDbPatchName() === "flex" && $ahpMode == AHPAbsenceFunctions::G2AHP_MONTH_MODE && empty($data) && $mode === "group")
		{
			$filters = requestParam("searchInput");
			$searchMatches =
			[
				"company__company_id" => "`company`.`company_id`",
				"compnay_org_group1__company_org_group_id" => "`company_org_group1`.`company_org_group_id`",
				"unit__unit_id" => "`unit`.`unit_id`",
				"cost_center__cost_center_id" => "`cost_center`.`cost_center_id`",
				"company_org_group2__company_org_group_id" => "`company_org_group2`.`company_org_group_id`",
				"employee_ext2__ext2_option23" => "`employee_ext2`.`ext2_option23`"
			];
			if (EmployeeGroupConfig::isActiveGroup('cost_center_id')) {
				$JOIN .= EmployeeGroup::getLeftJoinSQL("cost_center_id", "employee_contract", "c.`date`");
			}
			$JOIN .= "
				LEFT JOIN `employee_ext2` ON
						`employee_ext2`.`employee_id` = `employee`.`employee_id`
					AND `employee_ext2`.`status` = " . Status::PUBLISHED . "
					AND (c.`date` BETWEEN `employee_ext2`.`valid_from` AND IFNULL(`employee_ext2`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				LEFT JOIN `employee_cost` ON
						`employee_cost`.`employee_contract_id` = `employee_contract`.`employee_contract_id`
					AND `employee_cost`.`status` = " . Status::PUBLISHED . "
					AND (c.`date` BETWEEN `employee_cost`.`valid_from` AND IFNULL(`employee_cost`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				LEFT JOIN `cost_center` ON
						`cost_center`.`cost_center_id` = " . EmployeeGroup::getActiveGroupSQL("cost_center_id", "employee_cost") . "
					AND (c.`date` BETWEEN `cost_center`.`valid_from` AND IFNULL(`cost_center`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
					AND `cost_center`.`status` = " . Status::PUBLISHED . "
			";
			if (isset($filters["state_type_id"]) && $filters["state_type_id"] != "ALL") {
				$SELECT .= ",
					e_state_type_check.`row_id` AS st_check
				";
				$MAIN_SELECT .= ",
					SUM(IF(st_check IS NOT NULL, 1, 0)) AS st_exists
				";
				$JOIN .= "
				LEFT JOIN `employee_absence` e_state_type_check ON
						e_state_type_check.`employee_contract_id` = `employee_contract`.`employee_contract_id`
					AND e_state_type_check.`day` = c.`date`
					AND e_state_type_check.`state_type_id` = '{$filters["state_type_id"]}'
					AND e_state_type_check.`status` IN ($status)
				";
			}
			foreach ($searchMatches as $filterId => $sqlField)
			{
				if (isset($filters[$filterId]))
				{
					if (!is_array($filters[$filterId])) {
						$filters[$filterId] = [$filters[$filterId]];
					}

					if (!empty($WHERE)) {
						$WHERE .= "
							AND ({$sqlField} IN ('" . implode("','", $filters[$filterId]) . "') OR 'ALL' IN ('" . implode("','", $filters[$filterId]) . "') OR '' IN ('" . implode("','", $filters[$filterId]) . "'))
						";
					} else {
						if (!is_array($filters[$filterId])) { $filters[$filterId] = [$filters[$filterId]]; }
						$WHERE .= "
							({$sqlField} IN ('" . implode("','", $filters[$filterId]) . "') OR 'ALL' IN ('" . implode("','", $filters[$filterId]) . "') OR '' IN ('" . implode("','", $filters[$filterId]) . "'))
						";
					}
				}
			}
			if ($usedGroup == 'company_org_group1' || $usedGroup == 'company_org_group2' || $usedGroup == 'company_org_group3') {
				$GROUPBY1 = "`{$usedGroup}`.`company_org_group_id`, ";
			} else {
				$GROUPBY1 = "`{$usedGroup}`.`{$usedGroup}_id`, ";
			}
			$GROUPBY2 = ", usedgroup";
		} else {
			$GROUPBY1 = "";
			$GROUPBY2 = "";
		}

		$gargSQL = [];
		if (Yang::customerDbPatchName() !== "obo") {
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("Employee", $approverProcessId, false, "c.`date`");
		}
		if ($mode === "ec") {
			$ecIdSel	= "ec_group.`employee_contract_id` AS ec_id,";
		} else {
			$ecIdSel	= "`employee_contract`.`employee_contract_id` AS ec_id,";
		}

		$SQL = "
			SELECT
				date,
				COUNT(*) AS count,
				minimalCount,
				usedgroup,
				usedgroup_name,
				GROUP_CONCAT(ec_id SEPARATOR ';') AS ec_ids
			";
		$SQL.=(!empty($MAIN_SELECT)?", {$MAIN_SELECT}":"");
		$SQL .= "
			FROM (
			SELECT
				c.date,
				{$ecIdSel}
				ea.`row_id` AS ea_row_id,
				";
				if(!$activeHeatmap2 || $teamtype==="H1-") {
					if($mode === "group" && in_array($usedGroup, ["company_org_group1", "company_org_group2", "company_org_group3", "unit", "company", "workgroup"])) {
						$SQL.="$usedGroup.minimal_group_count as minimalCount ";
					} else if($mode === "competency"){
						$SQL.="competency.minimal_count as minimalCount ";
					} else {
						$SQL.="0 AS minimalCount ";
					}
				}
				else
				{
					$SQL.="team.`minimal_team_count` AS minimalCount ";
				}
			$SQL.=(!empty($SELECT)?", {$SELECT}":"");

			$SQL.="
			FROM
				`calendar` c
			LEFT JOIN `employee` ON
					employee.`status` = ".Status::PUBLISHED."
				AND (c.date BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, '".App::getSetting("defaultEnd")."'))
			LEFT JOIN `employee_contract` ON
					employee_contract.`status` = ".Status::PUBLISHED."
				AND employee_contract.`employee_id` = employee.`employee_id`
				AND c.`date` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '".App::getSetting("defaultEnd")."')
				AND c.`date` BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '"
				.App::getSetting("defaultEnd")."')
			{$JOIN}
			";
		if (isset($gargSQL["join"]))
		{
			$SQL .= $gargSQL["join"];
		}
		$SQL .= "
			WHERE
				c.`date` BETWEEN '".$startDate."' AND '".$endDate."'
				AND employee_contract.`row_id` IS NOT NULL
				".(!empty($WHERE)?"AND {$WHERE}":"")."
		";
		if (isset($gargSQL["where"]))
		{
			$SQL .= $gargSQL["where"];
		}
		$SQL .= "
			GROUP BY c.`date`, {$GROUPBY1} employee_contract.`employee_contract_id`
		";

		if ($mode === "ec") {
			$SQL .= ",ec_group.`employee_contract_id`";
		}

		$SQL .= "
			) sel
			GROUP BY date{$GROUPBY2}
		";

		$hmResults = dbFetchAll($SQL);

		$employee_groups = [];
		if (Yang::customerDbPatchName() === "flex" && $ahpMode == AHPAbsenceFunctions::G2AHP_MONTH_MODE && empty($data) && $mode === "group")
		{
			$stExists = [];
			foreach ($hmResults as $result) {
				if (isset($result["st_exists"])) {
					$stExists[$result["usedgroup"]] = (isset($stExists[$result["usedgroup"]])) ? $stExists[$result["usedgroup"]] + (int)$result["st_exists"] : (int)$result["st_exists"];
				}
				$employee_groups[$result["usedgroup"]][$result["date"]] = $result;
			}
			if (!empty($stExists)) {
				foreach ($stExists as $usedgroup => $val) {
					if ($val === 0) {
						unset($employee_groups[$usedgroup]);
					}
				}
			}
		} else {
			foreach ($hmResults as $result) {
				$employee_groups[$result["date"]] = $result;
			}
		}

		return $employee_groups;
	}

	public static function getCurrentHeatmapGroup($ahpMode = AHPAbsenceFunctions::G2AHP_YEAR_MODE) {
		$validGroups = AHPAbsenceFunctions::getHeatmapGroups();

		if(App::hasRight('ahp/absenceplannermonthview', "group_override")
			&& $ahpMode === AHPAbsenceFunctions::G2AHP_MONTH_MODE ){
			$usedGroup = App::getSetting("overrideHeatmapGroupInMonthlyView");
		}else{
			$usedGroup = App::getSetting("heatmapGroup");
		}
		if (!array_key_exists($usedGroup, $validGroups)) {
			return null;
		}

		return $usedGroup;
	}

	public static function getCurrentHeatmapGroupParams($ahpMode = AHPAbsenceFunctions::G2AHP_YEAR_MODE) {
		$validGroups = AHPAbsenceFunctions::getHeatmapGroups();

		$usedGroup = AHPAbsenceFunctions::getCurrentHeatmapGroup($ahpMode);

		return isset($validGroups[$usedGroup])?$validGroups[$usedGroup]:[];
		}

	public static function getHeatmapGroups() {
		return [
			"company"				=> ["table" => "company", "comboId" => "company_id", "comboValue" => "company_name", "filter_table" => "employee", "filter_column" => "company_id","related_group" => "Company"],
			"workgroup"				=> ["table" => "workgroup", "comboId" => "workgroup_id", "comboValue" => "workgroup_name", "filter_table" => "employee_contract", "filter_column" => "workgroup_id","related_group" => "Workgroup"],
			"unit"					=> ["table" => "unit", "comboId" => "unit_id", "comboValue" => "unit_name", "filter_table" => "employee", "filter_column" => "unit_id","related_group" => "Unit"],
			"company_org_group1"	=> ["table" => "company_org_group1", "comboId" => "company_org_group_id", "comboValue" => "company_org_group_name", "filter_table" => "employee", "filter_column" => "company_org_group1_id","related_group" => "CompanyOrgGroup1"],
			"company_org_group2"	=> ["table" => "company_org_group2", "comboId" => "company_org_group_id", "comboValue" => "company_org_group_name", "filter_table" => "employee", "filter_column" => "company_org_group2_id","related_group" => "CompanyOrgGroup2"],
			"company_org_group3"	=> ["table" => "company_org_group3", "comboId" => "company_org_group_id", "comboValue" => "company_org_group_name", "filter_table" => "employee", "filter_column" => "company_org_group3_id","related_group" => "CompanyOrgGroup3"],
		];
	}

	public static function getHeatmapLevels() {
		return [
			0 => "#FFEBEE",
			1 => "#FFCDD2",
			2 => "#EF9A9A",
			3 => "#E57373",
			4 => "#EF5350",
			5 => "#F44336",
			6 => "#E53935",
			7 => "#D32F2F",
			8 => "#C62828",
			9 => "#B71C1C",
		];
	}

	public static function getEmployeesHasSameCompetencies($employee_contract_id, $startDate, $endDate, $employeeContracts, $eaStatus = "1,2,4") {
		$SQL = "
			SELECT
				cal.`date`,
				employee_competency.`competency_id`,
				employee_competency.`employee_contract_id`,
				".Employee::getParam('fullname_with_emp_id_ec_id',array('employee','employee_contract'))." AS fullname,
				competency.`competency_name`,
				competency.`minimal_count`,
				IF(employee_absence.`row_id` IS NOT NULL, 1, 0) AS has_absence
			FROM
				`calendar` cal
			LEFT JOIN
				`employee_competency` ON
					employee_competency.`competency_id` IN (
						SELECT
							employee_competency.`competency_id`
						FROM
							`employee_competency`
						WHERE
							employee_competency.`employee_contract_id` = '$employee_contract_id'
								AND employee_competency.`status` = ".Status::STATUS_PUBLISHED."
								AND employee_competency.`valid_from` <= '$endDate' AND '$startDate' <= IFNULL(employee_competency.`valid_to`, '".App::getSetting("defaultEnd")."')
						GROUP BY
							employee_competency.`competency_id`
					)
					AND employee_competency.`status` = ".Status::STATUS_PUBLISHED."
					AND cal.`date` BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN
				`employee_contract` ON
					employee_contract.`employee_contract_id` = employee_competency.`employee_contract_id`
						AND	employee_contract.`status` = 2
						AND cal.`date` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '".App::getSetting("defaultEnd")."')
						AND cal.`date` BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN
				`employee` ON
					employee.`employee_id` = employee_contract.`employee_id`
						AND employee.`status` = 2
						AND cal.date BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN
				`competency` ON
					employee_competency.`competency_id` = competency.`competency_id`
						AND competency.`status` = ".Status::PUBLISHED."
			LEFT JOIN
				`employee_absence` ON
					employee_absence.`employee_contract_id` = employee_competency.`employee_contract_id`
						AND employee_absence.`day` = cal.`date`
						AND employee_absence.`status` IN ($eaStatus)
			WHERE
				cal.`date` BETWEEN '$startDate' AND '$endDate'
		";
		if (count($employeeContracts)) {
			$SQL .= "
					AND employee_competency.`employee_contract_id` IN ('".(implode("','",$employeeContracts))."')
			";
		}
		$SQL .= "
			GROUP BY
				employee_competency.`employee_contract_id`,
					employee_competency.`competency_id`,
						cal.`date`
			ORDER BY
				competency.`competency_name`,
					".Employee::getParam('fullname_with_emp_id_ec_id',array('employee','employee_contract'))."
		";

		$results = dbFetchAll($SQL);

		$ret = [];
		foreach ($results as $res) {
			if (!isset($ret[$res["competency_id"]]["employee_contracts"])) {
				$ret[$res["competency_id"]]["employee_contracts"] = [];
			}

			if (!isset($ret[$res["competency_id"]][$res["date"]]["used"])) {
				$ret[$res["competency_id"]][$res["date"]]["used"] = 0;
			}

			if (!isset($ret[$res["competency_id"]][$res["date"]]["employee_contracts"])) {
				$ret[$res["competency_id"]][$res["date"]]["employee_contracts"] = [];
			}

			if ($res["employee_contract_id"]) $ret[$res["competency_id"]]["employee_contracts"][$res["employee_contract_id"]] = $res["fullname"];

			$ret[$res["competency_id"]]["competencyName"] = $res["competency_name"];

			$ret[$res["competency_id"]][$res["date"]]["minimalCount"] = $res["minimal_count"];

			if ($res["employee_contract_id"]) $ret[$res["competency_id"]][$res["date"]]["employee_contracts"][$res["employee_contract_id"]] = $res["fullname"];

			$ret[$res["competency_id"]][$res["date"]]["count"] = count($ret[$res["competency_id"]][$res["date"]]["employee_contracts"]);

			if ((int)$res["has_absence"]) {
				$ret[$res["competency_id"]][$res["date"]]["used"]++;
			}
		}
		return $ret;
	}

}

<?php

#yii2: done

'yii2-only`;

	namespace app\components\ahpCore\API;
	use app\components\App;
	use app\models\ApproverRelatedGroup;
	use app\models\EmployeeExtraHours;
	use app\models\EmployeeGroup;
	use app\models\EmployeeGroupConfig;
	use app\models\Status;
	use Yang;

`/yii2-only';

class GetEmployeeExtraHours
{
	private $employeeExtraHours;
	private $STANDBYMODE;

	public function __construct($startDate, $endDate, $employeeContracts = [], $skipCheckApprovers = \false, $approverProcessId =	"workSchedule")
	{
		$this->STANDBYMODE			= (int) App::getSetting("standbyUpgradedVersion");
		$this->employeeExtraHours	= [];

		// check, that ttwa-wfm module is installed
		$checkModuleSQL = "
			SELECT `module` FROM `_sql_version` WHERE `module` = 'ttwa-wfm'
		";

		$checkModuleSQLResults = dbFetchAll($checkModuleSQL);

		if (count($checkModuleSQLResults)) {
			$SQL = $this->getSQL($startDate, $endDate, $employeeContracts, $skipCheckApprovers, $approverProcessId);

			$results = dbFetchAll($SQL);
			$i = 0;
			foreach ($results as $day) {
				$i = isset($this->employeeExtraHours[$day["employee_contract_id"]][$day["date"]][$day["date"]][$i]) ? $i+1 : 0;

				$dateString = $day['date'];
				$startString = $day['standby_start'];
				$endString = $day['standby_end'];
				$day['standby_start_ts'] = strtotime("$dateString $startString UTC");
				$day['standby_end_ts'] = strtotime("$dateString $endString UTC");

				if ($day['standby_start_ts'] > $day['standby_end_ts'] || ($day['standby_start_ts'] === $day['standby_end_ts'] && $this->STANDBYMODE)) {
					$dayPlusOneDayString = date("Y-m-d", strtotime($dateString . " +1 day"));
					$standByRow['standby_end_ts'] =  strtotime("$dayPlusOneDayString $endString UTC");
				}

				$this->employeeExtraHours[$day["employee_contract_id"]][$day["date"]][$i] = $day;
			}
		}
	}

	public function get($employeeContractId = "ALL", $dayToProcess = "ALL", $insideType = "ALL")
	{
		if ($employeeContractId === "ALL") {
			return isset($this->employeeExtraHours) ? $this->employeeExtraHours : null;
		}

		if ($dayToProcess === "ALL") {
			return isset($this->employeeExtraHours[$employeeContractId]) ? $this->employeeExtraHours[$employeeContractId] : null;
		}
		if ($insideType === "ALL") {
			return isset($this->employeeExtraHours[$employeeContractId][$dayToProcess]) ?
							$this->employeeExtraHours[$employeeContractId][$dayToProcess] : null;
		}
		return isset($this->employeeExtraHours[$employeeContractId][$dayToProcess]) ?
							$this->employeeExtraHours[$employeeContractId][$dayToProcess] : null;
	}

	public function getPlusMinusOneDay($employeeContractId = "ALL", $getDay = "ALL")
	{
		if ($employeeContractId === "ALL") {
			return isset($this->employeeExtraHours) ? $this->employeeExtraHours : null;
		}

		if ($getDay === "ALL") {
			return isset($this->employeeExtraHours[$employeeContractId]) ? $this->employeeExtraHours[$employeeContractId] : null;
		}
		$ret = [];
		for ($i = (-1); $i < 2; $i++) {
			$dayToProcess = date("Y-m-d", strtotime($getDay . "$i day"));

			if (isset($this->employeeExtraHours[$employeeContractId][$dayToProcess])) {
				$ret = Yang::arrayMerge($ret, $this->employeeExtraHours[$employeeContractId][$dayToProcess]);
			}
		}

		return $ret;
	}

	public function getSQL($startDate, $endDate, $employeeContracts = [], $skipCheckApprovers = \false, $approverProcessId = "workSchedule", $absence=false)
	{
		$draft     = Status::DRAFT;
		$published = Status::PUBLISHED;
		$locked    = status::LOCKED;
		$defEnd    = App::getSetting("defaultEnd");

		$insideOvertime = EmployeeExtraHours::INSIDE_OVERTIME;
		$insideStandBy  = EmployeeExtraHours::INSIDE_STANDBY;
		$insideDuty	    = EmployeeExtraHours::INSIDE_DUTY;
		$activeJoin     = "";

		$activeUnitId             = EmployeeGroup::getActiveGroupSQL("unit_id", "employee");
		$activeWorkgroupId        = EmployeeGroup::getActiveGroupSQL("workgroup_id", "employee_contract");
		$activeCompanyOrgGroup1Id = EmployeeGroup::getActiveGroupSQL("company_org_group1_id", "employee");
		$activeCompanyOrgGroup2Id = EmployeeGroup::getActiveGroupSQL("company_org_group2_id", "employee");
		$activeCompanyOrgGroup3Id = EmployeeGroup::getActiveGroupSQL("company_org_group3_id", "employee");

		if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
			$activeJoin .= EmployeeGroup::getLeftJoinSQL("unit_id", "employee_contract");
		}

		if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
			$activeJoin .= EmployeeGroup::getLeftJoinSQL("workgroup_id", "employee_contract");
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
			$activeJoin .= EmployeeGroup::getLeftJoinSQL("company_org_group1_id", "employee_contract");
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
			$activeJoin .= EmployeeGroup::getLeftJoinSQL("company_org_group2_id", "employee_contract");
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group3_id')) {
			$activeJoin .= EmployeeGroup::getLeftJoinSQL("company_org_group3_id", "employee_contract");
		}

		//	Összesítő kártyának nincs szüksége jogosultság kezelésre, mert dolgozók betöltésénél már megtörténik az ellenőrzés
		//	Benne hagytam, hogy ha máshol szükség lenne rá, egyszerűen bele lehessen tenni.
		//  GARG-al megjelölt kommentek

		$art = new ApproverRelatedGroup();
		$gargSQL = $art->getApproverReleatedGroupSQL("Employee", $approverProcessId);
		$gargJoin = "";
		if ($skipCheckApprovers === false && isset($gargSQL["join"])) {
			$gargJoin .= $gargSQL["join"];
		}

		$gargFilter = "";
		if ($skipCheckApprovers === false && isset($gargSQL["where"])) {
			$gargFilter .= $gargSQL["where"];
		}

		$employeeContractFilter = "";
		if (is_array($employeeContracts) && count($employeeContracts)) {
			$employeeContractFilter .= "
				AND employee_contract.`employee_contract_id` IN ('" . implode("','", $employeeContracts) . "')
			";
		} elseif ($employeeContracts !== 'ALL') {
			$employeeContractFilter .= "
				AND employee_contract.`employee_contract_id` = ''
			";
		}

		$SQL = "SELECT
				cal.`date`,
				employee.employee_id,
				employee_contract.employee_contract_id,

				wsr_ot.`row_id`																	as `overtime_row_id`,
				CONCAT(wsr_ot.`day`,' ',wsr_ot.`start`,':00')									as `overtime_start`,
				IF(wsr_ot.`start`<=wsr_ot.`end`,CONCAT(wsr_ot.`day`,' ',wsr_ot.`end`,':00'),
					CONCAT(wsr_ot.`day` + INTERVAL 1 DAY,' ',wsr_ot.`end`,':00'))				as `overtime_end`,
				wsr_ot.`status`																	as `overtime_status`,

				wsr_sb.`row_id`																	as `standby_row_id`,
				wsr_sb.`status`																	as `standby_status`,

				wsr_d.`row_id`																	as `duty_row_id`,
				CONCAT(wsr_d.`day`,' ',wsr_d.`start`,':00')										as `duty_start`,
				IF(wsr_d.`start`<=wsr_d.`end`,CONCAT(wsr_d.`day`,' ',wsr_d.`end`,':00'),
					CONCAT(wsr_d.`day` + INTERVAL 1 DAY,' ',wsr_d.`end`,':00'))					as `duty_end`,
				wsr_d.`status`																	as `duty_status`,

				wsr_ot.`start`																	AS `overtime_start`,
				wsr_ot.`end`																	AS `overtime_end`,
				wsr_sb.`start`																	AS `standby_start`,
				wsr_sb.`end`																	AS `standby_end`,
				wsr_d.`start`																	AS `duty_start`,
				wsr_d.`end`																		AS `duty_end`
			FROM `calendar` cal
			LEFT JOIN `employee` ON
					employee.`status` = $published 
				AND (cal.date BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, '$defEnd'))
			LEFT JOIN `employee_contract` ON
					employee_contract.`status` = $published AND employee_contract.`employee_id` = employee.`employee_id`
				AND cal.`date` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '$defEnd')
				AND cal.`date` BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '$defEnd')

			$activeJoin

			LEFT JOIN `company` ON
					`employee`.`company_id` = `company`.`company_id`
				AND	`company`.`status` =  $published	 
				AND cal.`date` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, '$defEnd')
			LEFT JOIN `unit` ON
					unit.`status` = $published
				AND unit.`unit_id` = $activeUnitId
				AND cal.`date` BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '$defEnd')
			LEFT JOIN `workgroup` ON
					workgroup.`workgroup_id` = $activeWorkgroupId
				AND workgroup.`status`= $published
				AND cal.`date` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '$defEnd')
			LEFT JOIN `company_org_group1` ON
					company_org_group1.`status`= $published
				AND company_org_group1.`company_org_group_id` = $activeCompanyOrgGroup1Id
				AND cal.`date` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '$defEnd')
			LEFT JOIN `company_org_group2` ON
					company_org_group2.`status`= $published
				AND company_org_group2.`company_org_group_id` = $activeCompanyOrgGroup1Id
				AND cal.`date` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '$defEnd')
			LEFT JOIN `company_org_group3` ON
					company_org_group3.`status`= $published
				AND company_org_group3.`company_org_group_id` = $activeCompanyOrgGroup3Id
				AND cal.`date` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '$defEnd')

			$gargJoin

			LEFT JOIN `employee_extra_hours` wsr_ot ON
					wsr_ot.employee_contract_id = employee_contract.`employee_contract_id`
				AND wsr_ot.`day` = cal.`date`
				AND wsr_ot.`inside_type` = '$insideOvertime'
				AND wsr_ot.`status` IN ($draft, $published, $locked)
			LEFT JOIN `employee_extra_hours` wsr_sb ON
					wsr_sb.employee_contract_id = employee_contract.`employee_contract_id`
				AND wsr_sb.`day` = cal.`date`
				AND wsr_sb.`inside_type` = '$insideStandBy'
				AND wsr_sb.`status` IN ($draft, $published, $locked)
			LEFT JOIN `employee_extra_hours` wsr_d ON
					wsr_d.employee_contract_id = employee_contract.`employee_contract_id`
				AND wsr_d.`day` = cal.`date`
				AND wsr_d.`inside_type` = '$insideDuty'
				AND wsr_d.`status` IN ($draft, $published, $locked)
			WHERE
					cal.`date` BETWEEN '$startDate' AND '$endDate'
				AND employee_contract.`row_id` IS NOT NULL
				AND (wsr_ot.row_id is not null OR wsr_sb.row_id is not null  OR wsr_d.row_id is not null)
				$gargFilter
				$employeeContractFilter
			ORDER BY employee_contract.employee_contract_id, cal.`date` ";

		return $SQL;
	}
}

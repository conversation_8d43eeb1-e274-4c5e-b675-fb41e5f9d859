<?php #yii2: done

'yii2-only`;

	namespace app\components\ahpCore\API;
	use app\components\App;
	use app\models\ApproverRelatedGroup;
	use app\models\Employee;
	use Yang;

`/yii2-only';


class GetEmployeeWorkScheduleUsed
{
	private $employeeSchedules;

	public function __construct($startDate, $endDate, $employeeContracts = array(), $skipCheckApprovers = false, $approverProcessId = "workSchedule") {
		$this->employeeSchedules = array();

		$defaultEnd = App::getSetting("defaultEnd");

		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQL("Employee", $approverProcessId);

		// check, that ttwa-wfm module is installed
		$checkModuleSQL = "
			SELECT `module` FROM `_sql_version` WHERE `module` = 'ttwa-wfm'
		";

		$checkModuleSQLResults = dbFetchAll($checkModuleSQL);

		if (!count($checkModuleSQLResults)) {

		} else {
			$SQL = "
				SELECT
					wsu.row_id,
					wsu.`day`,wsu.`employee_contract_id`,wsu.`dt_source`,
					wsu.`overtime_start`,wsu.`overtime_end`,wsu.`standby_start`,wsu.`standby_end`,
					wsu.`duty_start`,wsu.`duty_end`,wsu.`standby`,wsu.`duty`,
					wsu.`status` as wsu_status,
					dt.`daytype_id`,dt.`name` as daytype_name,
					dt.`type_of_daytype`, dt.`work_start`, dt.`work_end`,
					dt.`earliest_arrival_time`, dt.`latest_depart_time`,
					dt.`tolerance`, dt.`tolerance_after`, dt.`tolerance_before`,
					dt.`ordinary_time_start`, dt.`ordinary_time_end`,
					dt.`overtime_from_work_start`, dt.`work_end_from_overtime`, dt.`pre_day`,
					dt.`work_time_day_shift`, dt.`work_time_evening_shift`, dt.`work_time_night_shift`,
					dt.`break_time_from_1`, dt.`break_time_duration_1`, dt.`paid_break_time_1`,
					dt.`break_time_from_2`, dt.`break_time_duration_2`, dt.`paid_break_time_2`,
					dt.`break_time_from_3`, dt.`break_time_duration_3`, dt.`paid_break_time_3`,
					dt.`break_time_from_4`, dt.`break_time_duration_4`, dt.`paid_break_time_4`,
					dt.`break_time_from_5`, dt.`break_time_duration_5`, dt.`paid_break_time_5`,
					dt.`break_time_after_worktime`, dt.`break_time_after_worktime_from`,
					dt.`break_time_after_worktime_duration`,
					".Employee::getParam('fullname_with_emp_id_ec_id',array('employee','employee_contract'))."		AS fullname,
					DATE_FORMAT(wsu.`standby_start`,	'%H:%i') AS standby_start_hi,
					DATE_FORMAT(wsu.`standby_end`,		'%H:%i') AS standby_end_hi
				FROM
					`calendar` cal
				LEFT JOIN
					`employee` ON employee.`status` = 2 AND (cal.date BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, '$defaultEnd'))
				LEFT JOIN
					`employee_contract` ON employee_contract.`status` = 2 AND employee_contract.`employee_id` = employee.`employee_id`
						AND cal.`date` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '$defaultEnd')
						AND cal.`date` BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '$defaultEnd') ";

			if($skipCheckApprovers === false && isset($gargSQL["join"])){
				$SQL .= $gargSQL["join"];
			}

			$SQL .= "
				LEFT JOIN
					`work_schedule_used` wsu ON wsu.`employee_contract_id` = employee_contract.`employee_contract_id`
						AND wsu.`day` = cal.`date`
						AND wsu.`status` IN (2,5,6)
				LEFT JOIN daytype dt ON wsu.`daytype_id` = dt.`daytype_id`
						AND dt.`status` = 2
						AND wsu.`day` BETWEEN dt.`valid_from` AND IFNULL(dt.`valid_to`,'$defaultEnd')
				WHERE
					cal.`date` BETWEEN '".$startDate."' AND '".$endDate."'
					AND employee_contract.`row_id` IS NOT NULL
			";

			if ($skipCheckApprovers === false && isset($gargSQL["where"])){
					$SQL .= $gargSQL["where"];
			}

			if (count($employeeContracts)) {
				$SQL .= "
					AND employee_contract.`employee_contract_id` IN ('".implode("','",$employeeContracts)."')
				";
			} else if ($employeeContracts !== 'ALL'){
				$SQL .= "
					AND employee_contract.`employee_contract_id` = ''
				";
			}

			$workScheduleResults = dbFetchAll($SQL);

			foreach ($workScheduleResults as $day) {
				$this->employeeSchedules[$day["employee_contract_id"]][$day["day"]] = $day;
			}
		}
	}

	public function get($employeeContractId = "ALL", $dayToProcess = "ALL") {
		if ($employeeContractId === "ALL") {
			return isset($this->employeeSchedules)?$this->employeeSchedules:null;
		}

		if ($dayToProcess === "ALL") {
			return isset($this->employeeSchedules[$employeeContractId])?$this->employeeSchedules[$employeeContractId]:null;
		}
		return isset($this->employeeSchedules[$employeeContractId][$dayToProcess])?
							$this->employeeSchedules[$employeeContractId][$dayToProcess]:null;
	}
}

?>
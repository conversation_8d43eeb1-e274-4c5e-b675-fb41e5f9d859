<?php

class GetEmployeeWorkScheduleUsedDraft
{
	private $employeeSchedules;
	private $wsuStatus;
	private $defaultEnd;
	private $useStandbyAndDaytypeChangeRequest;
	private $otMgmtUpgradedVersion;
	private $otMgmtRestdayUseInterval;
	private $standbyUpgradedVersion;
	private $shiftStartInDaytype;

	public function __construct($startDate, $endDate, $employeeContracts = array(), $skipCheckApprovers = \false, $approverProcessId = "workSchedule", $wsuStatus = "2", $absence = false, $overtimeZero = false)
	{
		$this->employeeSchedules = [];

		$this->wsuStatus = $wsuStatus;

		$this->defaultEnd = App::getSetting("defaultEnd");
		$this->useStandbyAndDaytypeChangeRequest = App::getSetting("useStandbyAndDaytypeChangeRequest");
		$this->shiftStartInDaytype = ((int)App::getSetting('summarySheet_calculation_shiftStartInDaytype') === 1) ? \true : \false;

		$SQL = $this->getSQL($startDate, $endDate, $employeeContracts, $skipCheckApprovers, $approverProcessId, $absence, $overtimeZero);

		$workScheduleResults = dbFetchAll($SQL);

		foreach ($workScheduleResults as $sfd) {

			if ($this->otMgmtRestdayUseInterval && isset($sfd['overtime_rd_start'])) {

				$sfd['used_full_work_time'] = $sfd['overtime_time'];
                for ($i = 1; $i <= 5; $i++) {
                    if ((integer)$sfd["used_paid_break_time_$i"] === 0) {
                        $sfd['used_full_work_time'] -= (integer)$sfd["used_break_time_duration_$i"];
                    }
                }
			}
			$this->employeeSchedules[$sfd["employee_contract_id"]][$sfd["date"]] = $sfd;
		}

	}

	public function get($employeeContractId = "ALL", $dayToProcess = "ALL")
	{
		if ($employeeContractId === "ALL") {
			return isset($this->employeeSchedules) ? $this->employeeSchedules : null;
		}

		if ($dayToProcess === "ALL") {
			return isset($this->employeeSchedules[$employeeContractId]) ? $this->employeeSchedules[$employeeContractId] : null;
		}
		return isset($this->employeeSchedules[$employeeContractId][$dayToProcess]) ?
			$this->employeeSchedules[$employeeContractId][$dayToProcess] : null;
	}

	public function getWorkScheduleFromLastRestday($startDate, $endDate, $employeeContract, $skipCheckApprovers = \false, $approverProcessId = "workSchedule", $onlyFirstWorkday = \false)
	{

		$SQL = $this->getWorkScheduleFromLastRestdaySQL($startDate, $endDate, $employeeContract, $skipCheckApprovers, $approverProcessId, $onlyFirstWorkday);
		$workScheduleResults = dbFetchAll($SQL);

		$res = [];
		foreach ($workScheduleResults as $day) {
			$res[$day["employee_contract_id"]][$day["date"]] = $day;
		}
		return $res;
	}

	public function getWorkScheduleFromLastRestdaySQL($startDate, $endDate, $employeeContract, $skipCheckApprovers = \false, $approverProcessId = "workSchedule", $onlyFirstWorkday = \false)
	{
		$employeeContracts = [];
		array_push($employeeContracts, $employeeContract);

		$SQL = "
		SELECT 	@no,
				IF(a.used_type_of_daytype = 'RESTDAY',@no:=@no+1,@no),
				a.*
		FROM
		(
			";
		$SQL .= $this->getSQL($startDate, $endDate, $employeeContracts, $skipCheckApprovers, $approverProcessId);
		$SQL .= " DESC
		) a
		LEFT JOIN (SELECT @no := 0) v ON 1
		WHERE @no<1 ";
		if ($onlyFirstWorkday === \true) {
			$SQL .= "
			LIMIT 1,1";
		}
		return $SQL;
	}

	public function getSQL($startDate, $endDate, $employeeContracts = array(), $skipCheckApprovers = \false, $approverProcessId = "workSchedule", $absence = false, $overtimeZero = false)
	{

		$overtimeMode = empty(App::getSetting("overtime_mode")) ? "none" : mb_strtolower(App::getSetting("overtime_mode"), 'UTF-8');
		$this->otMgmtRestdayUseInterval = App::getSetting("otMgmtRestdayUseInterval");
		$this->otMgmtUpgradedVersion = App::getSetting("otMgmtUpgradedVersion");
		$this->standbyUpgradedVersion = App::getSetting("standbyUpgradedVersion");

		if ($this->standbyUpgradedVersion) {
			$operator = "<";
			$ifNull = "''";
		} else {
			$operator = "<=";
			$ifNull = "dt.`work_end`";
		}
		if (!$overtimeZero) {
			$overtimeSQL = "
				IFNULL(wsr_ot.`start`,IF(wsu.`overtime_start` IS NOT NULL, DATE_FORMAT(wsu.`overtime_start`,	'%H:%i'), dt.`work_end`))	AS overtime_start_hi,
				IFNULL(wsr_ot.`end`,IF(wsu.`overtime_end` IS NOT NULL, DATE_FORMAT(wsu.`overtime_end`,	'%H:%i'), dt.`work_end`))			AS overtime_end_hi,
			";
		} else {
			$overtimeSQL = "
				IFNULL(wsr_ot.`start`,IF(wsu.`overtime_start` IS NOT NULL AND DATE_FORMAT(wsu.`overtime_end`, '%H:%i') <> DATE_FORMAT(wsu.`overtime_start`,'%H:%i'), DATE_FORMAT(wsu.`overtime_start`,'%H:%i'), '00:00')) AS overtime_start_hi,
				IFNULL(wsr_ot.`end`,IF(wsu.`overtime_end` IS NOT NULL AND DATE_FORMAT(wsu.`overtime_end`, '%H:%i') <> DATE_FORMAT(wsu.`overtime_start`,'%H:%i'), DATE_FORMAT(wsu.`overtime_end`,'%H:%i'), '00:00'))	AS overtime_end_hi,
			";
		}

		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQL("Employee", $approverProcessId);

		$SQL = "
			SELECT
				cal.`date`,
				DAYNAME(cal.`date`) as dayname,
				employee.employee_id,
				employee_contract.employee_contract_id,
				employee_contract.daily_worktime										AS ec_daily_worktime,
				`unit`.`unit_id`,
				company_org_group1.`company_org_group_id` 								AS company_org_group1_id,
				company_org_group2.`company_org_group_id` 								AS company_org_group2_id,
				company_org_group3.`company_org_group_id` 								AS company_org_group3_id,
				`workgroup`.`workgroup_id`,
				`workgroup`.`work_type`													AS workgroup_work_type,
				ph.`row_id`																AS public_holiday_row_id,
				ph.`type`																AS public_holiday_type,
				ph.`name_dict_id`														AS public_holiday_name_dict_id,
				ph.`chdate`																AS public_holiday_chdate,
				wsu.`row_id`,
				wsu.`row_id`															AS used_row_id,
				wsu.`daytype_id`														AS used_daytype,
				
				unit.`unit_name`														AS unit_name,
				IF(wsu.`daytype_id` is not null, 'work_schedule_used',	null)			AS used_daytype_table_name,
				";
		if ($this->shiftStartInDaytype) {
			$SQL .= "
					shift_start_type.`dayShiftStart`,
					shift_start_type.`eveningShift1Start`,
					shift_start_type.`eveningShift2Start`,
					shift_start_type.`nightShiftStart`,
					";
		}

		$SQL .= "
				wsu.`daytype_id`        					AS used_daytype_id,
				dt.`name`									AS used_daytype_name,
				wsu.`type_of_daytype` 						AS used_type_of_daytype,
				wsu.`work_start` 							AS used_work_start,
				wsu.`work_end`								AS used_work_end,
				wsu.`pause_start` 							AS used_pause_start,
				wsu.`pause_end`								AS used_pause_end,
				wsu.`earliest_arrival_time`					AS used_earliest_arrival_time,
				wsu.`latest_depart_time`					AS used_latest_depart_time,
				wsu.`tolerance`								AS used_tolerance,
				wsu.`tolerance_before`						AS used_tolerance_before,
				wsu.`tolerance_after`						AS used_tolerance_after,
				wsu.`ordinary_time_start`					AS used_ordinary_time_start,
				wsu.`ordinary_time_end`						AS used_ordinary_time_end,
				wsu.`overtime_from_work_start`				AS used_overtime_from_work_start,
				wsu.`work_end_from_overtime`				AS used_work_end_from_overtime,
				wsu.`pre_day`								AS used_pre_day,
				wsu.`work_time_day_shift`					AS used_work_time_day_shift,
				wsu.`work_time_evening_shift`				AS used_work_time_evening_shift,
				wsu.`work_time_evening_shift_1`				AS used_work_time_evening_shift_1,
				wsu.`work_time_evening_shift_2`				AS used_work_time_evening_shift_2,
				wsu.`work_time_night_shift`					AS used_work_time_night_shift,
				wsu.`full_work_time`						AS used_full_work_time,
				wsu.`break_time_from_1`						AS used_break_time_from_1,
				wsu.`break_time_duration_1`					AS used_break_time_duration_1,
				wsu.`paid_break_time_1`						AS used_paid_break_time_1,
				wsu.`break_time_from_2`						AS used_break_time_from_2,
				wsu.`break_time_duration_2`					AS used_break_time_duration_2,
				wsu.`paid_break_time_2`						AS used_paid_break_time_2,
				wsu.`break_time_from_3`						AS used_break_time_from_3,
				wsu.`break_time_duration_3`					AS used_break_time_duration_3,
				wsu.`paid_break_time_3`						AS used_paid_break_time_3,
				wsu.`break_time_from_4`						AS used_break_time_from_4,
				wsu.`break_time_duration_4`					AS used_break_time_duration_4,
				wsu.`paid_break_time_4`						AS used_paid_break_time_4,
				wsu.`break_time_from_5`						AS used_break_time_from_5,
				wsu.`break_time_duration_5`					AS used_break_time_duration_5,
				wsu.`paid_break_time_5`						AS used_paid_break_time_5,
				wsu.`break_time_after_worktime`				AS used_break_time_after_worktime,
				wsu.`break_time_after_worktime_from`		AS used_break_time_after_worktime_from,
				wsu.`break_time_after_worktime_duration`	AS used_break_time_after_worktime_duration,
				wsu.`paid_break_time_after_worktime`		AS used_paid_break_time_after_worktime,
				wsu.`shift_start_type_id`					AS used_shift_start_type_id,
				wsu.`row_id` 								AS wsu_row_id,
				wsu.`status` 								AS wsu_status,
				

				IFNULL(wsr_ot.`row_id`,wsu.`row_id`) as `overtime_row_id`,
				IFNULL(CONCAT(wsr_ot.`day`,' ',wsr_ot.`start`,':00'),wsu.`overtime_start`) as `overtime_start`,
				IFNULL(IF(wsr_ot.`start`<=wsr_ot.`end`,CONCAT(wsr_ot.`day`,' ',wsr_ot.`end`,':00'),
						CONCAT(wsr_ot.`day` + INTERVAL 1 DAY,' ',wsr_ot.`end`,':00')),wsu.`overtime_end`) as `overtime_end`,
				IFNULL(wsr_ot.`status`,wsu.`status`) as `overtime_status`,

				IFNULL(wsr_sb.`row_id`,wsu.`row_id`) as `standby_row_id`,
				IFNULL(CONCAT(wsr_sb.`day`,' ',wsr_sb.`start`,':00'),wsu.`standby_start`) as `standby_start`,
				IFNULL(IF(wsr_sb.`start` {$operator} wsr_sb.`end`,CONCAT(wsr_sb.`day`,' ',wsr_sb.`end`,':00'),
						CONCAT(wsr_sb.`day` + INTERVAL 1 DAY,' ',wsr_sb.`end`,':00')),wsu.`standby_end`) as `standby_end`,
				IFNULL(wsr_sb.`status`,wsu.`status`) as `standby_status`,

				IFNULL(wsr_d.`row_id`,wsu.`row_id`) as `duty_row_id`,
				IFNULL(CONCAT(wsr_d.`day`,' ',wsr_d.`start`,':00'),wsu.`duty_start`) as `duty_start`,
				IFNULL(IF(wsr_d.`start`<=wsr_d.`end`,CONCAT(wsr_d.`day`,' ',wsr_d.`end`,':00'),
						CONCAT(wsr_d.`day` + INTERVAL 1 DAY,' ',wsr_d.`end`,':00')),wsu.`duty_end`) as `duty_end`,
				IFNULL(wsr_d.`status`,wsu.`status`) as `duty_status`,

				{$overtimeSQL}
				IFNULL(wsr_sb.`start`,IF(wsu.`standby_start` IS NOT NULL, DATE_FORMAT(wsu.`standby_start`,	'%H:%i'), {$ifNull}))		AS standby_start_hi,
				IFNULL(wsr_sb.`end`,IF(wsu.`standby_end` IS NOT NULL, DATE_FORMAT(wsu.`standby_end`,	'%H:%i'), {$ifNull}))			AS standby_end_hi,
				IFNULL(wsr_d.`start`,IF(wsu.`duty_start` IS NOT NULL, DATE_FORMAT(wsu.`duty_start`,	'%H:%i'), dt.`work_end`))		AS duty_start_hi,
				IFNULL(wsr_d.`end`,IF(wsu.`duty_end` IS NOT NULL, DATE_FORMAT(wsu.`duty_end`,	'%H:%i'), dt.`work_end`))			AS duty_end_hi,

				IF(rr.`row_id` IS NOT NULL, 1, 0) as existing_restday_request,

				dt.`increase_worktime_value`,
				dt.`increase_worktime_greater_than`,
				dt.`skip_lunchtime_after_worked_less_than`,
				dt.`bgcolor`,
				" . Employee::getParam('fullname_with_emp_id_ec_id', array('employee', 'employee_contract')) . "		AS fullname,
				" . Employee::getParam('fullname', 'employee') . " AS fullname_without_emp_id,
			";
		if ($overtimeMode === "paid") {

			$SQL .= "
				wso.type			as overtime_type,
				wso.time			as overtime_time,
				wso_draft.`type`	as draft_overtime_type,
				wso_draft.`time`	as draft_overtime_time,
				DATE_FORMAT(wso_draft.`restday_overtime_start`, '%H:%i') AS draft_overtime_rd_start,
				DATE_FORMAT(wso_draft.`restday_overtime_end`, '%H:%i') AS draft_overtime_rd_end,
			";
		}
		if ($this->otMgmtUpgradedVersion) {
			$SQL .= "
				wso.`break_between_wt_ot` AS overtime_break,
				wso_draft.`break_between_wt_ot` AS draft_overtime_break,
			";
		}
		if ($this->otMgmtRestdayUseInterval) {
			$SQL .= "
				DATE_FORMAT(wso.`restday_overtime_start`, '%H:%i') AS overtime_rd_start,
				DATE_FORMAT(wso.`restday_overtime_end`, '%H:%i') AS overtime_rd_end,
			";
		}
		$SQL .= "
				eeh_standby_draft.`process_id` as draft_standby_process_id,
				CONCAT(eeh_standby_draft.`day`, ' ', eeh_standby_draft.`start`, ':00') as `draft_standby_start`,
				IF(wsr_sb.`start` {$operator} wsr_sb.`end`,CONCAT(eeh_standby_draft.`day`, ' ', eeh_standby_draft.`end`, ':00'),
					CONCAT(eeh_standby_draft.`day` + INTERVAL 1 DAY,' ',eeh_standby_draft.`end`,':00')) as `draft_standby_end`,

				CASE
					WHEN wsu.`row_id`  IS NOT NULL THEN 'byUsed'
					ELSE 'None'
				END																			AS daytypeFrom,
				CASE
					WHEN employee_calc_used_daytype.`daytype_id`  IS NOT NULL THEN 'calc'
					WHEN wsu.`row_id`  IS NOT NULL THEN 'wsu'
					ELSE 'none'
				END																			AS usedScheduleFrom,
				`employee_calc_used_daytype`.`used_table_name`								AS employee_calc_used_table_name,
				`employee_calc_used_daytype`.`used_daytype_table_name` 						AS employee_calc_used_daytype_table_name,
				`employee_calc_used_daytype`.`daytype_id`									AS employee_calc_daytype_id,
				`employee_calc_used_daytype`.`reg_filter_from`								AS employee_calc_reg_filter_from,
				`employee_calc_used_daytype`.`reg_filter_to`								AS employee_calc_reg_filter_to,
				`employee_calc_used_daytype`.`status`										AS employee_calc_used_daytype_status

			";
		if ($absence) {
			$SQL .= ", d.`dict_value` as absence_name
				";
		}
		$SQL .= "FROM `calendar` cal
			LEFT JOIN `employee` ON
					employee.`status` = 2 AND (cal.date BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, '{$this->defaultEnd}'))
			LEFT JOIN `employee_contract` ON
					employee_contract.`status` = 2 AND employee_contract.`employee_id` = employee.`employee_id`
				AND cal.`date` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '{$this->defaultEnd}')
				AND cal.`date` BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee_calc_used_daytype` ON
					`employee_calc_used_daytype`.`employee_contract_id` = `employee_contract`.`employee_contract_id`
				AND `employee_calc_used_daytype`.`day` = cal.`date`
				AND `employee_calc_used_daytype`.`status` IN (" . Status::LOCKED . "," . Status::SAVED . ")
			";

		if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("unit_id", "employee_contract");
		}

		if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("workgroup_id", "employee_contract");
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("company_org_group1_id", "employee_contract");
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("company_org_group2_id", "employee_contract");
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group3_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("company_org_group3_id", "employee_contract");
		}

		$SQL .= "
			LEFT JOIN `company` ON
					`employee`.`company_id` = `company`.`company_id`
				AND	`company`.`status` = " . Status::PUBLISHED . "
				AND cal.`date` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `unit` ON
					unit.`status`=" . Status::PUBLISHED . "
				AND unit.`unit_id`=" . EmployeeGroup::getActiveGroupSQL("unit_id", "employee") . "
				AND cal.`date` BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `workgroup` ON
					workgroup.`workgroup_id` = " . EmployeeGroup::getActiveGroupSQL("workgroup_id", "employee_contract") . "
				AND workgroup.`status`=" . Status::PUBLISHED . "
				AND cal.`date` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `company_org_group1` ON
					company_org_group1.`status`=" . Status::PUBLISHED . "
				AND company_org_group1.`company_org_group_id`=" . EmployeeGroup::getActiveGroupSQL("company_org_group1_id", "employee") . "
				AND cal.`date` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `company_org_group2` ON
					company_org_group2.`status`=" . Status::PUBLISHED . "
				AND company_org_group2.`company_org_group_id`=" . EmployeeGroup::getActiveGroupSQL("company_org_group2_id", "employee") . "
				AND cal.`date` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `company_org_group3` ON
					company_org_group3.`status`=" . Status::PUBLISHED . "
				AND company_org_group3.`company_org_group_id`=" . EmployeeGroup::getActiveGroupSQL("company_org_group3_id", "employee") . "
				AND cal.`date` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '{$this->defaultEnd}')
		";
		if ($skipCheckApprovers === false && isset($gargSQL["join"])) {
			$SQL .= $gargSQL["join"];
		}

		$SQL .= "
			LEFT JOIN public_holiday ph ON
						cal.date = ph.holidaydate
					AND ph.country = `company`.`country`
			LEFT JOIN `work_schedule_used_draft` wsu ON
					wsu.`employee_contract_id` = employee_contract.`employee_contract_id`
				AND wsu.`day` = cal.`date`
				AND wsu.`status` IN ({$this->wsuStatus})
			LEFT JOIN `employee_extra_hours` eeh_standby_draft ON
					eeh_standby_draft.`employee_contract_id` = `employee_contract`.`employee_contract_id`
				AND eeh_standby_draft.`day` = cal.`date`
				AND eeh_standby_draft.`inside_type` = '" . EmployeeExtraHours::INSIDE_STANDBY . "'
				AND eeh_standby_draft.`status` = " . Status::DRAFT . "
			";

		if ($overtimeMode === "paid") {

			$SQL .= "
			LEFT JOIN `work_schedule_overtime` wso ON
					wso.`employee_contract_id` = employee_contract.`employee_contract_id`
				AND wso.`day` = cal.`date`
				AND wso.`status` = " . Status::PUBLISHED . "
			LEFT JOIN `work_schedule_overtime` wso_draft ON
					wso_draft.`employee_contract_id` = `employee_contract`.`employee_contract_id`
				AND wso_draft.`day` = cal.`date`
				AND wso_draft.`status` = " . Status::DRAFT . "

			";
		}
		$SQL .= "
			LEFT JOIN `daytype` dt ON
					(cal.`date` BETWEEN dt.`valid_from` AND IFNULL(dt.`valid_to`,'{$this->defaultEnd}'))
				AND dt.`status` = " . Status::PUBLISHED . "
				AND	dt.`daytype_id` =
										CASE
											WHEN wsu.`row_id` IS NOT NULL THEN wsu.`daytype_id`
											ELSE null
										END
			LEFT JOIN `employee_extra_hours` wsr_ot ON
					wsr_ot.employee_contract_id = employee_contract.`employee_contract_id`
				AND wsr_ot.`day` = cal.`date`
				AND wsr_ot.`inside_type`='" . EmployeeExtraHours::INSIDE_OVERTIME . "'
				AND wsr_ot.`status` IN (" . Status::DRAFT . "," . Status::PUBLISHED . "," . Status::LOCKED . ")
			LEFT JOIN `employee_extra_hours` wsr_sb ON
					wsr_sb.employee_contract_id = employee_contract.`employee_contract_id`
				AND wsr_sb.`day` = cal.`date`
				AND wsr_sb.`inside_type`='" . EmployeeExtraHours::INSIDE_STANDBY . "'
				AND wsr_sb.`status` IN (" . Status::DRAFT . "," . Status::PUBLISHED . "," . Status::LOCKED . ")
			LEFT JOIN `employee_extra_hours` wsr_d ON
					wsr_d.employee_contract_id = employee_contract.`employee_contract_id`
				AND wsr_d.`day` = cal.`date`
				AND wsr_d.`inside_type`='" . EmployeeExtraHours::INSIDE_DUTY . "'
				AND wsr_d.`status` IN (" . Status::DRAFT . "," . Status::PUBLISHED . "," . Status::LOCKED . ")
			LEFT JOIN `restday_request` rr ON
					`employee_contract`.`employee_contract_id` = rr.`employee_contract_id`
				AND cal.`date` = rr.`day`
				AND rr.`status` = " . Status::PUBLISHED . "
			";
		if ($this->shiftStartInDaytype) {
			$SQL .= "
					LEFT JOIN `shift_start_type` ON
							shift_start_type.`shift_start_type_id` = IFNULL(wsu.`shift_start_type_id`, dt.`shift_start_type_id`)
						AND shift_start_type.`status` = " . Status::PUBLISHED . "
						AND cal.`date` BETWEEN shift_start_type.`valid_from` AND IFNULL(shift_start_type.`valid_to`, '{$this->defaultEnd}')
				";
		}
		if ($absence) {
			$SQL .= "LEFT JOIN `employee_absence` ea ON
							ea.`employee_contract_id`=employee_contract.`employee_contract_id`
						AND ea.`day`=cal.`date`
						AND ea.`status` = " . Status::PUBLISHED . "
					LEFT JOIN `state_type` st ON
							st.`state_type_id`=ea.`state_type_id`
						AND st.`status` = " . Status::PUBLISHED . "
					LEFT JOIN dictionary d ON
							d.`dict_id`=st.`name_dict_id`
						AND d.`lang`='" . Dict::getLang() . "'
				";
		}
		$SQL .= "WHERE
					cal.`date` BETWEEN '" . $startDate . "' AND '" . $endDate . "'
				AND employee_contract.`row_id` IS NOT NULL
		";

		if ($skipCheckApprovers === false && isset($gargSQL["where"])) {
			$SQL .= $gargSQL["where"];
		}

		if (is_array($employeeContracts) && count($employeeContracts)) {
			$SQL .= "
				AND employee_contract.`employee_contract_id` IN ('" . implode("','", $employeeContracts) . "')
			";
		} else if ($employeeContracts !== 'ALL') {
			$SQL .= "
				AND employee_contract.`employee_contract_id` = ''
			";
		}
		$SQL .= "
			GROUP BY cal.`date`, `employee_contract`.`employee_contract_id`
			ORDER BY cal.`date`, fullname ";

		return $SQL;
	}

	public function convertTimeStringToSeconds($timeString, $dateString = "1970-01-01")
	{
		return strtotime("$dateString $timeString UTC");
	}

	public function getShiftStartType($employeeContractId, $dayToProcess)
	{
		if (!$this->shiftStartInDaytype) {
			return \false;
		}
		if ($employeeContractId === "ALL") {
			return isset($this->employeeSchedules) ? $this->employeeSchedules : null;
		}
		$row = $this->employeeSchedules[$employeeContractId][$dayToProcess];
		if (isset($row) and is_array($row)) {
			return [
				"day" => $row['dayShiftStart'],
				"evening1" => $row['eveningShift1Start'],
				"evening2" => $row['eveningShift2Start'],
				"night" => $row['nightShiftStart']
			];
		}
		return null;
	}
	public function getWSUColumns($getEmployeeContractId = "ALL", $getDayToProcess = "ALL")
	{
		if ($getEmployeeContractId === "ALL") {
			foreach ($this->employeeSchedules as $employeeContractId => $days) {
				foreach ($days as $day => $row) {
					$this->loadWSURow($employeeContractId, $day, $row, $wsuCols);
				}
			}
			return empty($wsuCols) ? null : $wsuCols;
		}

		if ($getDayToProcess === "ALL") {
			if (!isset($this->employeeSchedules[$getEmployeeContractId])) return null;
			foreach ($this->employeeSchedules[$getEmployeeContractId] as $day => $row) {
				$this->loadWSURow($getEmployeeContractId, $day, $row, $wsuCols);
			}
			return empty($wsuCols) ? null : $wsuCols;
		}

		if (!isset($this->employeeSchedules[$getEmployeeContractId][$getDayToProcess])) return null;
			$this->loadWSURow(  $getEmployeeContractId,
								$getDayToProcess,
								$this->employeeSchedules[$getEmployeeContractId][$getDayToProcess],
								$wsuCols
		);
		return empty($wsuCols) ? null : $wsuCols;
	}

	private function loadWSURow($employeeContractId, $day, $row, &$wsuCols)
	{
		$wsuCols =
		[
			$employeeContractId =>
			[
				$day =>
				[
					'employee_contract_id' => $employeeContractId,
					'day' => $day,
					'dt_source' => null,
					'daytype_id' => $row['used_daytype_id'],
					'daytype_name' => $row['used_daytype_name'],
					'type_of_daytype' => $row['used_type_of_daytype'],
					'distr_no' => null,
					'work_start' => $row['used_work_start'],
					'work_end' => $row['used_work_end'],
					'overtime_start' => $row['overtime_start'],
					'overtime_end' => $row['overtime_end'],
					'standby_start' => $row['standby_start'],
					'standby_end' => $row['standby_end'],
					'duty_start' => $row['duty_start'],
					'duty_end' => $row['duty_end'],
					'pause_start' => $row['used_pause_start'],
					'pause_end' => $row['used_pause_end'],
					'earliest_arrival_time' => $row['used_earliest_arrival_time'],
					'latest_depart_time' => $row['used_latest_depart_time'],
					'tolerance' => $row['used_tolerance'],
					'tolerance_before' => $row['used_tolerance_before'],
					'tolerance_after' => $row['used_tolerance_after'],
					'ordinary_time_start' => $row['used_ordinary_time_start'],
					'ordinary_time_end' => $row['used_ordinary_time_end'],
					'overtime_from_work_start' => $row['used_overtime_from_work_start'],
					'work_end_from_overtime' => $row['used_work_end_from_overtime'],
					'pre_day' => $row['used_pre_day'],
					'work_time_day_shift' => $row['used_work_time_day_shift'],
					'work_time_evening_shift' => $row['used_work_time_evening_shift'],
					'work_time_evening_shift_1' => $row['used_work_time_evening_shift_1'],
					'work_time_evening_shift_2' => $row['used_work_time_evening_shift_2'],
					'work_time_night_shift' => $row['used_work_time_night_shift'],
					'full_work_time' => $row['used_full_work_time'],
					'break_time_from_1' => $row['used_break_time_from_1'],
					'break_time_duration_1' => $row['used_break_time_duration_1'],
					'paid_break_time_1' => $row['used_paid_break_time_1'],
					'break_time_from_2' => $row['used_break_time_from_2'],
					'break_time_duration_2' => $row['used_break_time_duration_2'],
					'paid_break_time_2' => $row['used_paid_break_time_2'],
					'break_time_from_3' => $row['used_break_time_from_3'],
					'break_time_duration_3' => $row['used_break_time_duration_3'],
					'paid_break_time_3' => $row['used_paid_break_time_3'],
					'break_time_from_4' => $row['used_break_time_from_4'],
					'break_time_duration_4' => $row['used_break_time_duration_4'],
					'paid_break_time_4' => $row['used_paid_break_time_4'],
					'break_time_from_5' => $row['used_break_time_from_5'],
					'break_time_duration_5' => $row['used_break_time_duration_5'],
					'paid_break_time_5' => $row['used_paid_break_time_5'],
					'break_time_after_worktime' => $row['used_break_time_after_worktime'],
					'break_time_after_worktime_from' => $row['used_break_time_after_worktime_from'],
					'break_time_after_worktime_duration' => $row['used_break_time_after_worktime_duration'],
					'paid_break_time_after_worktime' => $row['used_paid_break_time_after_worktime'],
					'standby' => null,
					'duty' => null,
					'process_id' => null,
					'shift_start_type_id' => $row['used_shift_start_type_id'],
				],
			],
		];
	}
}

?>

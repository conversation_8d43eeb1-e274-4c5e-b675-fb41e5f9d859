<?php #yii2: done

'yii2-only`;

	namespace app\components\ahpCore\API;
	use DateTime;
	use app\components\App;
	use app\components\Dict;
	use app\components\ahpCore\API\AHPSaveMethods;
	use app\models\Status;
	use yii\web\Response;
	use Yang;

`/yii2-only';


class AHPAbsenceForm
{
	public static function getForm($filename,$jasperright,$wsPlannerMode = false) {
		$pub = Status::PUBLISHED;
		$absenceTypeSelect= '';
		$user_rolegroup = Yang::getUserRoleGroup();

		$current_lang = Dict::getLang();

		$minMaxDate = '';

		$controllerId =  substr(Yang::requestUri(), 0, strpos(Yang::requestUri(), "/"));

		if(!App::hasRight($controllerId, "absence_all_days_editable")){
			$absLimit = AHPSaveMethods::getAbsenceLimits(null);
			$minDate = date_diff(new DateTime(date('Y-m-d')), $absLimit->min_date)->days-1;
			$maxDate = date_diff(new DateTime(date('Y-m-d')), $absLimit->max_date)->days-1;
			if($minDate < 0) $minDate = 0;
			if($maxDate < 0) $maxDate = 0;

			$minMaxDate = ', minDate:-'.$minDate.', maxDate:'.$maxDate;
		}

		if (!$wsPlannerMode) {
			$stateTypeDefaultValue = App::getSetting("stateTypeDefaultValue");
			$stateTypeSql = '';
			$stateTypeSelect = '';
			if($stateTypeDefaultValue)
			{
						$stateTypeSql = "
						LEFT JOIN absence_type_default_value atdv ON
								  at.`state_type_id` = atdv.state_type_id
							 AND atdv.auth_rolegroup_id = '$user_rolegroup'
						  	 AND  atdv.status = " . $pub;
						$stateTypeSelect = ",atdv.`default` as atdv_default";
			}
			$SQL = "SELECT at.`state_type_id`, IFNULL(di.`dict_value`,at.name_dict_id) AS name, at.`default` AS 'default'
						, IFNULL(at.absence_hour, 99) as abshour
						$stateTypeSelect
						FROM `state_type` at
						LEFT JOIN `dictionary` di
						ON di.dict_id=at.name_dict_id AND di.lang='".$current_lang."' AND di.valid=1 AND (di.module='ttwa-ahp' OR di.module='ttwa-ahp-core')
						LEFT JOIN `state_type_right` atr
						ON atr.`state_type_id` = at.`state_type_id` AND atr.`rolegroup_id` = '$user_rolegroup'
						LEFT JOIN link_at_to_bat latb ON latb.state_type_id = at.`state_type_id`
						LEFT JOIN base_absence_type bat ON bat.base_absence_type_id =latb.base_absence_type_id
						$stateTypeSql
						WHERE
							at.`in_use` = 1 AND at.`status` = " . $pub;

			if (!App::getRight(null,"su")/* && !App::getRight($this->getControllerId(),"approval")*/) {
				$SQL .= " AND atr.`row_id` IS NOT NULL";
			}


			$having = '';
			if(App::getSetting("absence_calculation_hour") != '1'){
				$having = 'HAVING abshour = 99';

			}

			$SQL .= " GROUP BY at.`state_type_id` ".$having." ORDER BY at.`order`, di.`dict_value`;";

			$dataReader = dbFetchAll($SQL);

			foreach ($dataReader as $absenceType) {
				$absenceTypeSelect.= '<option value="' . $absenceType['state_type_id'] . '" ' . ($stateTypeDefaultValue ? ($absenceType['atdv_default'] ? 'selected="selected"' : '' ) : ($absenceType['default'] ? 'selected="selected"' : '')) . '>' . $absenceType['name'] . '</option>';
			}
		} else {
			$absenceTypeSelect.= '<option value="">'.Dict::getValue("absence_day_type_workday").'</option>';
			$absenceTypeSelect.= '<option value="">'.Dict::getValue("absence_type_restday").'</option>';
			$absenceTypeSelect.= '<option value="">'.Dict::getValue("absence_type_freeday").'</option>';
		}

		$form = '<form id="absenceForm" style="padding:10px;" method="post" action="saveAbsences">
			<table>
				<tr>
					<td style="padding-bottom:10px;">
						<table style="width:100%;">
							<tr>
								<td style="width:50%;">
								    <input id="absenceSaveType" name="absenceSaveType" type="hidden" value="section" />
    								<input id="absenceDates" name="absenceDates" type="hidden" value="" />						
									<input style="width:75px;background-image:none;" id="absenceBegin" name="absenceBegin" type="text" value="" /> '.Dict::getValue("absenceplanneryearview_from").'
									<script type="text/javascript">
										$(function() {
											$("#absenceBegin").datepicker({dateFormat:"yy-mm-dd"'.$minMaxDate.'});
										});
									</script>
								</td>
								<td style="width:50%;">
									<input style="width:75px;background-image:none;" name="absenceEnd" id="absenceEnd" type="text" value="" /> '.Dict::getValue("absenceplanneryearview_to").'
									<script type="text/javascript">
										$(function() {
											$("#absenceEnd").datepicker({dateFormat:"yy-mm-dd"'.$minMaxDate.'});
										});
									</script>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td style="padding-bottom:10px;">
						<div class="styled-select" style="width:225px;">
							<select id="absType" name="absType" style="width:253px;">';
								$form .= $absenceTypeSelect;
							$form .= '</select>
						</div>
					</td>
				</tr>
				<tr>
					<td class="delegacy_travel_time" style="padding-bottom:10px;display:none;">
						<div style="width:225px;">
							'.Dict::getValue("delegacy_travel_time").': <input style="width:75px;background-image:none;" name="delegacyTime" id="delegacyTime" type="text" value="" />
							<script type="text/javascript">
								$(function() {
									$("#delegacyTime").timepicker({
										timeOnlyTitle: "'.Dict::getValue('time').'",
										timeText: "'.Dict::getValue('time').'",
										hourText: "'.Dict::getValue('hour').'",
										minuteText: "'.Dict::getValue('minute').'",
										secondText: "sec",
										currentText: "'.Dict::getValue('now').'",
										closeText: "'.Dict::getValue('done').'"
									}, {timeFormat:"hh:mm"});
								});
							</script>
						</div>
					</td>
				</tr>
				<tr>
					<td class="absredempt_interval" style="padding-bottom:10px;display:none;">
						<div style="width:225px;" class="styled-select">
							<select style="width:225px;" name="absredemptInterval" id="absredemptInterval">
								<option value="full" selected="selected">' . Dict::getValue("fullDayAbsence") . '</option>
								<option value="1">' . Dict::getValue("interval") . ': 1 ' . Dict::getValue("hours") . '</option>
								<option value="2">' . Dict::getValue("interval") . ': 2 ' . Dict::getValue("hours") . '</option>
								<option value="3">' . Dict::getValue("interval") . ': 3 ' . Dict::getValue("hours") . '</option>
								<option value="4">' . Dict::getValue("interval") . ': 4 ' . Dict::getValue("hours") . '</option>
								<option value="5">' . Dict::getValue("interval") . ': 5 ' . Dict::getValue("hours") . '</option>
								<option value="6">' . Dict::getValue("interval") . ': 6 ' . Dict::getValue("hours") . '</option>
								<option value="7">' . Dict::getValue("interval") . ': 7 ' . Dict::getValue("hours") . '</option>
								<option value="8">' . Dict::getValue("interval") . ': 8 ' . Dict::getValue("hours") . '</option>
								<option value="9">' . Dict::getValue("interval") . ': 9 ' . Dict::getValue("hours") . '</option>
								<option value="10">' . Dict::getValue("interval") . ': 10 ' . Dict::getValue("hours") . '</option>
								<option value="11">' . Dict::getValue("interval") . ': 10 ' . Dict::getValue("hours") . '</option>
								<option value="12">' . Dict::getValue("interval") . ': 12 ' . Dict::getValue("hours") . '</option>
							</select>
						</div>
					</td>
				</tr>
				<tr>
					<td style="padding-bottom:10px;">
						<textarea id="note" name="note" rows="2" cols="50" style="width:218px;background:rgba(255,255,255,1);text-align:left;border:solid 1px #e5e5e6;">'.Dict::getModuleValue("ttwa-base","note").'</textarea>
						<script type="text/javascript">
							var noteFilled = false;
							$("#note").click(function(){
								if (!noteFilled) {
									$("#note").val("");
									noteFilled = true;
								}
							});

							$("#absType").change(function() {
								var state_type_id = $(this).val();
								var has_travel_time = [];
								var found = false;

								$.ajax({
									type: "POST",
									url: "./getStateTypes",
									data: "",
									async: false,
									dataType: "json",
									success: function(resp) {
										has_travel_time = resp.has_travel_time;

										for (var id in has_travel_time) {
											if (has_travel_time[id] === state_type_id) {
												found = true;
											}
										}
									},
									error: function() {

									}
								});

								if (found) {
									$(".delegacy_travel_time").css("display","inline");
								} else {
									$(".delegacy_travel_time").css("display","none");
								}
								if (state_type_id === "absence_type_absredempt" && getAppSetting("absredemptAbsUseInterval") == "1") {
									$(".absredempt_interval").css("display","table-cell");
								} else {
									$(".absredempt_interval").css("display","none");
								}
							});
						</script>
					</td>
				</tr>
				<tr>
					<td style="width:100%;text-align:center;">
						<script type="text/javascript">
							<!--
								var dictArray = [];

								dictArray["sending_of_notification_emails_are_in_progress"] = "'.Dict::getModuleValue("ttwa-base",'sending_of_notification_emails_are_in_progress').'";
								dictArray["notification_emails_sent"]						= "'.Dict::getModuleValue("ttwa-base",'notification_emails_sent').'";
								dictArray["error_we_can_save_absences_working_days_only"]	= "'.Dict::getModuleValue("ttwa-ahp",'error_we_can_save_absences_working_days_only').'";
								dictArray["are_you_sure_to_revoke_absences"]				= "'.Dict::getModuleValue("ttwa-base",'are_you_sure_to_revoke_absences').'";
								var sendTo = null;
								var reportHost="'.Yang::getParam('reportHost').'";
								var filename="'.$filename.'";
							-->
						</script>';

						$messaging = (int)App::getSetting("messagingEnabled") === 1;
						$emailSending = (int)App::getSetting("emailSending") === 1;

						$form .= '<input id="saveAPGrid" onclick="saveForm(sendTo,dictArray,'.($messaging?1:0).','.($emailSending?1:0).',reportHost,filename,'.($jasperright?'true':'false').')" name="saveAPGrid" type="button" value="" class="addmodDialogSave buttonDisabled" disabled="disabled" />
						<input id="deleteAPGrid" onclick="deleteForm(sendTo,dictArray,'.($messaging?1:0).','.($emailSending?1:0).')" name="deleteAPGrid" type="button" value="" class="addmodDialogDelItem buttonDisabled" disabled="disabled" style="margin-left:10px;" />
					</td>
				</tr>
			</table>
		</form>';

		return $form;
	}
}

?>
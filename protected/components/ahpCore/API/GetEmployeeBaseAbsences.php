<?php
class GetEmployeeBaseAbsences
{
    public function get($year, $employeeContracts =[])
	{
		$baseAbsenceTypesCondition = implode("', '", array_keys($this->getLinkAtToBat()));
		/**
		 * Dolgozók alapszabadságai
		 */
		$baseAbsenceSql = "
				SELECT
					`employee_contract_id`,
                    `quantity` AS quantity,
					`quantity_hour` AS quantity_hour,
					base_absence_type_id
				FROM `employee_base_absence`
				WHERE `status` = " . Status::PUBLISHED . "
					AND `valid_from` BETWEEN '{$year}-01-01' AND '{$year}-12-31'
					AND base_absence_type_id IN ('{$baseAbsenceTypesCondition}')
					AND `employee_contract_id` IN ('" . implode("', '", $employeeContracts) . "')
				";
		$res = dbFetchAll($baseAbsenceSql);
		$ret=[];
		for ($i=0;$i<count($res); $i++)
		{
			$ret[$res[$i]['employee_contract_id']]['employee_contract_id'] = $res[$i]['employee_contract_id'];
			isset($ret[$res[$i]['employee_contract_id']]['quantity']) ?
				$ret[$res[$i]['employee_contract_id']]['quantity'] += $res[$i]['quantity']
				: $ret[$res[$i]['employee_contract_id']]['quantity'] = $res[$i]['quantity'];
			isset($ret[$res[$i]['employee_contract_id']]['quantity_hour']) ?
				$ret[$res[$i]['employee_contract_id']]['quantity_hour'] += $res[$i]['quantity_hour']
				: $ret[$res[$i]['employee_contract_id']]['quantity_hour'] = $res[$i]['quantity_hour'];

		}
		return $ret;
	}

	protected function getLinkAtToBat()
	{
        $absencesString = str_replace(";", "','", App::getSetting('absenceCondition'));
		$sql = "
			SELECT
				l.state_type_id,
				l.base_absence_type_id,
				bat.dict_id
			FROM base_absence_type bat
			LEFT JOIN link_at_to_bat l ON
					l.base_absence_type_id = bat.base_absence_type_id
			WHERE bat.status = ".Status::PUBLISHED."
		";
		if (!empty($absencesString))
		{
			$sql .= " AND l.state_type_id IN ('{$absencesString}')";
		}
		$baseAbsenceCondition = App::getSetting('baseAbsenceCondition');
		if (!empty($baseAbsenceCondition))
		{
			$baseAbsencesString = str_replace(";", "', '", $baseAbsenceCondition);
			$sql .= " AND l.base_absence_type_id IN ('{$baseAbsencesString}')";
		}
		$sql .= " ORDER BY bat.priority";
		$res = dbFetchAll($sql);
		for ($i=0; $i<count($res); $i++)
		{
			$ret[$res[$i]['base_absence_type_id']][] = $res[$i];
		}
		return $ret;
	}
}

?>
<?php

class GetEmployeeAbsences
{
    public const WORKTIME = 'worktime';
    public const ABSENCE_HOUR = 'absence_hour';
    public const FULL_DAY_ABSENCE = 'full_day_absence';

    public function get(
        $startDate,
        $endDate,
        $employeeContracts = [],
        $status = '1,2,3,4',
        $onlyAbsenceDays = false,
        $stateTypeFilter = 'ALL'
    ) {
        $worktimeFieldName = self::WORKTIME;
        $absenceHourFieldName = self::ABSENCE_HOUR;
        $fullDayAbsenceFieldName = self::FULL_DAY_ABSENCE;
        $hasWfmModule = in_array('ttwa-wfm', Yang::getParam('modules'));
        $SQL = "
			SELECT
				e.employee_id,
				ec.employee_contract_id,
				c.date,
				ea.state_type_id,
				ea.`status`,
				ea.`note`,
				at.bgcolor,
				at.name_dict_id,
				IF(pcce.`max` >= c.date,1,0) AS locked,
				ea.`score`,
				ea.`absence_hour` as $absenceHourFieldName,
				ea.`full_day_absence` as $fullDayAbsenceFieldName,
				ea.employee_absence_id,
				ea.row_id,
				at.worktime as $worktimeFieldName,
                at.type AS state_type
				";
        if ($hasWfmModule) {
            $SQL .= ',
					wsu.`standby_start`,
					wsu.`standby_end`';
        }
        $SQL .= '
			FROM `calendar` c
			LEFT JOIN `employee` e ON
					e.`status` = ' . Status::PUBLISHED . "
				AND (c.date BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '" . App::getSetting('defaultEnd') . "'))
			LEFT JOIN `employee_contract` ec ON
					ec.`status` = " . Status::PUBLISHED . "
				AND ec.`employee_id` = e.`employee_id`
				AND c.`date` BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '" . App::getSetting('defaultEnd') . "')
				AND c.`date` BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '" . App::getSetting(
                'defaultEnd'
            ) . "')
			LEFT JOIN `employee_absence` ea ON
					ea.`employee_contract_id` = ec.`employee_contract_id`
				AND ea.`day` = c.`date`
				AND ea.`status` IN ($status)
			LEFT JOIN `state_type` at ON
					ea.`state_type_id` = at.`state_type_id`
				AND at.`status`= " . Status::PUBLISHED;
        if ($hasWfmModule) {
            $SQL .= '
				LEFT JOIN `work_schedule_used` wsu ON
					wsu.`day` = c.`date`
				AND wsu.`status` IN (' . Status::PUBLISHED . ', ' . Status::STATUS_SAVED . ', ' . Status::LOCKED . ')
				AND	wsu.`employee_contract_id` = ec.`employee_contract_id`';
        }
        $SQL .= "
			LEFT JOIN
				(
						SELECT `employee_id`, MAX(`locked_on`) as max
						FROM `payroll_calc_correction_export`
						GROUP BY `employee_id`
				) pcce ON
					pcce.`employee_id` = e.`employee_id`
			WHERE
					c.`date` BETWEEN '" . $startDate . "' AND '" . $endDate . "'
				AND (ea.state_type_id = '{$stateTypeFilter}' OR 'ALL' = '{$stateTypeFilter}')
				AND ec.`row_id` IS NOT NULL
		";

        if (count($employeeContracts)) {
            $SQL .= "
				AND ec.`employee_contract_id` IN ('" . implode("','", $employeeContracts) . "')
			";
        }

        if ($onlyAbsenceDays) {
            $SQL .= '
				AND ea.`row_id` IS NOT NULL
			';
        }

        $absenceResults = dbFetchAll($SQL);

        $employee_absences = [];
        $prevEcId = null;
        $prevDate = null;
        $prevStatus = null;

        foreach ($absenceResults as $absence) {
            //Több lehet különböző státuszú távollét is lehet egy napon, ez így mindig kisebb státuszút nézi a
            // enableGetAbsIfIssetRejectedAbs app_settings miatt
            if ($absence['employee_contract_id'] !== $prevEcId || $absence['date'] !== $prevDate || is_null(
                    $absence['status']
                ) || is_null($prevStatus)
                || $absence['status'] < $prevStatus) {
                $employee_absences[$absence['employee_contract_id']][$absence['date']] = $absence;
            }

            $prevEcId = $absence['employee_contract_id'];
            $prevDate = $absence['date'];
            $prevStatus = $absence['status'];
        }

        return $employee_absences;
    }
}
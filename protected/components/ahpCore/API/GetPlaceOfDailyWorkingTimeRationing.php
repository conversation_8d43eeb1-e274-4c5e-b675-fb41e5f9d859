<?php

declare(strict_types=1);

//namespace Components\ahpCore\API;

final class GetPlaceOfDailyWorkingTimeRationing
{
    private string $placeOfDailyWorkingTimeRationingTableName;

    public function __construct()
    {
        $placeOfDailyWorkingTimeRationing = strtolower(App::getSetting( "placeOfDailyWorkingTimeRationing")??'');

        $this->placeOfDailyWorkingTimeRationingTableName =
            (!in_array($placeOfDailyWorkingTimeRationing, ['contract', 'workgroup']) ||
                $placeOfDailyWorkingTimeRationing === 'contract') ?
                'employee_contract':
                'workgroup';
    }

    public function getPlaceOfDailyWorkingTimeRationingTableName()
    {
        return $this->placeOfDailyWorkingTimeRationingTableName;
    }

    public function getPlaceOfDailyWorkingTimeRationingTableWithColumn()
    {
        return $this->placeOfDailyWorkingTimeRationingTableName.".`daily_worktime`";
    }


}
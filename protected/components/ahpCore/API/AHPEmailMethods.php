<?php

'yii2-only`;

	namespace app\components\ahpCore\API;
	use app\components\Messaging;
	use app\components\ahpCore\API\AHPAbsenceFunctions;
	use app\components\ahpCore\API\AHPEmployeeFunctions;
	use app\models\Employee;
	use Yang;

`/yii2-only';


class AHPEmailMethods
{
	private $sendTo = "employee";
	private $employee_contract_id = null;
	private $employee_absence_id = null;
	private $employee_absence_row_ids = [];
	private $note = "";
	private $message_type = null;
	
	public function __construct($employee_contract_id, $employee_absence_id, $employee_absence_row_ids, $message_type, $note, $sendTo) {
		if (!empty($sendTo) && in_array($sendTo,array("approvers","employee"))) {
			$this->sendTo = $sendTo;
		}
		
		if (!empty($employee_contract_id)) {
			$this->employee_contract_id = $employee_contract_id;
		}
		
		$this->employee_absence_id = $employee_absence_id;
		
		$this->employee_absence_row_ids = $employee_absence_row_ids; /* only on delete! */
		
		$this->message_type = $message_type; /* add / delete */
		
		$this->note = $note;
	}
	
	public function sendAHPEmail() {
		if (!empty($this->employee_contract_id) && !empty($this->sendTo) && in_array($this->sendTo,array("approvers","employee")) /*&& !empty($this->employee_absence_id)*/) {
			if ($this->sendTo === "approvers") {
				$this->sendToApprovers($this->employee_contract_id, $this->employee_absence_id, $this->employee_absence_row_ids, $this->note, $this->message_type);
			} else if ($this->sendTo === "employee") {
				$this->sendToEmployee($this->employee_contract_id, $this->employee_absence_id, $this->employee_absence_row_ids, $this->note, $this->message_type);
			}
		}
	}

	private function sendToEmployee($employee_contract_id, $employee_absence_id, $employee_absence_row_ids, $note, $message_type, $status=null, $absenceDay=null) {
		$users = AHPEmployeeFunctions::getUsersByEcID($employee_contract_id);
		
		if ($message_type === "add") {
			$absenceData = AHPAbsenceFunctions::getAbsenceDataByID($employee_absence_id, $status, $absenceDay);

			if (count($users) && count($absenceData)) {
				foreach ($users as $user) {
					$email = null;

					if (isset($user->email) && !empty($user->email)) {
						$email = $user->email;
					}

					if (!empty($email)) {
						$messaging = new Messaging('ahp/absenceapproval', 'EmployeeAbsence', $employee_absence_id, $absenceData);
						$messaging->sendMessage(userID(), [$user->user_id], $absenceData, $message_type);
					}
				}
			}
		} else if ($message_type === "delete") {
			$absenceData = AHPAbsenceFunctions::getAbsenceDataByPK($employee_absence_row_ids);
			
			if(!isset($absenceData["begin"]) && isset($absenceData[0])){
				$absenceData = $absenceData[0];
			}
			
			$appr_fullname = Employee::getEmployeeFullnameByEcID(Yang::getUserECID());

			if (count($users) && count($absenceData)) {
				foreach ($users as $user) {
					$email = null;

					if (isset($user->email) && !empty($user->email)) {
						$email = $user->email;
					}

					if (!empty($email)) {
						$messaging = new Messaging('ahp/absenceapproval', 'EmployeeAbsence', $employee_absence_id, $absenceData);
						$messaging->sendMessage(userID(), [$user->user_id], $absenceData, "del");
					}
				}
			}
		}
	}

	private function sendToApprovers($employee_contract_id, $employee_absence_id, $employee_absence_row_ids, $note, $message_type, $status=null, $absenceDay=null) {
		//$fullname = Employee::getEmployeeFullnameByEcID($employee_contract_id);
		
		if ($message_type === "add") {
			$absenceData = AHPAbsenceFunctions::getAbsenceDataByID($employee_absence_id, $status, $absenceDay);

			if (count($absenceData)) {
				foreach (AHPEmployeeFunctions::getApproverUsersByEcID($employee_contract_id) as $user) {
					$email = null;

					if (isset($user["email"]) && !empty($user["email"])) {
						$email = $user["email"];
					}

					if (!empty($email)) {
						$approverFullname = $user["username"];

						if (isset($user["employee_id"]) && !empty($user["employee_id"])) {
							$approverFullname = Employee::getEmployeeFullnameByID($user["employee_id"]);
						}

						$messaging = new Messaging('ahp/absenceapproval', 'EmployeeAbsence', $employee_absence_id, $absenceData);
						$messaging->sendMessage(userID(), [$user['user_id']], $absenceData, 'req_add');
					}
				}
			}
		} else if ($message_type === "delete") {
			$absenceData = AHPAbsenceFunctions::getAbsenceDataByPK($employee_absence_row_ids);

			if (count($absenceData)) {
				$absenceData = $absenceData[0];
				foreach (AHPEmployeeFunctions::getApproverUsersByEcID($employee_contract_id) as $user) {
					$email = null;

					if (isset($user["email"]) && !empty($user["email"])) {
						$email = $user["email"];
					}

					if (!empty($email)) {
						$approverFullname = $user["username"];

						if (isset($user["employee_id"]) && !empty($user["employee_id"])) {
							$approverFullname = Employee::getEmployeeFullnameByID($user["employee_id"]);
						}

						$subject_type = ($absenceData["status"] === '4') ? "req_del" : $subject_type = "del"; // 4 törlésre jelölt, 5 törölt
						
						$messaging = new Messaging('ahp/absenceapproval', 'EmployeeAbsence', $employee_absence_id, $absenceData);
						$messaging->sendMessage(userID(), [$user['user_id']], $absenceData, $subject_type);
					}
				}
			}
		}
	}
}

?>